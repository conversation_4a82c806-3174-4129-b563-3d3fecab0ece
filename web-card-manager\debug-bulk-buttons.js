// Debug script for bulk add buttons
console.log('🔍 Starting bulk button debug...');

// Wait for DOM to be ready
function debugBulkButtons() {
    console.log('🔍 Debugging bulk add buttons...');
    
    // Check if buttons exist
    const bulkAddCardsBtn = document.getElementById('bulk-add-cards-btn');
    const bulkAddEmailsBtn = document.getElementById('bulk-add-emails-btn');
    
    console.log('🔍 Bulk add cards button:', bulkAddCardsBtn);
    console.log('🔍 Bulk add emails button:', bulkAddEmailsBtn);
    
    if (bulkAddCardsBtn) {
        console.log('✅ Cards button found');
        console.log('🔍 Cards button classes:', bulkAddCardsBtn.className);
        console.log('🔍 Cards button style:', bulkAddCardsBtn.style.cssText);
        console.log('🔍 Cards button disabled:', bulkAddCardsBtn.disabled);
        console.log('🔍 Cards button event listeners:', getEventListeners ? getEventListeners(bulkAddCardsBtn) : 'getEventListeners not available');
        
        // Test click manually
        console.log('🔍 Testing manual click on cards button...');
        bulkAddCardsBtn.click();
    } else {
        console.error('❌ Cards button not found');
    }
    
    if (bulkAddEmailsBtn) {
        console.log('✅ Emails button found');
        console.log('🔍 Emails button classes:', bulkAddEmailsBtn.className);
        console.log('🔍 Emails button style:', bulkAddEmailsBtn.style.cssText);
        console.log('🔍 Emails button disabled:', bulkAddEmailsBtn.disabled);
        console.log('🔍 Emails button event listeners:', getEventListeners ? getEventListeners(bulkAddEmailsBtn) : 'getEventListeners not available');
        
        // Test click manually
        console.log('🔍 Testing manual click on emails button...');
        bulkAddEmailsBtn.click();
    } else {
        console.error('❌ Emails button not found');
    }
    
    // Check if CardManagerApp is available
    console.log('🔍 CardManagerApp available:', typeof window.CardManagerApp);
    if (window.CardManagerApp) {
        console.log('🔍 CardManagerApp instance:', window.CardManagerApp);
        console.log('🔍 UIManager available:', !!window.CardManagerApp.uiManager);
        console.log('🔍 ModalManager available:', !!window.CardManagerApp.modalManager);
        
        if (window.CardManagerApp.uiManager) {
            console.log('🔍 UIManager showBulkAddModal method:', typeof window.CardManagerApp.uiManager.showBulkAddModal);
        }
        
        if (window.CardManagerApp.modalManager) {
            console.log('🔍 ModalManager showBulkAddModal method:', typeof window.CardManagerApp.modalManager.showBulkAddModal);
        }
    }
    
    // Check modal overlay
    const modalOverlay = document.getElementById('modal-overlay');
    console.log('🔍 Modal overlay:', modalOverlay);
    if (modalOverlay) {
        console.log('🔍 Modal overlay classes:', modalOverlay.className);
        console.log('🔍 Modal overlay style:', modalOverlay.style.cssText);
        console.log('🔍 Modal overlay children:', modalOverlay.children.length);
    }
}

// Run debug when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', debugBulkButtons);
} else {
    debugBulkButtons();
}

// Also run after a delay to ensure everything is initialized
setTimeout(debugBulkButtons, 2000);
