"""
Search and filtering functionality for the Card Manager application.

Provides advanced search, filtering, and sorting capabilities for cards, emails, and combinations.
"""
import re
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from enum import Enum

class SearchType(Enum):
    """Types of search operations."""
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    EXACT_MATCH = "exact_match"
    REGEX = "regex"

class SortOrder(Enum):
    """Sort order options."""
    ASC = "ascending"
    DESC = "descending"

@dataclass
class SearchFilter:
    """Search filter configuration."""
    query: str
    search_type: SearchType = SearchType.CONTAINS
    case_sensitive: bool = False
    include_used: bool = True
    include_archived: bool = True
    email_types: Optional[List[str]] = None  # For email filtering

@dataclass
class SearchResult:
    """Search result with highlighting information."""
    item: str
    item_type: str  # 'card', 'email', 'combination'
    matches: List[Tuple[int, int]]  # List of (start, end) positions for highlighting
    metadata: Dict[str, Any] = None  # Additional metadata (e.g., email type, usage status)

class SearchManager:
    """Manages search and filtering operations."""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.last_search_results = []
        self.search_history = []
        self.max_history = 20
    
    def search_cards(self, search_filter: SearchFilter) -> List[SearchResult]:
        """Search through cards with the given filter."""
        results = []
        
        # Get all cards based on filter settings
        all_cards = set(self.data_manager.cards)
        
        if not search_filter.include_used:
            all_cards -= self.data_manager.all_used_cards
        
        if not search_filter.include_archived:
            all_cards -= self.data_manager.archived_cards
        
        # Perform search
        for card in all_cards:
            matches = self._find_matches(card, search_filter)
            if matches:
                # Determine card status
                status = "available"
                if card in self.data_manager.all_used_cards:
                    status = "used"
                if card in self.data_manager.archived_cards:
                    status = "archived"
                if card in self.data_manager.used_cards:
                    status = "in_use"
                
                metadata = {
                    'status': status,
                    'is_currently_used': card in self.data_manager.used_cards,
                    'is_archived': card in self.data_manager.archived_cards
                }
                
                results.append(SearchResult(
                    item=card,
                    item_type="card",
                    matches=matches,
                    metadata=metadata
                ))
        
        return results
    
    def search_emails(self, search_filter: SearchFilter) -> List[SearchResult]:
        """Search through emails with the given filter."""
        results = []
        
        # Get email lists based on filter
        email_sources = []
        if search_filter.email_types is None:
            # Search all email types
            email_sources = [
                (self.data_manager.pm_emails, "PM emails 30 off 35"),
                (self.data_manager.ue_emails_25_25, "UE emails 25 off 25"),
                (self.data_manager.ue_emails_25_15, "UE emails 25 off 15"),
                (self.data_manager.ue_emails_25_1, "UE emails 25 off 1"),
                (self.data_manager.emails, "General emails")
            ]
        else:
            # Search specific email types
            type_mapping = {
                "pm_emails": (self.data_manager.pm_emails, "PM emails 30 off 35"),
                "ue_emails_25_25": (self.data_manager.ue_emails_25_25, "UE emails 25 off 25"),
                "ue_emails_25_15": (self.data_manager.ue_emails_25_15, "UE emails 25 off 15"),
                "ue_emails_25_1": (self.data_manager.ue_emails_25_1, "UE emails 25 off 1"),
                "emails": (self.data_manager.emails, "General emails")
            }
            email_sources = [type_mapping[t] for t in search_filter.email_types if t in type_mapping]
        
        # Search through each email source
        for email_list, email_type in email_sources:
            for email in email_list:
                # Apply filters
                if not search_filter.include_used and email in self.data_manager.all_used_emails:
                    continue
                if not search_filter.include_archived and email in self.data_manager.archived_emails:
                    continue
                
                matches = self._find_matches(email, search_filter)
                if matches:
                    # Determine email status
                    status = "available"
                    if email in self.data_manager.all_used_emails:
                        status = "used"
                    if email in self.data_manager.archived_emails:
                        status = "archived"
                    if email in self.data_manager.used_emails:
                        status = "in_use"
                    
                    metadata = {
                        'status': status,
                        'email_type': email_type,
                        'is_currently_used': email in self.data_manager.used_emails,
                        'is_archived': email in self.data_manager.archived_emails
                    }
                    
                    results.append(SearchResult(
                        item=email,
                        item_type="email",
                        matches=matches,
                        metadata=metadata
                    ))
        
        return results
    
    def search_combinations(self, search_filter: SearchFilter) -> List[SearchResult]:
        """Search through combinations with the given filter."""
        results = []
        
        combinations = self.data_manager.get_combinations()
        
        for i, combo in enumerate(combinations):
            # Search in formatted combination text
            search_text = combo.get('formatted', '')
            matches = self._find_matches(search_text, search_filter)
            
            if matches:
                metadata = {
                    'index': i,
                    'card': combo.get('card', ''),
                    'email': combo.get('email', ''),
                    'email_type': combo.get('email_type', 'Unknown'),
                    'timestamp': combo.get('timestamp', '')
                }
                
                results.append(SearchResult(
                    item=search_text,
                    item_type="combination",
                    matches=matches,
                    metadata=metadata
                ))
        
        return results
    
    def _find_matches(self, text: str, search_filter: SearchFilter) -> List[Tuple[int, int]]:
        """Find all matches in text based on search filter."""
        if not search_filter.query:
            return []
        
        query = search_filter.query
        target_text = text if search_filter.case_sensitive else text.lower()
        search_query = query if search_filter.case_sensitive else query.lower()
        
        matches = []
        
        try:
            if search_filter.search_type == SearchType.CONTAINS:
                start = 0
                while True:
                    pos = target_text.find(search_query, start)
                    if pos == -1:
                        break
                    matches.append((pos, pos + len(search_query)))
                    start = pos + 1
            
            elif search_filter.search_type == SearchType.STARTS_WITH:
                if target_text.startswith(search_query):
                    matches.append((0, len(search_query)))
            
            elif search_filter.search_type == SearchType.ENDS_WITH:
                if target_text.endswith(search_query):
                    start_pos = len(target_text) - len(search_query)
                    matches.append((start_pos, len(target_text)))
            
            elif search_filter.search_type == SearchType.EXACT_MATCH:
                if target_text == search_query:
                    matches.append((0, len(text)))
            
            elif search_filter.search_type == SearchType.REGEX:
                flags = 0 if search_filter.case_sensitive else re.IGNORECASE
                pattern = re.compile(query, flags)
                for match in pattern.finditer(text):
                    matches.append((match.start(), match.end()))
        
        except re.error:
            # Invalid regex pattern
            pass
        
        return matches
    
    def search_all(self, search_filter: SearchFilter) -> Dict[str, List[SearchResult]]:
        """Search across all data types."""
        results = {
            'cards': self.search_cards(search_filter),
            'emails': self.search_emails(search_filter),
            'combinations': self.search_combinations(search_filter)
        }
        
        # Add to search history
        self._add_to_history(search_filter.query)
        
        # Cache results
        self.last_search_results = results
        
        return results
    
    def sort_results(self, results: List[SearchResult], 
                    sort_by: str = "item", 
                    order: SortOrder = SortOrder.ASC) -> List[SearchResult]:
        """Sort search results."""
        reverse = (order == SortOrder.DESC)
        
        if sort_by == "item":
            return sorted(results, key=lambda x: x.item.lower(), reverse=reverse)
        elif sort_by == "type":
            return sorted(results, key=lambda x: x.item_type, reverse=reverse)
        elif sort_by == "status" and results and results[0].metadata:
            return sorted(results, key=lambda x: x.metadata.get('status', ''), reverse=reverse)
        else:
            return results
    
    def filter_by_status(self, results: List[SearchResult], 
                        statuses: List[str]) -> List[SearchResult]:
        """Filter results by status."""
        return [r for r in results 
                if r.metadata and r.metadata.get('status') in statuses]
    
    def get_search_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """Get search suggestions based on partial query and history."""
        suggestions = []
        
        # Add from search history
        for query in self.search_history:
            if partial_query.lower() in query.lower() and query not in suggestions:
                suggestions.append(query)
                if len(suggestions) >= limit:
                    break
        
        # Add common patterns if we have space
        if len(suggestions) < limit:
            common_patterns = [
                "@gmail.com", "@yahoo.com", "@hotmail.com",
                "4111", "4222", "5555", "3333"  # Common test card prefixes
            ]
            
            for pattern in common_patterns:
                if partial_query.lower() in pattern.lower() and pattern not in suggestions:
                    suggestions.append(pattern)
                    if len(suggestions) >= limit:
                        break
        
        return suggestions[:limit]
    
    def _add_to_history(self, query: str):
        """Add query to search history."""
        if query and query not in self.search_history:
            self.search_history.insert(0, query)
            # Keep only recent searches
            self.search_history = self.search_history[:self.max_history]
    
    def clear_history(self):
        """Clear search history."""
        self.search_history.clear()
    
    def get_history(self) -> List[str]:
        """Get search history."""
        return self.search_history.copy()
    
    def highlight_text(self, text: str, matches: List[Tuple[int, int]], 
                      highlight_start: str = "<mark>", 
                      highlight_end: str = "</mark>") -> str:
        """Add highlighting markup to text based on matches."""
        if not matches:
            return text
        
        # Sort matches by start position
        sorted_matches = sorted(matches, key=lambda x: x[0])
        
        result = ""
        last_end = 0
        
        for start, end in sorted_matches:
            # Add text before match
            result += text[last_end:start]
            # Add highlighted match
            result += highlight_start + text[start:end] + highlight_end
            last_end = end
        
        # Add remaining text
        result += text[last_end:]
        
        return result
