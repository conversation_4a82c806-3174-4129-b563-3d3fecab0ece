<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Bulk Buttons - Card Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .debug-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Bulk Button Debug Tool</h1>
        
        <div class="debug-section">
            <h2>Test Actions</h2>
            <button onclick="loadMainApp()">Load Main App</button>
            <button onclick="testBulkButtons()">Test Bulk Buttons</button>
            <button onclick="clearConsole()">Clear Console</button>
            <button onclick="window.location.href='index.html'">Go to Main App</button>
        </div>
        
        <div class="debug-section">
            <h2>Console Output</h2>
            <div id="console-output"></div>
        </div>
        
        <!-- Hidden iframe to load main app -->
        <iframe id="main-app-frame" src="index.html" style="display: none;"></iframe>
    </div>

    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('WARN', ...args);
        };
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        function loadMainApp() {
            console.log('🔄 Loading main app...');
            const frame = document.getElementById('main-app-frame');
            frame.src = 'index.html?' + Date.now(); // Force reload
        }
        
        function testBulkButtons() {
            console.log('🧪 Testing bulk buttons...');
            
            // Try to access the main app from iframe
            const frame = document.getElementById('main-app-frame');
            try {
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                const frameWindow = frame.contentWindow;
                
                console.log('🔍 Frame document:', frameDoc);
                console.log('🔍 Frame window:', frameWindow);
                
                if (frameWindow && frameWindow.CardManagerApp) {
                    console.log('✅ Found CardManagerApp in frame');
                    
                    // Test bulk buttons
                    const bulkCardsBtn = frameDoc.getElementById('bulk-add-cards-btn');
                    const bulkEmailsBtn = frameDoc.getElementById('bulk-add-emails-btn');
                    
                    console.log('🔍 Bulk cards button:', bulkCardsBtn);
                    console.log('🔍 Bulk emails button:', bulkEmailsBtn);
                    
                    if (bulkCardsBtn) {
                        console.log('🖱️ Clicking bulk cards button...');
                        bulkCardsBtn.click();
                    }
                    
                    if (bulkEmailsBtn) {
                        console.log('🖱️ Clicking bulk emails button...');
                        bulkEmailsBtn.click();
                    }
                } else {
                    console.warn('❌ CardManagerApp not found in frame');
                }
            } catch (error) {
                console.error('❌ Error accessing frame:', error);
                console.log('🔄 Trying direct window access...');
                
                // Try direct window access
                if (window.CardManagerApp) {
                    console.log('✅ Found CardManagerApp in main window');
                } else {
                    console.warn('❌ CardManagerApp not found in main window');
                }
            }
        }
        
        // Load debug script
        console.log('🚀 Debug tool loaded');
    </script>
    
    <!-- Load the debug script -->
    <script src="debug-bulk-buttons.js"></script>
</body>
</html>
