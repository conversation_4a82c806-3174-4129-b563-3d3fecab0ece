<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Card Manager</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 10000;
        }
        .test-btn {
            display: block;
            margin: 10px 0;
            padding: 10px 15px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #005a99;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .status.success {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
        }
        .status.error {
            background: rgba(255, 0, 0, 0.2);
            color: #ff4444;
        }
    </style>
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Loading Card Manager...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <header class="toolbar">
                <div class="toolbar-left">
                    <h1 class="app-title">
                        <i class="fas fa-credit-card"></i>
                        Card Manager - Simple Test
                    </h1>
                </div>
            </header>
            
            <main class="main-content">
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="cards">
                        <i class="fas fa-credit-card"></i>
                        Cards
                        <span class="tab-count" id="cards-count">0</span>
                    </button>
                </div>
                
                <div class="tab-panel active" id="cards-tab">
                    <div class="tab-content">
                        <section class="input-section">
                            <h3>Add Cards</h3>
                            <div class="input-group">
                                <input 
                                    type="text" 
                                    id="card-input" 
                                    class="form-input" 
                                    placeholder="Enter card: number,month,year,cvv,zip"
                                    autocomplete="off"
                                >
                                <button id="add-card-btn" class="primary-btn">
                                    <i class="fas fa-plus"></i>
                                    Add Card
                                </button>
                            </div>
                        </section>
                        
                        <section class="list-section">
                            <h3>Available Cards</h3>
                            <div class="list-container">
                                <div class="list" id="available-cards-list">
                                    <p>No cards added yet.</p>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </main>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Test Controls -->
    <div class="test-controls">
        <h3>🧪 Simple Tests</h3>
        <button class="test-btn" onclick="testDOMElements()">Test DOM Elements</button>
        <button class="test-btn" onclick="testShowApp()">Test Show App</button>
        <button class="test-btn" onclick="testModuleImport()">Test Module Import</button>
        <button class="test-btn" onclick="testFullInit()">Test Full Init</button>
        <div id="test-status"></div>
    </div>

    <script>
        const statusDiv = document.getElementById('test-status');
        
        function addStatus(message, type = 'success') {
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            statusDiv.appendChild(status);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearStatus() {
            statusDiv.innerHTML = '';
        }

        window.testDOMElements = function() {
            clearStatus();
            addStatus('Testing DOM elements...', 'info');
            
            const elements = [
                'loading-screen',
                'main-app', 
                'card-input',
                'add-card-btn',
                'available-cards-list',
                'cards-count'
            ];
            
            let allFound = true;
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    addStatus(`✅ Found: ${id}`, 'success');
                } else {
                    addStatus(`❌ Missing: ${id}`, 'error');
                    allFound = false;
                }
            });
            
            if (allFound) {
                addStatus('🎉 All DOM elements found!', 'success');
            }
        };

        window.testShowApp = function() {
            clearStatus();
            addStatus('Testing show application...', 'info');
            
            const loadingScreen = document.getElementById('loading-screen');
            const mainApp = document.getElementById('main-app');
            
            if (!loadingScreen || !mainApp) {
                addStatus('❌ Required elements not found', 'error');
                return;
            }
            
            addStatus('Hiding loading screen...', 'info');
            loadingScreen.classList.add('hidden');
            
            setTimeout(() => {
                addStatus('Showing main app...', 'info');
                mainApp.classList.remove('hidden');
                mainApp.classList.add('fade-in');
                addStatus('✅ App shown successfully!', 'success');
            }, 300);
        };

        window.testModuleImport = async function() {
            clearStatus();
            addStatus('Testing module imports...', 'info');
            
            try {
                addStatus('Importing CardManagerApp...', 'info');
                const { CardManagerApp } = await import('./js/main.js');
                addStatus('✅ CardManagerApp imported', 'success');
                
                addStatus('Creating instance...', 'info');
                const app = new CardManagerApp();
                addStatus('✅ Instance created', 'success');
                
            } catch (error) {
                addStatus(`❌ Import failed: ${error.message}`, 'error');
                console.error('Import error:', error);
            }
        };

        window.testFullInit = async function() {
            clearStatus();
            addStatus('Testing full initialization...', 'info');
            
            try {
                const { CardManagerApp } = await import('./js/main.js');
                const app = new CardManagerApp();
                
                addStatus('Starting initialization...', 'info');
                await app.init();
                addStatus('🎉 Full initialization completed!', 'success');
                
            } catch (error) {
                addStatus(`❌ Initialization failed: ${error.message}`, 'error');
                console.error('Initialization error:', error);
                console.error('Stack:', error.stack);
            }
        };

        // Auto-run DOM test on load
        document.addEventListener('DOMContentLoaded', () => {
            addStatus('Page loaded, running DOM test...', 'info');
            testDOMElements();
        });
    </script>
</body>
</html>
