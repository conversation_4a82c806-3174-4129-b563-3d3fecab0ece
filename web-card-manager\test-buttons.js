/**
 * Button Test Script
 * Run this in the browser console to test button functionality
 */

console.log('🧪 Starting button functionality test...');

// Test if buttons exist
const buttonsToTest = [
    'bulk-add-cards-btn',
    'bulk-add-emails-btn', 
    'settings-btn',
    'import-btn',
    'export-btn',
    'undo-btn',
    'redo-btn'
];

console.log('📋 Testing button existence...');
buttonsToTest.forEach(id => {
    const btn = document.getElementById(id);
    if (btn) {
        console.log(`✅ ${id}: Found`);
        console.log(`   - Tag: ${btn.tagName}`);
        console.log(`   - Classes: ${btn.className}`);
        console.log(`   - Disabled: ${btn.disabled}`);
        console.log(`   - Style display: ${btn.style.display}`);
        console.log(`   - Computed display: ${getComputedStyle(btn).display}`);
        console.log(`   - Visible: ${btn.offsetWidth > 0 && btn.offsetHeight > 0}`);
    } else {
        console.log(`❌ ${id}: Not found`);
    }
});

// Test if Card Manager App is available
console.log('📋 Testing Card Manager App...');
if (window.cardManagerApp) {
    console.log('✅ Card Manager App found');
    console.log('   - Initialized:', window.cardManagerApp.isInitialized);
    console.log('   - Managers:', Object.keys(window.cardManagerApp.managers));
    
    // Test UI Manager
    if (window.cardManagerApp.managers.ui) {
        console.log('✅ UI Manager found');
        console.log('   - Modal Manager:', !!window.cardManagerApp.managers.ui.modalManager);
    } else {
        console.log('❌ UI Manager not found');
    }
} else {
    console.log('❌ Card Manager App not found');
}

// Test clicking buttons programmatically
console.log('📋 Testing button clicks...');

function testButtonClick(buttonId) {
    const btn = document.getElementById(buttonId);
    if (btn) {
        console.log(`🖱️ Testing click on ${buttonId}...`);
        try {
            btn.click();
            console.log(`✅ ${buttonId} click executed`);
        } catch (error) {
            console.error(`❌ ${buttonId} click failed:`, error);
        }
    } else {
        console.log(`❌ ${buttonId} not found for click test`);
    }
}

// Test bulk add buttons
setTimeout(() => {
    console.log('🧪 Testing bulk add buttons...');
    testButtonClick('bulk-add-cards-btn');
    
    setTimeout(() => {
        testButtonClick('bulk-add-emails-btn');
        
        setTimeout(() => {
            testButtonClick('settings-btn');
        }, 1000);
    }, 1000);
}, 2000);

console.log('🧪 Button test script loaded. Tests will run automatically.');
