"""
Utility functions for the Card Manager application.

Includes functions for validation, formatting, and bulk entry parsing.
"""
import re

def validate_email(email):
    """Validate email format."""
    # More permissive email validation to allow for various formats
    return bool(email and '@' in email and '.' in email)

def validate_card(card):
    """Validate card number format.
    Validates card format with comma-separated values.
    """
    # Check if it's not empty and has the expected format
    return bool(card and card.strip())

def format_combination(card, email, format_type="default"):
    """Format the combination of card and email.
    Different format types can be added as needed.
    """
    # Always use the comma format as requested
    return f"{card},{email}"

def parse_bulk_cards(text):
    """Parse bulk card entries from text."""
    cards = []
    for line in text.strip().split('\n'):
        if line.strip():
            cards.append(line.strip())
    return cards

def parse_bulk_emails(text):
    """Parse bulk email entries from text."""
    emails = []
    for line in text.strip().split('\n'):
        if line.strip() and '@' in line:
            emails.append(line.strip())
    return emails
