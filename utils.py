"""
Utility functions for the Card Manager application.

Includes functions for validation, formatting, and bulk entry parsing.
"""
import re
from typing import List, Optional, Tuple, Dict, Any
import csv

def validate_email(email: str) -> bool:
    """Validate email format with comprehensive checks."""
    if not email or not isinstance(email, str):
        return False

    email = email.strip()
    if not email:
        return False

    # Enhanced email validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

    # Basic checks
    if '@' not in email or '.' not in email:
        return False

    # Check for multiple @ symbols
    if email.count('@') != 1:
        return False

    # Split into local and domain parts
    try:
        local, domain = email.split('@')
        if not local or not domain:
            return False

        # Check domain has at least one dot
        if '.' not in domain:
            return False

        # Check for valid characters and length
        if len(local) > 64 or len(domain) > 255:
            return False

        return bool(re.match(email_pattern, email))
    except ValueError:
        return False

def validate_card(card: str) -> Tuple[bool, Optional[str]]:
    """Validate card format with detailed error reporting.

    Expected format: card_number,month,year,cvv,zip
    Returns: (is_valid, error_message)
    """
    if not card or not isinstance(card, str):
        return False, "Card data is empty or invalid"

    card = card.strip()
    if not card:
        return False, "Card data is empty"

    # Split by comma
    parts = card.split(',')
    if len(parts) != 5:
        return False, f"Card must have exactly 5 comma-separated fields, got {len(parts)}"

    card_number, month, year, cvv, zip_code = [part.strip() for part in parts]

    # Validate card number (should be 13-19 digits)
    if not card_number.isdigit():
        return False, "Card number must contain only digits"
    if len(card_number) < 13 or len(card_number) > 19:
        return False, f"Card number must be 13-19 digits, got {len(card_number)}"

    # Validate month (01-12)
    if not month.isdigit() or len(month) != 2:
        return False, "Month must be 2 digits (01-12)"
    month_int = int(month)
    if month_int < 1 or month_int > 12:
        return False, f"Month must be between 01-12, got {month}"

    # Validate year (2-digit or 4-digit)
    if not year.isdigit():
        return False, "Year must contain only digits"
    if len(year) == 2:
        year_int = int(year)
        if year_int < 0 or year_int > 99:
            return False, f"2-digit year must be 00-99, got {year}"
    elif len(year) == 4:
        year_int = int(year)
        if year_int < 2000 or year_int > 2099:
            return False, f"4-digit year must be 2000-2099, got {year}"
    else:
        return False, f"Year must be 2 or 4 digits, got {len(year)} digits"

    # Validate CVV (3-4 digits)
    if not cvv.isdigit():
        return False, "CVV must contain only digits"
    if len(cvv) < 3 or len(cvv) > 4:
        return False, f"CVV must be 3-4 digits, got {len(cvv)}"

    # Validate ZIP code (5 digits for US format)
    if not zip_code.isdigit():
        return False, "ZIP code must contain only digits"
    if len(zip_code) != 5:
        return False, f"ZIP code must be 5 digits, got {len(zip_code)}"

    return True, None

def format_combination(card: str, email: str, format_type: str = "default") -> str:
    """Format the combination of card and email.
    Different format types can be added as needed.
    """
    if format_type == "wool":
        # Format for Wool: card_number,month/year,cvv,zip,email
        parts = card.split(',')
        if len(parts) >= 5:
            return f"{parts[0]},{parts[1]}/{parts[2]},{parts[3]},{parts[4]},{email}"
    elif format_type == "fusion":
        # Format for Fusion: card_number,month,year,cvv,zip,email
        return f"{card},{email}"

    # Default comma format
    return f"{card},{email}"

def parse_bulk_cards(text: str) -> List[str]:
    """Parse bulk card entries from text with validation."""
    cards = []
    lines = text.strip().split('\n')

    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue

        # Validate each card
        is_valid, error = validate_card(line)
        if is_valid:
            cards.append(line)
        else:
            print(f"Warning: Invalid card on line {i}: {error}")

    return cards

def parse_bulk_emails(text: str) -> List[str]:
    """Parse bulk email entries from text with validation."""
    emails = []
    lines = text.strip().split('\n')

    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue

        if validate_email(line):
            emails.append(line)
        else:
            print(f"Warning: Invalid email on line {i}: {line}")

    return emails

def export_to_csv(data: List[Dict[str, Any]], filename: str) -> bool:
    """Export data to CSV file."""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            if not data:
                return True

            fieldnames = data[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        return True
    except Exception as e:
        print(f"Error exporting to CSV: {e}")
        return False

def import_from_csv(filename: str) -> Tuple[bool, List[Dict[str, Any]], Optional[str]]:
    """Import data from CSV file.

    Returns: (success, data, error_message)
    """
    try:
        data = []
        with open(filename, 'r', encoding='utf-8') as csvfile:
            # Try to detect delimiter
            sample = csvfile.read(1024)
            csvfile.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter

            reader = csv.DictReader(csvfile, delimiter=delimiter)
            for row in reader:
                data.append(dict(row))

        return True, data, None
    except Exception as e:
        return False, [], str(e)

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # Limit length
    if len(filename) > 255:
        filename = filename[:255]

    return filename.strip()

def format_card_display(card: str) -> str:
    """Format card for display with masked number."""
    parts = card.split(',')
    if len(parts) >= 1:
        card_number = parts[0]
        if len(card_number) >= 4:
            masked = '*' * (len(card_number) - 4) + card_number[-4:]
            return f"{masked},{','.join(parts[1:])}" if len(parts) > 1 else masked
    return card
