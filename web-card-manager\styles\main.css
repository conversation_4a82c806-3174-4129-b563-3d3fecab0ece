/* Main CSS - Core styles and layout */

:root {
    /* Dark Theme Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --bg-hover: #4d4d4d;
    --bg-active: #5d5d5d;
    
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --text-disabled: #666666;
    
    --border-primary: #404040;
    --border-secondary: #606060;
    --border-focus: #0078d4;
    
    --accent-primary: #0078d4;
    --accent-hover: #106ebe;
    --accent-active: #005a9e;
    
    --success: #107c10;
    --success-hover: #0e6e0e;
    --warning: #ff8c00;
    --warning-hover: #e67c00;
    --danger: #d13438;
    --danger-hover: #b92b2f;
    
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
    --transition-slow: 0.35s ease;
    
    /* Z-index layers */
    --z-dropdown: 1000;
    --z-modal: 2000;
    --z-toast: 3000;
    --z-loading: 4000;
}

/* Light Theme Override */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-tertiary: #e5e5e5;
    --bg-hover: #d5d5d5;
    --bg-active: #c5c5c5;
    
    --text-primary: #000000;
    --text-secondary: #4d4d4d;
    --text-muted: #808080;
    --text-disabled: #999999;
    
    --border-primary: #d0d0d0;
    --border-secondary: #a0a0a0;
    
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.2);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* App Layout */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main-app.hidden {
    display: none;
}

/* Toolbar */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.toolbar-left {
    display: flex;
    align-items: center;
    min-width: 200px;
}

.app-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.app-title i {
    color: var(--accent-primary);
}

.toolbar-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 var(--spacing-lg);
}

.search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.search-icon {
    position: absolute;
    left: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

.clear-search-btn {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    opacity: 0;
    visibility: hidden;
}

.clear-search-btn.visible {
    opacity: 1;
    visibility: visible;
}

.clear-search-btn:hover {
    color: var(--text-primary);
    background-color: var(--bg-hover);
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 200px;
    justify-content: flex-end;
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.toolbar-btn:hover:not(:disabled) {
    background-color: var(--bg-hover);
    border-color: var(--border-secondary);
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.toolbar-btn span {
    display: none;
}

@media (min-width: 768px) {
    .toolbar-btn span {
        display: inline;
    }
}

.divider {
    width: 1px;
    height: 24px;
    background-color: var(--border-primary);
    margin: 0 var(--spacing-xs);
}

.status-indicator {
    display: flex;
    align-items: center;
}

.performance-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--success);
    color: white;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 2px solid transparent;
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
}

.tab-btn:hover {
    color: var(--text-primary);
    background-color: var(--bg-hover);
}

.tab-btn.active {
    color: var(--accent-primary);
    border-bottom-color: var(--accent-primary);
    background-color: var(--bg-tertiary);
}

.tab-count {
    background-color: var(--bg-hover);
    color: var(--text-primary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.tab-btn.active .tab-count {
    background-color: var(--accent-primary);
    color: white;
}

/* Tab Content */
.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-panel {
    display: none;
    height: 100%;
    overflow: hidden;
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
}

.tab-panel-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Status Bar */
.status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.status-left {
    display: flex;
    align-items: center;
}

.status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.save-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--success);
}

.item-counts {
    font-family: 'Courier New', monospace;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        width: 100%;
        min-width: auto;
    }
    
    .toolbar-center {
        margin: 0;
        max-width: none;
    }
    
    .toolbar-right {
        justify-content: center;
    }
    
    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .tab-panel-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .toolbar-btn span {
        display: none;
    }

    .status-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .status-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* Print Styles */
@media print {
    .toolbar,
    .status-bar {
        display: none;
    }

    .main-content {
        height: auto;
    }

    .tab-navigation {
        display: none;
    }

    .tab-panel {
        display: block !important;
        page-break-inside: avoid;
    }
}
