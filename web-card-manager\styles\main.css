/* Main CSS - Modern UI Overhaul */

:root {
    /* Modern Dark Theme with Gradients */
    --bg-primary: #0a0a0a;
    --bg-secondary: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    --bg-tertiary: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
    --bg-card: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    --bg-glass: rgba(255, 255, 255, 0.08);
    --bg-hover: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    --bg-active: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);

    /* Modern Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-muted: #a0a0a0;
    --text-disabled: #666666;
    --text-accent: #64b5f6;

    /* Modern Borders */
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.15);
    --border-focus: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    --border-glass: rgba(255, 255, 255, 0.2);

    /* Modern Accent Colors with Gradients */
    --accent-primary: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    --accent-secondary: linear-gradient(135deg, #81c784 0%, #66bb6a 100%);
    --accent-tertiary: linear-gradient(135deg, #ffb74d 0%, #ffa726 100%);
    --accent-hover: linear-gradient(135deg, #90caf9 0%, #64b5f6 100%);
    --accent-active: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);

    /* Status Colors with Gradients */
    --success: linear-gradient(135deg, #81c784 0%, #66bb6a 100%);
    --success-hover: linear-gradient(135deg, #a5d6a7 0%, #81c784 100%);
    --warning: linear-gradient(135deg, #ffb74d 0%, #ffa726 100%);
    --warning-hover: linear-gradient(135deg, #ffcc02 0%, #ffb74d 100%);
    --danger: linear-gradient(135deg, #e57373 0%, #ef5350 100%);
    --danger-hover: linear-gradient(135deg, #ef9a9a 0%, #e57373 100%);

    /* Modern Shadows with Multiple Layers */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25), 0 8px 16px rgba(0, 0, 0, 0.35);
    --shadow-glow: 0 0 20px rgba(100, 181, 246, 0.3);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.1);

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Modern Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Z-index layers */
    --z-dropdown: 1000;
    --z-modal: 2000;
    --z-toast: 3000;
    --z-loading: 4000;
}

/* Reset and Modern Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(100, 181, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(129, 199, 132, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 183, 77, 0.05) 0%, transparent 50%);
    background-attachment: fixed;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Modern Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-glass);
    border-radius: var(--radius-full);
    border: 1px solid var(--border-primary);
    transition: all var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-primary);
    box-shadow: var(--shadow-glow);
}

/* Selection Styling */
::selection {
    background: rgba(100, 181, 246, 0.3);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(100, 181, 246, 0.3);
    color: var(--text-primary);
}

/* Light Theme Override (Optional) */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --bg-tertiary: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    --bg-card: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.01) 100%);
    --bg-glass: rgba(0, 0, 0, 0.05);

    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --text-disabled: #adb5bd;

    --border-primary: rgba(0, 0, 0, 0.1);
    --border-secondary: rgba(0, 0, 0, 0.15);

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Modern App Layout */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

.main-app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    opacity: 1;
    transform: translateY(0);
    transition: all var(--transition-slow);
}

.main-app.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

/* Modern Glassmorphism Toolbar */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-md);
    -webkit-backdrop-filter: var(--blur-md);
    border-bottom: 1px solid var(--border-glass);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all var(--transition-normal);
}

.toolbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-secondary);
    opacity: 0.8;
    z-index: -1;
    border-radius: 0;
}

.toolbar-left {
    display: flex;
    align-items: center;
    min-width: 250px;
    z-index: 1;
}

.app-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all var(--transition-normal);
}

.app-title:hover {
    transform: translateY(-1px);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.app-title i {
    background: var(--accent-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.2em;
    filter: drop-shadow(0 2px 4px rgba(100, 181, 246, 0.3));
    transition: all var(--transition-normal);
}

.app-title:hover i {
    filter: drop-shadow(0 4px 8px rgba(100, 181, 246, 0.5));
    transform: scale(1.1);
}

.toolbar-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 var(--spacing-xl);
    z-index: 1;
}

.search-container {
    position: relative;
    width: 100%;
    max-width: 450px;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-left: 3rem;
    padding-right: 3rem;
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-full);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-inner);
}

.search-input::placeholder {
    color: var(--text-muted);
    font-weight: var(--font-weight-normal);
}

.search-input:focus {
    outline: none;
    border: 1px solid transparent;
    background: var(--accent-primary);
    box-shadow: var(--shadow-glow), var(--shadow-md);
    transform: translateY(-1px);
}

.search-input:focus::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
    transition: all var(--transition-normal);
    font-size: 1.1em;
}

.search-input:focus + .search-icon {
    color: rgba(255, 255, 255, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.clear-search-btn {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-full);
    transition: all var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-search-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) scale(1);
}

.clear-search-btn:hover {
    color: var(--text-primary);
    background: var(--danger);
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-md);
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    min-width: 250px;
    justify-content: flex-end;
    z-index: 1;
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.toolbar-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-slow);
}

.toolbar-btn:hover::before {
    left: 100%;
}

.toolbar-btn:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--border-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.toolbar-btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.toolbar-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
}

.toolbar-btn i {
    font-size: 1.1em;
    transition: all var(--transition-normal);
}

.toolbar-btn:hover:not(:disabled) i {
    transform: scale(1.1);
}

.toolbar-btn span {
    display: none;
}

@media (min-width: 768px) {
    .toolbar-btn span {
        display: inline;
    }
}

.divider {
    width: 2px;
    height: 32px;
    background: linear-gradient(to bottom, transparent, var(--border-glass), transparent);
    margin: 0 var(--spacing-md);
    border-radius: var(--radius-full);
}

.status-indicator {
    display: flex;
    align-items: center;
}

.performance-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--success);
    color: white;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 2px solid transparent;
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
}

.tab-btn:hover {
    color: var(--text-primary);
    background-color: var(--bg-hover);
}

.tab-btn.active {
    color: var(--accent-primary);
    border-bottom-color: var(--accent-primary);
    background-color: var(--bg-tertiary);
}

.tab-count {
    background-color: var(--bg-hover);
    color: var(--text-primary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.tab-btn.active .tab-count {
    background-color: var(--accent-primary);
    color: white;
}

/* Tab Content */
.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-panel {
    display: none;
    height: 100%;
    overflow: hidden;
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
}

.tab-panel-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Status Bar */
.status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.status-left {
    display: flex;
    align-items: center;
}

.status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.save-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--success);
}

.item-counts {
    font-family: 'Courier New', monospace;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        width: 100%;
        min-width: auto;
    }
    
    .toolbar-center {
        margin: 0;
        max-width: none;
    }
    
    .toolbar-right {
        justify-content: center;
    }
    
    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .tab-panel-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .toolbar-btn span {
        display: none;
    }

    .status-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .status-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* Print Styles */
@media print {
    .toolbar,
    .status-bar {
        display: none;
    }

    .main-content {
        height: auto;
    }

    .tab-navigation {
        display: none;
    }

    .tab-panel {
        display: block !important;
        page-break-inside: avoid;
    }
}
