/**
 * Data Manager - Core data management and business logic
 * Handles all card, email, and combination operations
 */

import { validateCard, validateEmail, formatCard } from '../utils/validation.js';
import { generateId, getCurrentTimestamp } from '../utils/helpers.js';

export class DataManager {
    constructor() {
        // Core data structures
        this.cards = new Set();
        this.pmEmails = new Set();
        this.ueEmails2525 = new Set();
        this.ueEmails2515 = new Set();
        this.ueEmails251 = new Set();
        
        // Used items tracking
        this.usedCards = new Set();
        this.usedEmails = new Set();
        this.allUsedCards = new Set(); // Historical used cards
        this.allUsedEmails = new Set(); // Historical used emails
        
        // Archived items
        this.archivedCards = new Set();
        this.archivedEmails = new Set();
        
        // Combinations
        this.combinations = [];
        
        // Event listeners
        this.listeners = new Map();
        
        // Storage key
        this.storageKey = 'cardManagerData';
    }

    /**
     * Initialize the data manager
     */
    async init() {
        console.log('📊 Initializing Data Manager...');
        
        // Load data from storage
        await this.loadData();
        
        console.log('✅ Data Manager initialized');
    }

    /**
     * Add event listener
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * Remove event listener
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * Emit event to listeners
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    // Card Management
    /**
     * Add a card
     */
    addCard(cardData) {
        const validation = validateCard(cardData);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }

        const formattedCard = formatCard(cardData);
        
        if (this.cards.has(formattedCard)) {
            throw new Error('Card already exists');
        }

        this.cards.add(formattedCard);
        this.emit('cardAdded', { card: formattedCard });
        this.saveData();
        
        return formattedCard;
    }

    /**
     * Add multiple cards
     */
    addCards(cardsData) {
        const results = {
            added: [],
            errors: []
        };

        cardsData.forEach((cardData, index) => {
            try {
                const card = this.addCard(cardData);
                results.added.push(card);
            } catch (error) {
                results.errors.push({
                    line: index + 1,
                    data: cardData,
                    error: error.message
                });
            }
        });

        if (results.added.length > 0) {
            this.emit('cardsAdded', results);
        }

        return results;
    }

    /**
     * Remove a card
     */
    removeCard(card) {
        if (!this.cards.has(card)) {
            throw new Error('Card not found');
        }

        this.cards.delete(card);
        this.usedCards.delete(card);
        this.archivedCards.delete(card);
        
        this.emit('cardRemoved', { card });
        this.saveData();
    }

    /**
     * Archive a card
     */
    archiveCard(card) {
        if (!this.cards.has(card)) {
            throw new Error('Card not found');
        }

        this.cards.delete(card);
        this.archivedCards.add(card);
        
        this.emit('cardArchived', { card });
        this.saveData();
    }

    /**
     * Restore archived card
     */
    restoreArchivedCard(card) {
        if (!this.archivedCards.has(card)) {
            throw new Error('Archived card not found');
        }

        this.archivedCards.delete(card);
        this.cards.add(card);
        
        this.emit('cardRestored', { card });
        this.saveData();
    }

    /**
     * Get available cards (not used, not archived)
     */
    getAvailableCards() {
        return Array.from(this.cards).filter(card => 
            !this.allUsedCards.has(card) && !this.archivedCards.has(card)
        );
    }

    /**
     * Get oldest available card
     */
    getOldestCard() {
        const available = this.getAvailableCards();
        return available.length > 0 ? available[available.length - 1] : null;
    }

    // Email Management
    /**
     * Add an email
     */
    addEmail(emailData, type = 'pm_emails') {
        if (!validateEmail(emailData)) {
            throw new Error('Invalid email format');
        }

        const email = emailData.trim().toLowerCase();
        const emailSet = this.getEmailSet(type);
        
        if (emailSet.has(email)) {
            throw new Error('Email already exists in this category');
        }

        emailSet.add(email);
        this.emit('emailAdded', { email, type });
        this.saveData();
        
        return email;
    }

    /**
     * Add multiple emails
     */
    addEmails(emailsData, type = 'pm_emails') {
        const results = {
            added: [],
            errors: []
        };

        emailsData.forEach((emailData, index) => {
            try {
                const email = this.addEmail(emailData, type);
                results.added.push(email);
            } catch (error) {
                results.errors.push({
                    line: index + 1,
                    data: emailData,
                    error: error.message
                });
            }
        });

        if (results.added.length > 0) {
            this.emit('emailsAdded', { results, type });
        }

        return results;
    }

    /**
     * Remove an email
     */
    removeEmail(email, type) {
        const emailSet = this.getEmailSet(type);
        
        if (!emailSet.has(email)) {
            throw new Error('Email not found');
        }

        emailSet.delete(email);
        this.usedEmails.delete(email);
        this.archivedEmails.delete(email);
        
        this.emit('emailRemoved', { email, type });
        this.saveData();
    }

    /**
     * Get email set by type
     */
    getEmailSet(type) {
        switch (type) {
            case 'pm_emails': return this.pmEmails;
            case 'ue_emails_25_25': return this.ueEmails2525;
            case 'ue_emails_25_15': return this.ueEmails2515;
            case 'ue_emails_25_1': return this.ueEmails251;
            default: throw new Error('Invalid email type');
        }
    }

    /**
     * Get available emails by type
     */
    getAvailableEmails(type) {
        const emailSet = this.getEmailSet(type);
        return Array.from(emailSet).filter(email => 
            !this.allUsedEmails.has(email) && !this.archivedEmails.has(email)
        );
    }

    /**
     * Get oldest available email by type
     */
    getOldestEmail(type) {
        const available = this.getAvailableEmails(type);
        return available.length > 0 ? available[available.length - 1] : null;
    }

    /**
     * Get all emails across all types
     */
    getAllEmails() {
        const allEmails = new Set();
        
        ['pm_emails', 'ue_emails_25_25', 'ue_emails_25_15', 'ue_emails_25_1'].forEach(type => {
            this.getEmailSet(type).forEach(email => allEmails.add(email));
        });
        
        return Array.from(allEmails);
    }

    // Combination Management
    /**
     * Create a combination
     */
    createCombination(card, email, emailType, autoSelected = false) {
        // Validate inputs
        if (!card || !email || !emailType) {
            throw new Error('Card, email, and email type are required');
        }

        // Check if card is available
        if (!this.cards.has(card) || this.allUsedCards.has(card)) {
            throw new Error('Card is not available');
        }

        // Check if email is available
        const emailSet = this.getEmailSet(emailType);
        if (!emailSet.has(email) || this.allUsedEmails.has(email)) {
            throw new Error('Email is not available');
        }

        // Create combination object
        const combination = {
            id: generateId(),
            card,
            email,
            emailType,
            timestamp: getCurrentTimestamp(),
            autoSelected,
            woolFormat: this.formatWool(card, email),
            fusionFormat: this.formatFusion(card, email),
            otpFormat: email
        };

        // Mark items as used
        this.usedCards.add(card);
        this.usedEmails.add(email);
        this.allUsedCards.add(card);
        this.allUsedEmails.add(email);

        // Add to combinations
        this.combinations.unshift(combination); // Add to beginning for latest first

        this.emit('combinationCreated', { combination });
        this.saveData();

        return combination;
    }

    /**
     * Auto-create combination with oldest items
     */
    autoCreateCombination(emailType) {
        const card = this.getOldestCard();
        const email = this.getOldestEmail(emailType);

        if (!card) {
            throw new Error('No available cards');
        }

        if (!email) {
            throw new Error(`No available emails of type ${emailType}`);
        }

        return this.createCombination(card, email, emailType, true);
    }

    /**
     * Delete a combination
     */
    deleteCombination(combinationId) {
        const index = this.combinations.findIndex(c => c.id === combinationId);
        
        if (index === -1) {
            throw new Error('Combination not found');
        }

        const combination = this.combinations[index];
        
        // Remove from combinations
        this.combinations.splice(index, 1);
        
        // Remove from used items (but keep in all used for history)
        this.usedCards.delete(combination.card);
        this.usedEmails.delete(combination.email);

        this.emit('combinationDeleted', { combination });
        this.saveData();
    }

    /**
     * Format combination for Wool
     */
    formatWool(card, email) {
        const parts = card.split(',');
        if (parts.length !== 5) return `${card},${email}`;
        
        const [number, month, year, cvv, zip] = parts;
        return `${number},${month}/${year},${cvv},${zip},${email}`;
    }

    /**
     * Format combination for Fusion
     */
    formatFusion(card, email) {
        return `${card},${email}`;
    }

    /**
     * Get combinations
     */
    getCombinations() {
        return [...this.combinations];
    }

    /**
     * Clear all data
     */
    clearAllData() {
        this.cards.clear();
        this.pmEmails.clear();
        this.ueEmails2525.clear();
        this.ueEmails2515.clear();
        this.ueEmails251.clear();
        this.usedCards.clear();
        this.usedEmails.clear();
        this.allUsedCards.clear();
        this.allUsedEmails.clear();
        this.archivedCards.clear();
        this.archivedEmails.clear();
        this.combinations = [];
        
        this.emit('dataCleared');
        this.saveData();
    }

    /**
     * Get data counts
     */
    getCounts() {
        return {
            cards: this.cards.size,
            availableCards: this.getAvailableCards().length,
            usedCards: this.usedCards.size,
            archivedCards: this.archivedCards.size,
            pmEmails: this.pmEmails.size,
            ueEmails2525: this.ueEmails2525.size,
            ueEmails2515: this.ueEmails2515.size,
            ueEmails251: this.ueEmails251.size,
            totalEmails: this.getAllEmails().length,
            usedEmails: this.usedEmails.size,
            archivedEmails: this.archivedEmails.size,
            combinations: this.combinations.length
        };
    }

    /**
     * Save data to localStorage
     */
    saveData() {
        try {
            const data = {
                cards: Array.from(this.cards),
                pmEmails: Array.from(this.pmEmails),
                ueEmails2525: Array.from(this.ueEmails2525),
                ueEmails2515: Array.from(this.ueEmails2515),
                ueEmails251: Array.from(this.ueEmails251),
                usedCards: Array.from(this.usedCards),
                usedEmails: Array.from(this.usedEmails),
                allUsedCards: Array.from(this.allUsedCards),
                allUsedEmails: Array.from(this.allUsedEmails),
                archivedCards: Array.from(this.archivedCards),
                archivedEmails: Array.from(this.archivedEmails),
                combinations: this.combinations,
                lastSaved: getCurrentTimestamp()
            };

            localStorage.setItem(this.storageKey, JSON.stringify(data));
            this.emit('dataSaved', { timestamp: data.lastSaved });
            
        } catch (error) {
            console.error('Failed to save data:', error);
            this.emit('saveError', { error });
        }
    }

    /**
     * Load data from localStorage
     */
    async loadData() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            
            if (!savedData) {
                console.log('No saved data found, starting fresh');
                return;
            }

            const data = JSON.parse(savedData);
            
            // Restore data structures
            this.cards = new Set(data.cards || []);
            this.pmEmails = new Set(data.pmEmails || []);
            this.ueEmails2525 = new Set(data.ueEmails2525 || []);
            this.ueEmails2515 = new Set(data.ueEmails2515 || []);
            this.ueEmails251 = new Set(data.ueEmails251 || []);
            this.usedCards = new Set(data.usedCards || []);
            this.usedEmails = new Set(data.usedEmails || []);
            this.allUsedCards = new Set(data.allUsedCards || []);
            this.allUsedEmails = new Set(data.allUsedEmails || []);
            this.archivedCards = new Set(data.archivedCards || []);
            this.archivedEmails = new Set(data.archivedEmails || []);
            this.combinations = data.combinations || [];

            console.log('✅ Data loaded successfully');
            this.emit('dataLoaded', { timestamp: data.lastSaved });
            
        } catch (error) {
            console.error('Failed to load data:', error);
            this.emit('loadError', { error });
            throw error;
        }
    }

    /**
     * Export data
     */
    exportData() {
        const data = {
            cards: Array.from(this.cards),
            pmEmails: Array.from(this.pmEmails),
            ueEmails2525: Array.from(this.ueEmails2525),
            ueEmails2515: Array.from(this.ueEmails2515),
            ueEmails251: Array.from(this.ueEmails251),
            usedCards: Array.from(this.usedCards),
            usedEmails: Array.from(this.usedEmails),
            allUsedCards: Array.from(this.allUsedCards),
            allUsedEmails: Array.from(this.allUsedEmails),
            archivedCards: Array.from(this.archivedCards),
            archivedEmails: Array.from(this.archivedEmails),
            combinations: this.combinations,
            exportedAt: getCurrentTimestamp(),
            version: '1.0.0'
        };

        return data;
    }

    /**
     * Import data
     */
    importData(importedData) {
        try {
            // Validate imported data structure
            if (!importedData || typeof importedData !== 'object') {
                throw new Error('Invalid data format');
            }

            // Clear existing data
            this.clearAllData();

            // Import data
            if (importedData.cards) this.cards = new Set(importedData.cards);
            if (importedData.pmEmails) this.pmEmails = new Set(importedData.pmEmails);
            if (importedData.ueEmails2525) this.ueEmails2525 = new Set(importedData.ueEmails2525);
            if (importedData.ueEmails2515) this.ueEmails2515 = new Set(importedData.ueEmails2515);
            if (importedData.ueEmails251) this.ueEmails251 = new Set(importedData.ueEmails251);
            if (importedData.usedCards) this.usedCards = new Set(importedData.usedCards);
            if (importedData.usedEmails) this.usedEmails = new Set(importedData.usedEmails);
            if (importedData.allUsedCards) this.allUsedCards = new Set(importedData.allUsedCards);
            if (importedData.allUsedEmails) this.allUsedEmails = new Set(importedData.allUsedEmails);
            if (importedData.archivedCards) this.archivedCards = new Set(importedData.archivedCards);
            if (importedData.archivedEmails) this.archivedEmails = new Set(importedData.archivedEmails);
            if (importedData.combinations) this.combinations = importedData.combinations;

            this.emit('dataImported', { timestamp: getCurrentTimestamp() });
            this.saveData();

        } catch (error) {
            console.error('Failed to import data:', error);
            this.emit('importError', { error });
            throw error;
        }
    }

    /**
     * Clear specific email type
     */
    clearEmailType(type) {
        const emailSet = this.getEmailSet(type);
        const count = emailSet.size;
        emailSet.clear();

        this.emit('emailTypeCleared', { type, count });
        this.saveData();

        return count;
    }

    /**
     * Copy all items of a specific type
     */
    copyAllCards() {
        return Array.from(this.cards);
    }

    copyAllEmails(type) {
        return Array.from(this.getEmailSet(type));
    }

    copyUsedCards() {
        return Array.from(this.usedCards);
    }

    copyUsedEmails() {
        return Array.from(this.usedEmails);
    }

    /**
     * Remove all used items
     */
    removeAllUsedCards() {
        const count = this.usedCards.size;
        this.usedCards.clear();

        this.emit('usedCardsCleared', { count });
        this.saveData();

        return count;
    }

    removeAllUsedEmails() {
        const count = this.usedEmails.size;
        this.usedEmails.clear();

        this.emit('usedEmailsCleared', { count });
        this.saveData();

        return count;
    }

    /**
     * Restore all archived items
     */
    restoreAllArchivedCards() {
        const count = this.archivedCards.size;
        this.archivedCards.forEach(card => this.cards.add(card));
        this.archivedCards.clear();

        this.emit('archivedCardsRestored', { count });
        this.saveData();

        return count;
    }

    restoreAllArchivedEmails() {
        const count = this.archivedEmails.size;
        // Note: Need to determine which email type to restore to
        // For now, restore to PM emails
        this.archivedEmails.forEach(email => this.pmEmails.add(email));
        this.archivedEmails.clear();

        this.emit('archivedEmailsRestored', { count });
        this.saveData();

        return count;
    }

    /**
     * Clear all combinations
     */
    clearAllCombinations() {
        const count = this.combinations.length;

        // Move used items back to available (but keep in all used for history)
        this.combinations.forEach(combo => {
            this.usedCards.delete(combo.card);
            this.usedEmails.delete(combo.email);
        });

        this.combinations = [];

        this.emit('combinationsCleared', { count });
        this.saveData();

        return count;
    }
}
