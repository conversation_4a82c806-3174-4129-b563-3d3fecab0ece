/* Modern Animations CSS - Advanced transitions and loading states */

/* Modern Loading Screen with Glassmorphism */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(129, 199, 132, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 183, 77, 0.1) 0%, transparent 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading);
    transition: all var(--transition-slow);
    backdrop-filter: var(--blur-lg);
    -webkit-backdrop-filter: var(--blur-lg);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
    transform: scale(1.1);
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2xl);
    padding: var(--spacing-3xl);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-md);
    -webkit-backdrop-filter: var(--blur-md);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    animation: float 3s ease-in-out infinite;
}

.spinner {
    width: 60px;
    height: 60px;
    position: relative;
    animation: pulse 2s ease-in-out infinite;
}

.spinner::before,
.spinner::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    animation: spin 2s linear infinite;
}

.spinner::before {
    width: 60px;
    height: 60px;
    border: 3px solid transparent;
    border-top: 3px solid var(--accent-primary);
    border-right: 3px solid var(--accent-primary);
}

.spinner::after {
    width: 40px;
    height: 40px;
    top: 10px;
    left: 10px;
    border: 2px solid transparent;
    border-bottom: 2px solid var(--accent-secondary);
    border-left: 2px solid var(--accent-secondary);
    animation: spin 1.5s linear infinite reverse;
}

.loading-spinner p {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    text-align: center;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0% {
        text-shadow: 0 0 5px rgba(100, 181, 246, 0.5);
        color: var(--text-primary);
    }
    100% {
        text-shadow: 0 0 20px rgba(100, 181, 246, 0.8);
        color: var(--text-accent);
    }
}

/* Modern Fade Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn var(--transition-normal) var(--transition-bounce);
}

.fade-out {
    animation: fadeOut var(--transition-normal) ease-out;
}

.fade-in-up {
    animation: fadeInUp var(--transition-normal) ease-out;
}

.fade-in-down {
    animation: fadeInDown var(--transition-normal) ease-out;
}

/* Enhanced Slide Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(40px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.slide-in-left {
    animation: slideInLeft var(--transition-normal) var(--transition-bounce);
}

.slide-in-right {
    animation: slideInRight var(--transition-normal) var(--transition-bounce);
}

.slide-in-up {
    animation: slideInUp var(--transition-normal) var(--transition-bounce);
}

/* Modern Tab Animations */
@keyframes tabSlide {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.tab-panel {
    animation: tabSlide var(--transition-normal) ease-out;
}

.tab-panel.active {
    animation: fadeInUp var(--transition-normal) ease-out;
}

/* Tab Indicator Animation */
@keyframes indicatorSlide {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

.tab-indicator {
    animation: indicatorSlide var(--transition-normal) ease-out;
}
}

.slide-in-left {
    animation: slideInLeft var(--transition-normal) var(--transition-bounce);
}

.slide-in-right {
    animation: slideInRight var(--transition-normal) var(--transition-bounce);
}

.slide-in-up {
    animation: slideInUp var(--transition-normal) var(--transition-bounce);
}

/* Modern Scale Animations */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8) rotate(-5deg);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes scaleInBounce {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.scale-in {
    animation: scaleIn var(--transition-normal) ease-out;
}

.scale-in-bounce {
    animation: scaleInBounce var(--transition-slow) ease-out;
}

/* List Item Animations */
@keyframes listItemEnter {
    from {
        opacity: 0;
        transform: translateX(-20px) scale(0.95);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
        max-height: 200px;
        padding: var(--spacing-md);
        margin: var(--spacing-sm) 0;
    }
}

@keyframes listItemExit {
    from {
        opacity: 1;
        transform: translateX(0) scale(1);
        max-height: 200px;
        padding: var(--spacing-md);
        margin: var(--spacing-sm) 0;
    }
    to {
        opacity: 0;
        transform: translateX(20px) scale(0.95);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

.list-item-enter {
    animation: listItemEnter var(--transition-normal) var(--transition-bounce);
}

.list-item-exit {
    animation: listItemExit var(--transition-normal) ease-in;
}

/* Modern Modal Animations */
@keyframes modalBackdropFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: var(--blur-md);
        -webkit-backdrop-filter: var(--blur-md);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-backdrop {
    animation: modalBackdropFadeIn var(--transition-normal) ease-out;
}

.modal-content {
    animation: modalSlideIn var(--transition-normal) var(--transition-bounce);
}

/* Enhanced Pulse Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: var(--shadow-md);
    }
    50% {
        box-shadow: var(--shadow-glow), var(--shadow-lg);
    }
}

.pulse {
    animation: pulse 2s ease-in-out infinite;
}

.pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.bounce {
    animation: bounce 1s ease-in-out;
}

/* Shake Animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* Glow Effect */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--accent-primary);
    }
    50% {
        box-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary);
    }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Progress Bar Animation */
@keyframes progressBar {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

.progress-bar {
    height: 3px;
    background-color: var(--accent-primary);
    animation: progressBar 2s ease-in-out;
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--text-primary);
    }
}

.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--text-primary);
    white-space: nowrap;
    animation: typing 2s steps(40, end), blink 1s step-end infinite;
}

/* Hover Animations */
.hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-fast);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Focus Animations */
.focus-ring {
    transition: box-shadow var(--transition-fast);
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.3);
}

/* Stagger Animation */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn var(--transition-normal) ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }
.stagger-item:nth-child(7) { animation-delay: 0.7s; }
.stagger-item:nth-child(8) { animation-delay: 0.8s; }
.stagger-item:nth-child(9) { animation-delay: 0.9s; }
.stagger-item:nth-child(10) { animation-delay: 1.0s; }

/* Modal Animations */
.modal-overlay {
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.modal {
    transform: scale(0.9) translateY(-20px);
    transition: transform var(--transition-normal);
}

.modal-overlay.visible .modal {
    transform: scale(1) translateY(0);
}

/* Toast Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-enter {
    animation: slideInRight var(--transition-normal) ease-out;
}

.toast-exit {
    animation: slideOutRight var(--transition-normal) ease-in;
}

/* Search Highlight Animation */
@keyframes highlight {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(255, 255, 0, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

.search-highlight {
    animation: highlight 1s ease-in-out;
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .spinner {
        animation: none;
        border: 3px solid var(--accent-primary);
    }
}
