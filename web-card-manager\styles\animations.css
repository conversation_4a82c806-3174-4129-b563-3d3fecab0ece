/* Animations CSS - Transitions, animations, and loading states */

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading);
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-spinner p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn var(--transition-normal) ease-out;
}

/* Slide In Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft var(--transition-normal) ease-out;
}

.slide-in-right {
    animation: slideInRight var(--transition-normal) ease-out;
}

.slide-in-up {
    animation: slideInUp var(--transition-normal) ease-out;
}

/* Scale Animation */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.scale-in {
    animation: scaleIn var(--transition-normal) ease-out;
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.bounce {
    animation: bounce 1s ease-in-out;
}

/* Shake Animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* Glow Effect */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--accent-primary);
    }
    50% {
        box-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary);
    }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Progress Bar Animation */
@keyframes progressBar {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

.progress-bar {
    height: 3px;
    background-color: var(--accent-primary);
    animation: progressBar 2s ease-in-out;
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--text-primary);
    }
}

.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--text-primary);
    white-space: nowrap;
    animation: typing 2s steps(40, end), blink 1s step-end infinite;
}

/* Hover Animations */
.hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-fast);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Focus Animations */
.focus-ring {
    transition: box-shadow var(--transition-fast);
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.3);
}

/* Stagger Animation */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn var(--transition-normal) ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }
.stagger-item:nth-child(7) { animation-delay: 0.7s; }
.stagger-item:nth-child(8) { animation-delay: 0.8s; }
.stagger-item:nth-child(9) { animation-delay: 0.9s; }
.stagger-item:nth-child(10) { animation-delay: 1.0s; }

/* Modal Animations */
.modal-overlay {
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.modal {
    transform: scale(0.9) translateY(-20px);
    transition: transform var(--transition-normal);
}

.modal-overlay.visible .modal {
    transform: scale(1) translateY(0);
}

/* Toast Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-enter {
    animation: slideInRight var(--transition-normal) ease-out;
}

.toast-exit {
    animation: slideOutRight var(--transition-normal) ease-in;
}

/* Search Highlight Animation */
@keyframes highlight {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(255, 255, 0, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

.search-highlight {
    animation: highlight 1s ease-in-out;
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .spinner {
        animation: none;
        border: 3px solid var(--accent-primary);
    }
}
