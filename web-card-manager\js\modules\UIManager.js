/**
 * UI Manager - Handles all user interface interactions and updates
 */

import { getElementById, createElement, copyToClipboard, animateElement } from '../utils/helpers.js';
import { getEmailTypeDisplayName } from '../utils/validation.js';

export class UIManager {
    constructor(dataManager, toastManager) {
        this.dataManager = dataManager;
        this.toastManager = toastManager;
        
        // Manager references (set later)
        this.searchManager = null;
        this.historyManager = null;
        this.modalManager = null;
        this.configManager = null;
        
        // UI state
        this.currentTab = 'cards';
        this.isUpdating = false;
        
        // Element cache
        this.elements = {};
        
        // Event handlers
        this.handleTabChange = this.handleTabChange.bind(this);
        this.handleCardInput = this.handleCardInput.bind(this);
        this.handleEmailInput = this.handleEmailInput.bind(this);
        this.handleCombineClick = this.handleCombineClick.bind(this);
        this.handleGlobalSearch = this.handleGlobalSearch.bind(this);
    }

    /**
     * Initialize UI Manager
     */
    async init() {
        console.log('🎨 Initializing UI Manager...');
        
        // Cache DOM elements
        this.cacheElements();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Set up data manager listeners
        this.setupDataListeners();
        
        // Initialize UI state
        this.initializeUI();
        
        console.log('✅ UI Manager initialized');
    }

    /**
     * Set manager references
     */
    setSearchManager(searchManager) { this.searchManager = searchManager; }
    setHistoryManager(historyManager) { this.historyManager = historyManager; }
    setModalManager(modalManager) { this.modalManager = modalManager; }
    setConfigManager(configManager) { this.configManager = configManager; }
    setToastManager(toastManager) { this.toastManager = toastManager; }

    /**
     * Cache frequently used DOM elements
     */
    cacheElements() {
        // Tab elements
        this.elements.tabBtns = document.querySelectorAll('.tab-btn');
        this.elements.tabPanels = document.querySelectorAll('.tab-panel');
        
        // Input elements
        this.elements.cardInput = getElementById('card-input');
        this.elements.emailInput = getElementById('email-input');
        this.elements.emailTypeSelect = getElementById('email-type-select');
        this.elements.addCardBtn = getElementById('add-card-btn');
        this.elements.addEmailBtn = getElementById('add-email-btn');
        
        // Combination elements
        this.elements.cardSelect = getElementById('card-select');
        this.elements.emailSelect = getElementById('email-select');
        this.elements.combinationEmailType = getElementById('combination-email-type');
        this.elements.combineBtn = getElementById('combine-btn');
        this.elements.autoSelectCheckbox = getElementById('auto-select-checkbox');
        
        // List containers
        this.elements.availableCardsList = getElementById('available-cards-list');
        this.elements.usedCardsList = getElementById('used-cards-list');
        this.elements.archivedCardsList = getElementById('archived-cards-list');
        this.elements.pmEmailsList = getElementById('pm-emails-list');
        this.elements.ueEmails2525List = getElementById('ue-emails-25-25-list');
        this.elements.ueEmails2515List = getElementById('ue-emails-25-15-list');
        this.elements.ueEmails251List = getElementById('ue-emails-25-1-list');
        this.elements.usedEmailsList = getElementById('used-emails-list');
        this.elements.archivedEmailsList = getElementById('archived-emails-list');
        this.elements.combinationsList = getElementById('combinations-list');
        this.elements.latestCombination = getElementById('latest-combination');
        this.elements.latestCombinationContent = getElementById('latest-combination-content');
        
        // Search elements
        this.elements.globalSearch = getElementById('global-search');
        this.elements.clearSearchBtn = getElementById('clear-search');
        
        // Toolbar elements
        this.elements.undoBtn = getElementById('undo-btn');
        this.elements.redoBtn = getElementById('redo-btn');
        this.elements.importBtn = getElementById('import-btn');
        this.elements.exportBtn = getElementById('export-btn');
        this.elements.settingsBtn = getElementById('settings-btn');
        
        // Status elements
        this.elements.statusMessage = getElementById('status-message');
        this.elements.saveStatus = getElementById('save-status');
        this.elements.totalCards = getElementById('total-cards');
        this.elements.totalEmails = getElementById('total-emails');
        this.elements.totalCombinations = getElementById('total-combinations');
        
        // Count badges
        this.elements.cardsCount = getElementById('cards-count');
        this.elements.emailsCount = getElementById('emails-count');
        this.elements.combinationsCount = getElementById('combinations-count');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Tab navigation
        this.elements.tabBtns.forEach(btn => {
            btn.addEventListener('click', this.handleTabChange);
        });
        
        // Card input
        if (this.elements.cardInput) {
            this.elements.cardInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleCardInput();
                }
            });
        }
        
        if (this.elements.addCardBtn) {
            this.elements.addCardBtn.addEventListener('click', this.handleCardInput);
        }
        
        // Email input
        if (this.elements.emailInput) {
            this.elements.emailInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleEmailInput();
                }
            });
        }
        
        if (this.elements.addEmailBtn) {
            this.elements.addEmailBtn.addEventListener('click', this.handleEmailInput);
        }
        
        // Combination
        if (this.elements.combineBtn) {
            this.elements.combineBtn.addEventListener('click', this.handleCombineClick);
        }
        
        // Auto-select checkbox
        if (this.elements.autoSelectCheckbox) {
            this.elements.autoSelectCheckbox.addEventListener('change', () => {
                this.updateCombinationSelects();
            });
        }
        
        // Email type change for combination
        if (this.elements.combinationEmailType) {
            this.elements.combinationEmailType.addEventListener('change', () => {
                this.updateCombinationSelects();
            });
        }
        
        // Global search
        if (this.elements.globalSearch) {
            this.elements.globalSearch.addEventListener('input', this.handleGlobalSearch);
        }
        
        if (this.elements.clearSearchBtn) {
            this.elements.clearSearchBtn.addEventListener('click', () => {
                this.elements.globalSearch.value = '';
                this.handleGlobalSearch();
            });
        }
        
        // Toolbar buttons
        if (this.elements.undoBtn) {
            this.elements.undoBtn.addEventListener('click', () => {
                if (this.historyManager) {
                    this.historyManager.undo();
                }
            });
        }
        
        if (this.elements.redoBtn) {
            this.elements.redoBtn.addEventListener('click', () => {
                if (this.historyManager) {
                    this.historyManager.redo();
                }
            });
        }
        
        if (this.elements.importBtn) {
            this.elements.importBtn.addEventListener('click', () => {
                this.showImportModal();
            });
        }
        
        if (this.elements.exportBtn) {
            this.elements.exportBtn.addEventListener('click', () => {
                this.exportData();
            });
        }
        
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }
        
        // Bulk operation buttons
        this.setupBulkOperationListeners();
        
        // Copy buttons
        this.setupCopyButtonListeners();
        
        // Clear buttons
        this.setupClearButtonListeners();
    }

    /**
     * Set up bulk operation listeners
     */
    setupBulkOperationListeners() {
        const bulkAddCardsBtn = getElementById('bulk-add-cards-btn');
        const bulkAddEmailsBtn = getElementById('bulk-add-emails-btn');
        
        if (bulkAddCardsBtn) {
            bulkAddCardsBtn.addEventListener('click', () => {
                this.showBulkAddModal('cards');
            });
        }
        
        if (bulkAddEmailsBtn) {
            bulkAddEmailsBtn.addEventListener('click', () => {
                this.showBulkAddModal('emails');
            });
        }
    }

    /**
     * Set up copy button listeners
     */
    setupCopyButtonListeners() {
        // Available cards copy buttons
        const copyAvailableCardsBtn = getElementById('copy-available-cards-btn');
        const copyAvailableCards2xBtn = getElementById('copy-available-cards-2x-btn');
        
        if (copyAvailableCardsBtn) {
            copyAvailableCardsBtn.addEventListener('click', () => {
                this.copyAvailableCards(false);
            });
        }
        
        if (copyAvailableCards2xBtn) {
            copyAvailableCards2xBtn.addEventListener('click', () => {
                this.copyAvailableCards(true);
            });
        }
        
        // Used cards copy button
        const copyUsedCardsBtn = getElementById('copy-used-cards-btn');
        if (copyUsedCardsBtn) {
            copyUsedCardsBtn.addEventListener('click', () => {
                this.copyUsedCards();
            });
        }
        
        // Email copy buttons
        document.querySelectorAll('.copy-emails-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.target.closest('.copy-emails-btn').dataset.type;
                this.copyEmails(type, false);
            });
        });
        
        document.querySelectorAll('.copy-emails-2x-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.target.closest('.copy-emails-2x-btn').dataset.type;
                this.copyEmails(type, true);
            });
        });
    }

    /**
     * Set up clear button listeners
     */
    setupClearButtonListeners() {
        // Clear all cards
        const clearAllCardsBtn = getElementById('clear-all-cards-btn');
        if (clearAllCardsBtn) {
            clearAllCardsBtn.addEventListener('click', () => {
                this.clearAllCards();
            });
        }
        
        // Clear all emails
        const clearAllEmailsBtn = getElementById('clear-all-emails-btn');
        if (clearAllEmailsBtn) {
            clearAllEmailsBtn.addEventListener('click', () => {
                this.clearAllEmails();
            });
        }
        
        // Clear email type buttons
        document.querySelectorAll('.clear-emails-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.target.closest('.clear-emails-btn').dataset.type;
                this.clearEmailType(type);
            });
        });
        
        // Remove used items buttons
        const removeUsedCardsBtn = getElementById('remove-used-cards-btn');
        const removeUsedEmailsBtn = getElementById('remove-used-emails-btn');
        
        if (removeUsedCardsBtn) {
            removeUsedCardsBtn.addEventListener('click', () => {
                this.removeUsedCards();
            });
        }
        
        if (removeUsedEmailsBtn) {
            removeUsedEmailsBtn.addEventListener('click', () => {
                this.removeUsedEmails();
            });
        }
        
        // Restore archived items buttons
        const restoreArchivedCardsBtn = getElementById('restore-archived-cards-btn');
        const restoreArchivedEmailsBtn = getElementById('restore-archived-emails-btn');
        
        if (restoreArchivedCardsBtn) {
            restoreArchivedCardsBtn.addEventListener('click', () => {
                this.restoreArchivedCards();
            });
        }
        
        if (restoreArchivedEmailsBtn) {
            restoreArchivedEmailsBtn.addEventListener('click', () => {
                this.restoreArchivedEmails();
            });
        }
        
        // Clear all combinations
        const clearAllCombinationsBtn = getElementById('clear-all-combinations-btn');
        if (clearAllCombinationsBtn) {
            clearAllCombinationsBtn.addEventListener('click', () => {
                this.clearAllCombinations();
            });
        }
    }

    /**
     * Set up data manager event listeners
     */
    setupDataListeners() {
        this.dataManager.on('cardAdded', () => this.refreshCardLists());
        this.dataManager.on('cardsAdded', () => this.refreshCardLists());
        this.dataManager.on('cardRemoved', () => this.refreshCardLists());
        this.dataManager.on('cardArchived', () => this.refreshCardLists());
        this.dataManager.on('cardRestored', () => this.refreshCardLists());
        
        this.dataManager.on('emailAdded', () => this.refreshEmailLists());
        this.dataManager.on('emailsAdded', () => this.refreshEmailLists());
        this.dataManager.on('emailRemoved', () => this.refreshEmailLists());
        
        this.dataManager.on('combinationCreated', (data) => {
            this.refreshCombinationsList();
            this.refreshCardLists();
            this.refreshEmailLists();
            this.showLatestCombination(data.combination);
        });
        
        this.dataManager.on('combinationDeleted', () => {
            this.refreshCombinationsList();
            this.refreshCardLists();
            this.refreshEmailLists();
        });
        
        this.dataManager.on('dataSaved', () => {
            this.updateSaveStatus('saved');
        });
        
        this.dataManager.on('dataLoaded', () => {
            this.refreshAllLists();
            this.updateAllCounts();
        });
    }

    /**
     * Initialize UI state
     */
    initializeUI() {
        // Set initial tab
        this.switchToTab('cards');

        // Update all counts
        this.updateAllCounts();

        // Refresh all lists
        this.refreshAllLists();

        // Update combination selects
        this.updateCombinationSelects();

        // Set initial status
        this.updateStatus('Ready');
    }

    // Event Handlers
    /**
     * Handle tab change
     */
    handleTabChange(event) {
        const tabId = event.target.dataset.tab;
        if (tabId) {
            this.switchToTab(tabId);
        }
    }

    /**
     * Handle card input
     */
    async handleCardInput() {
        const input = this.elements.cardInput;
        if (!input || !input.value.trim()) return;

        try {
            const cardData = input.value.trim();
            await this.dataManager.addCard(cardData);

            input.value = '';
            this.toastManager.success('Card Added', 'Card added successfully');

        } catch (error) {
            this.toastManager.error('Error', error.message);
        }
    }

    /**
     * Handle email input
     */
    async handleEmailInput() {
        const input = this.elements.emailInput;
        const typeSelect = this.elements.emailTypeSelect;

        if (!input || !input.value.trim() || !typeSelect) return;

        try {
            const emailData = input.value.trim();
            const emailType = typeSelect.value;

            await this.dataManager.addEmail(emailData, emailType);

            input.value = '';
            this.toastManager.success('Email Added', 'Email added successfully');

        } catch (error) {
            this.toastManager.error('Error', error.message);
        }
    }

    /**
     * Handle combine click
     */
    async handleCombineClick() {
        try {
            const isAutoSelect = this.elements.autoSelectCheckbox?.checked;
            const emailType = this.elements.combinationEmailType?.value;

            if (!emailType) {
                this.toastManager.error('Error', 'Please select an email type');
                return;
            }

            let combination;

            if (isAutoSelect) {
                combination = await this.dataManager.autoCreateCombination(emailType);
            } else {
                const cardSelect = this.elements.cardSelect;
                const emailSelect = this.elements.emailSelect;

                if (!cardSelect?.value || !emailSelect?.value) {
                    this.toastManager.error('Error', 'Please select both card and email');
                    return;
                }

                combination = await this.dataManager.createCombination(
                    cardSelect.value,
                    emailSelect.value,
                    emailType,
                    false
                );
            }

            this.toastManager.success('Combination Created', 'New combination created successfully');

        } catch (error) {
            this.toastManager.error('Error', error.message);
        }
    }

    /**
     * Handle global search
     */
    handleGlobalSearch() {
        const query = this.elements.globalSearch?.value || '';

        if (this.searchManager) {
            this.searchManager.search(query);
        }

        // Update clear button visibility
        if (this.elements.clearSearchBtn) {
            this.elements.clearSearchBtn.style.display = query ? 'block' : 'none';
        }
    }

    // UI Update Methods
    /**
     * Switch to a specific tab
     */
    switchToTab(tabId) {
        if (this.currentTab === tabId) return;

        // Update tab buttons
        this.elements.tabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabId);
        });

        // Update tab panels
        this.elements.tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `${tabId}-tab`);
        });

        this.currentTab = tabId;

        // Update combination selects when switching to combinations tab
        if (tabId === 'combinations') {
            this.updateCombinationSelects();
        }
    }

    /**
     * Update all counts
     */
    updateAllCounts() {
        const counts = this.dataManager.getCounts();

        // Update tab badges
        if (this.elements.cardsCount) {
            this.elements.cardsCount.textContent = counts.availableCards;
        }

        if (this.elements.emailsCount) {
            this.elements.emailsCount.textContent = counts.totalEmails;
        }

        if (this.elements.combinationsCount) {
            this.elements.combinationsCount.textContent = counts.combinations;
        }

        // Update status bar
        if (this.elements.totalCards) {
            this.elements.totalCards.textContent = counts.cards;
        }

        if (this.elements.totalEmails) {
            this.elements.totalEmails.textContent = counts.totalEmails;
        }

        if (this.elements.totalCombinations) {
            this.elements.totalCombinations.textContent = counts.combinations;
        }
    }

    /**
     * Update status message
     */
    updateStatus(message) {
        if (this.elements.statusMessage) {
            this.elements.statusMessage.textContent = message;
        }
    }

    /**
     * Update save status
     */
    updateSaveStatus(status) {
        if (this.elements.saveStatus) {
            const statusText = status === 'saved' ? 'Saved' : 'Saving...';
            this.elements.saveStatus.textContent = statusText;
            this.elements.saveStatus.className = `save-status ${status}`;
        }
    }

    /**
     * Refresh all lists
     */
    refreshAllLists() {
        this.refreshCardLists();
        this.refreshEmailLists();
        this.refreshCombinationsList();
        this.updateAllCounts();
    }

    /**
     * Refresh card lists
     */
    refreshCardLists() {
        if (this.isUpdating) return;
        this.isUpdating = true;

        try {
            // Available cards
            this.updateCardList(
                this.elements.availableCardsList,
                this.dataManager.getAvailableCards(),
                'available'
            );

            // Used cards
            this.updateCardList(
                this.elements.usedCardsList,
                Array.from(this.dataManager.usedCards),
                'used'
            );

            // Archived cards
            this.updateCardList(
                this.elements.archivedCardsList,
                Array.from(this.dataManager.archivedCards),
                'archived'
            );

            this.updateAllCounts();

        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * Refresh email lists
     */
    refreshEmailLists() {
        if (this.isUpdating) return;
        this.isUpdating = true;

        try {
            // PM emails
            this.updateEmailList(
                this.elements.pmEmailsList,
                this.dataManager.getAvailableEmails('pm_emails'),
                'pm_emails'
            );

            // UE emails 25/25
            this.updateEmailList(
                this.elements.ueEmails2525List,
                this.dataManager.getAvailableEmails('ue_emails_25_25'),
                'ue_emails_25_25'
            );

            // UE emails 25/15
            this.updateEmailList(
                this.elements.ueEmails2515List,
                this.dataManager.getAvailableEmails('ue_emails_25_15'),
                'ue_emails_25_15'
            );

            // UE emails 25/1
            this.updateEmailList(
                this.elements.ueEmails251List,
                this.dataManager.getAvailableEmails('ue_emails_25_1'),
                'ue_emails_25_1'
            );

            // Used emails
            this.updateEmailList(
                this.elements.usedEmailsList,
                Array.from(this.dataManager.usedEmails),
                'used'
            );

            // Archived emails
            this.updateEmailList(
                this.elements.archivedEmailsList,
                Array.from(this.dataManager.archivedEmails),
                'archived'
            );

            this.updateAllCounts();

        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * Update card list
     */
    updateCardList(container, cards, type) {
        if (!container) return;

        container.innerHTML = '';

        if (cards.length === 0) {
            const emptyMsg = createElement('div', { className: 'empty-message' },
                `No ${type} cards`);
            container.appendChild(emptyMsg);
            return;
        }

        cards.forEach(card => {
            const item = this.createCardListItem(card, type);
            container.appendChild(item);
        });
    }

    /**
     * Create card list item
     */
    createCardListItem(card, type) {
        const item = createElement('div', { className: 'list-item card-item' });

        const content = createElement('div', { className: 'item-content' });
        const cardText = createElement('span', { className: 'card-text' }, card);
        content.appendChild(cardText);

        const actions = createElement('div', { className: 'item-actions' });

        // Copy button
        const copyBtn = createElement('button', {
            className: 'btn btn-sm copy-btn',
            title: 'Copy card'
        }, '📋');
        copyBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(card);
            if (success) {
                this.toastManager.success('Copied', 'Card copied to clipboard');
            } else {
                this.toastManager.error('Error', 'Failed to copy card');
            }
        });
        actions.appendChild(copyBtn);

        // Type-specific buttons
        if (type === 'available') {
            // Archive button
            const archiveBtn = createElement('button', {
                className: 'btn btn-sm archive-btn',
                title: 'Archive card'
            }, '📦');
            archiveBtn.addEventListener('click', () => {
                this.dataManager.archiveCard(card);
                this.toastManager.success('Archived', 'Card archived');
            });
            actions.appendChild(archiveBtn);

            // Remove button
            const removeBtn = createElement('button', {
                className: 'btn btn-sm btn-danger remove-btn',
                title: 'Remove card'
            }, '🗑️');
            removeBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to remove this card?')) {
                    this.dataManager.removeCard(card);
                    this.toastManager.success('Removed', 'Card removed');
                }
            });
            actions.appendChild(removeBtn);

        } else if (type === 'archived') {
            // Restore button
            const restoreBtn = createElement('button', {
                className: 'btn btn-sm restore-btn',
                title: 'Restore card'
            }, '↩️');
            restoreBtn.addEventListener('click', () => {
                this.dataManager.restoreArchivedCard(card);
                this.toastManager.success('Restored', 'Card restored');
            });
            actions.appendChild(restoreBtn);
        }

        item.appendChild(content);
        item.appendChild(actions);

        return item;
    }

    /**
     * Update email list
     */
    updateEmailList(container, emails, type) {
        if (!container) return;

        container.innerHTML = '';

        if (emails.length === 0) {
            const emptyMsg = createElement('div', { className: 'empty-message' },
                `No ${type === 'used' ? 'used' : type === 'archived' ? 'archived' : 'available'} emails`);
            container.appendChild(emptyMsg);
            return;
        }

        emails.forEach(email => {
            const item = this.createEmailListItem(email, type);
            container.appendChild(item);
        });
    }

    /**
     * Create email list item
     */
    createEmailListItem(email, type) {
        const item = createElement('div', { className: 'list-item email-item' });

        const content = createElement('div', { className: 'item-content' });
        const emailText = createElement('span', { className: 'email-text' }, email);
        content.appendChild(emailText);

        const actions = createElement('div', { className: 'item-actions' });

        // Copy button
        const copyBtn = createElement('button', {
            className: 'btn btn-sm copy-btn',
            title: 'Copy email'
        }, '📋');
        copyBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(email);
            if (success) {
                this.toastManager.success('Copied', 'Email copied to clipboard');
            } else {
                this.toastManager.error('Error', 'Failed to copy email');
            }
        });
        actions.appendChild(copyBtn);

        // Type-specific buttons
        if (type !== 'used' && type !== 'archived') {
            // Remove button
            const removeBtn = createElement('button', {
                className: 'btn btn-sm btn-danger remove-btn',
                title: 'Remove email'
            }, '🗑️');
            removeBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to remove this email?')) {
                    this.dataManager.removeEmail(email, type);
                    this.toastManager.success('Removed', 'Email removed');
                }
            });
            actions.appendChild(removeBtn);
        }

        item.appendChild(content);
        item.appendChild(actions);

        return item;
    }

    /**
     * Refresh combinations list
     */
    refreshCombinationsList() {
        if (!this.elements.combinationsList) return;

        const combinations = this.dataManager.getCombinations();
        this.elements.combinationsList.innerHTML = '';

        if (combinations.length === 0) {
            const emptyMsg = createElement('div', { className: 'empty-message' },
                'No combinations created yet');
            this.elements.combinationsList.appendChild(emptyMsg);
            return;
        }

        combinations.forEach(combination => {
            const item = this.createCombinationItem(combination);
            this.elements.combinationsList.appendChild(item);
        });

        this.updateAllCounts();
    }

    /**
     * Create combination item
     */
    createCombinationItem(combination) {
        const item = createElement('div', { className: 'combination-item' });

        // Header with email type and timestamp
        const header = createElement('div', { className: 'combination-header' });
        const typeLabel = createElement('span', { className: 'email-type-label' },
            getEmailTypeDisplayName(combination.emailType));
        const timestamp = createElement('span', { className: 'timestamp' },
            new Date(combination.timestamp).toLocaleString());
        header.appendChild(typeLabel);
        header.appendChild(timestamp);

        // Content
        const content = createElement('div', { className: 'combination-content' });
        const cardText = createElement('div', { className: 'card-text' }, combination.card);
        const emailText = createElement('div', { className: 'email-text' }, combination.email);
        content.appendChild(cardText);
        content.appendChild(emailText);

        // Actions
        const actions = createElement('div', { className: 'combination-actions' });

        // Copy buttons
        const woolBtn = createElement('button', {
            className: 'btn btn-sm copy-btn wool-btn',
            title: 'Copy Wool format'
        }, 'Wool');
        woolBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(combination.woolFormat);
            if (success) {
                this.toastManager.success('Copied', 'Wool format copied');
            }
        });

        const fusionBtn = createElement('button', {
            className: 'btn btn-sm copy-btn fusion-btn',
            title: 'Copy Fusion format'
        }, 'Fusion');
        fusionBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(combination.fusionFormat);
            if (success) {
                this.toastManager.success('Copied', 'Fusion format copied');
            }
        });

        const otpBtn = createElement('button', {
            className: 'btn btn-sm copy-btn otp-btn',
            title: 'Copy OTP (email only)'
        }, 'OTP');
        otpBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(combination.otpFormat);
            if (success) {
                this.toastManager.success('Copied', 'OTP copied');
            }
        });

        // Delete button
        const deleteBtn = createElement('button', {
            className: 'btn btn-sm btn-danger delete-btn',
            title: 'Delete combination'
        }, '🗑️');
        deleteBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to delete this combination?')) {
                this.dataManager.deleteCombination(combination.id);
                this.toastManager.success('Deleted', 'Combination deleted');
            }
        });

        actions.appendChild(woolBtn);
        actions.appendChild(fusionBtn);
        actions.appendChild(otpBtn);
        actions.appendChild(deleteBtn);

        item.appendChild(header);
        item.appendChild(content);
        item.appendChild(actions);

        return item;
    }

    /**
     * Update combination selects
     */
    updateCombinationSelects() {
        const isAutoSelect = this.elements.autoSelectCheckbox?.checked;
        const emailType = this.elements.combinationEmailType?.value;

        // Show/hide manual selects based on auto-select
        const manualSelects = document.querySelector('.manual-selects');
        if (manualSelects) {
            manualSelects.style.display = isAutoSelect ? 'none' : 'block';
        }

        if (!isAutoSelect && emailType) {
            this.updateCardSelect();
            this.updateEmailSelect(emailType);
        }
    }

    /**
     * Update card select dropdown
     */
    updateCardSelect() {
        if (!this.elements.cardSelect) return;

        const availableCards = this.dataManager.getAvailableCards();
        this.elements.cardSelect.innerHTML = '<option value="">Select a card...</option>';

        availableCards.forEach(card => {
            const option = createElement('option', { value: card }, card);
            this.elements.cardSelect.appendChild(option);
        });
    }

    /**
     * Update email select dropdown
     */
    updateEmailSelect(emailType) {
        if (!this.elements.emailSelect) return;

        const availableEmails = this.dataManager.getAvailableEmails(emailType);
        this.elements.emailSelect.innerHTML = '<option value="">Select an email...</option>';

        availableEmails.forEach(email => {
            const option = createElement('option', { value: email }, email);
            this.elements.emailSelect.appendChild(option);
        });
    }

    /**
     * Show latest combination
     */
    showLatestCombination(combination) {
        if (!this.elements.latestCombination || !this.elements.latestCombinationContent) return;

        // Create combination display
        const display = createElement('div', { className: 'latest-combination-display' });

        // Email type
        const typeLabel = createElement('div', { className: 'email-type-label' },
            getEmailTypeDisplayName(combination.emailType));

        // Card and email
        const cardText = createElement('div', { className: 'card-text' }, combination.card);
        const emailText = createElement('div', { className: 'email-text' }, combination.email);

        // Copy buttons
        const actions = createElement('div', { className: 'combination-actions' });

        const woolBtn = createElement('button', { className: 'btn btn-sm wool-btn' }, 'Wool');
        woolBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(combination.woolFormat);
            if (success) this.toastManager.success('Copied', 'Wool format copied');
        });

        const fusionBtn = createElement('button', { className: 'btn btn-sm fusion-btn' }, 'Fusion');
        fusionBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(combination.fusionFormat);
            if (success) this.toastManager.success('Copied', 'Fusion format copied');
        });

        const otpBtn = createElement('button', { className: 'btn btn-sm otp-btn' }, 'OTP');
        otpBtn.addEventListener('click', async () => {
            const success = await copyToClipboard(combination.otpFormat);
            if (success) this.toastManager.success('Copied', 'OTP copied');
        });

        actions.appendChild(woolBtn);
        actions.appendChild(fusionBtn);
        actions.appendChild(otpBtn);

        display.appendChild(typeLabel);
        display.appendChild(cardText);
        display.appendChild(emailText);
        display.appendChild(actions);

        this.elements.latestCombinationContent.innerHTML = '';
        this.elements.latestCombinationContent.appendChild(display);

        // Show the latest combination box
        this.elements.latestCombination.classList.remove('hidden');

        // Add animation
        animateElement(display, 'fade-in');
    }

    // Copy Operations
    /**
     * Copy available cards
     */
    async copyAvailableCards(duplicate = false) {
        const cards = this.dataManager.getAvailableCards();
        if (cards.length === 0) {
            this.toastManager.warning('No Data', 'No available cards to copy');
            return;
        }

        let text = cards.join('\n');
        if (duplicate) {
            text = text + '\n' + text; // Duplicate the list
        }

        const success = await copyToClipboard(text);
        if (success) {
            const message = duplicate ? 'Available cards (2x) copied' : 'Available cards copied';
            this.toastManager.success('Copied', message);
        } else {
            this.toastManager.error('Error', 'Failed to copy cards');
        }
    }

    /**
     * Copy used cards
     */
    async copyUsedCards() {
        const cards = Array.from(this.dataManager.usedCards);
        if (cards.length === 0) {
            this.toastManager.warning('No Data', 'No used cards to copy');
            return;
        }

        const text = cards.join('\n');
        const success = await copyToClipboard(text);
        if (success) {
            this.toastManager.success('Copied', 'Used cards copied');
        } else {
            this.toastManager.error('Error', 'Failed to copy used cards');
        }
    }

    /**
     * Copy emails by type
     */
    async copyEmails(type, duplicate = false) {
        const emails = this.dataManager.getAvailableEmails(type);
        if (emails.length === 0) {
            this.toastManager.warning('No Data', `No available emails of type ${getEmailTypeDisplayName(type)}`);
            return;
        }

        let text = emails.join('\n');
        if (duplicate) {
            text = text + '\n' + text; // Duplicate the list
        }

        const success = await copyToClipboard(text);
        if (success) {
            const typeName = getEmailTypeDisplayName(type);
            const message = duplicate ? `${typeName} (2x) copied` : `${typeName} copied`;
            this.toastManager.success('Copied', message);
        } else {
            this.toastManager.error('Error', 'Failed to copy emails');
        }
    }

    // Clear Operations
    /**
     * Clear all cards
     */
    clearAllCards() {
        if (!confirm('Are you sure you want to clear all cards? This cannot be undone.')) {
            return;
        }

        const count = this.dataManager.cards.size;
        this.dataManager.cards.clear();
        this.dataManager.saveData();

        this.refreshCardLists();
        this.toastManager.success('Cleared', `${count} cards cleared`);
    }

    /**
     * Clear all emails
     */
    clearAllEmails() {
        if (!confirm('Are you sure you want to clear all emails? This cannot be undone.')) {
            return;
        }

        let totalCount = 0;
        totalCount += this.dataManager.pmEmails.size;
        totalCount += this.dataManager.ueEmails2525.size;
        totalCount += this.dataManager.ueEmails2515.size;
        totalCount += this.dataManager.ueEmails251.size;

        this.dataManager.pmEmails.clear();
        this.dataManager.ueEmails2525.clear();
        this.dataManager.ueEmails2515.clear();
        this.dataManager.ueEmails251.clear();
        this.dataManager.saveData();

        this.refreshEmailLists();
        this.toastManager.success('Cleared', `${totalCount} emails cleared`);
    }

    /**
     * Clear specific email type
     */
    clearEmailType(type) {
        const typeName = getEmailTypeDisplayName(type);
        if (!confirm(`Are you sure you want to clear all ${typeName}? This cannot be undone.`)) {
            return;
        }

        const count = this.dataManager.clearEmailType(type);
        this.refreshEmailLists();
        this.toastManager.success('Cleared', `${count} emails cleared from ${typeName}`);
    }

    /**
     * Remove used cards
     */
    removeUsedCards() {
        if (!confirm('Are you sure you want to remove all used cards?')) {
            return;
        }

        const count = this.dataManager.removeAllUsedCards();
        this.refreshCardLists();
        this.toastManager.success('Removed', `${count} used cards removed`);
    }

    /**
     * Remove used emails
     */
    removeUsedEmails() {
        if (!confirm('Are you sure you want to remove all used emails?')) {
            return;
        }

        const count = this.dataManager.removeAllUsedEmails();
        this.refreshEmailLists();
        this.toastManager.success('Removed', `${count} used emails removed`);
    }

    /**
     * Restore archived cards
     */
    restoreArchivedCards() {
        const count = this.dataManager.restoreAllArchivedCards();
        if (count > 0) {
            this.refreshCardLists();
            this.toastManager.success('Restored', `${count} archived cards restored`);
        } else {
            this.toastManager.info('No Data', 'No archived cards to restore');
        }
    }

    /**
     * Restore archived emails
     */
    restoreArchivedEmails() {
        const count = this.dataManager.restoreAllArchivedEmails();
        if (count > 0) {
            this.refreshEmailLists();
            this.toastManager.success('Restored', `${count} archived emails restored`);
        } else {
            this.toastManager.info('No Data', 'No archived emails to restore');
        }
    }

    /**
     * Clear all combinations
     */
    clearAllCombinations() {
        if (!confirm('Are you sure you want to clear all combinations? This cannot be undone.')) {
            return;
        }

        const count = this.dataManager.clearAllCombinations();
        this.refreshCombinationsList();
        this.refreshCardLists();
        this.refreshEmailLists();

        // Hide latest combination
        if (this.elements.latestCombination) {
            this.elements.latestCombination.classList.add('hidden');
        }

        this.toastManager.success('Cleared', `${count} combinations cleared`);
    }

    // Modal Operations
    /**
     * Show bulk add modal
     */
    showBulkAddModal(type) {
        if (!this.modalManager) return;

        const title = type === 'cards' ? 'Bulk Add Cards' : 'Bulk Add Emails';
        const placeholder = type === 'cards'
            ? 'Enter cards (one per line)\nFormat: number,month,year,cvv,zip'
            : 'Enter emails (one per line)';

        this.modalManager.showBulkAddModal(title, placeholder, (data, emailType) => {
            this.handleBulkAdd(type, data, emailType);
        });
    }

    /**
     * Handle bulk add operation
     */
    async handleBulkAdd(type, data, emailType) {
        try {
            let results;

            if (type === 'cards') {
                const cards = data.trim().split('\n').filter(line => line.trim());
                results = this.dataManager.addCards(cards);
            } else {
                const emails = data.trim().split('\n').filter(line => line.trim());
                results = this.dataManager.addEmails(emails, emailType);
            }

            // Show results
            const { added, errors } = results;
            let message = `${added.length} ${type} added successfully`;

            if (errors.length > 0) {
                message += `, ${errors.length} errors`;
                console.warn('Bulk add errors:', errors);
            }

            if (added.length > 0) {
                this.toastManager.success('Bulk Add Complete', message);
            } else if (errors.length > 0) {
                this.toastManager.error('Bulk Add Failed', `${errors.length} errors occurred`);
            }

        } catch (error) {
            this.toastManager.error('Error', error.message);
        }
    }

    /**
     * Show import modal
     */
    showImportModal() {
        if (!this.modalManager) return;

        this.modalManager.showImportModal((data) => {
            this.handleImport(data);
        });
    }

    /**
     * Handle import operation
     */
    async handleImport(data) {
        try {
            this.dataManager.importData(data);
            this.toastManager.success('Import Complete', 'Data imported successfully');
        } catch (error) {
            this.toastManager.error('Import Failed', error.message);
        }
    }

    /**
     * Export data
     */
    exportData() {
        try {
            const data = this.dataManager.exportData();
            const jsonString = JSON.stringify(data, null, 2);
            const filename = `card-manager-export-${new Date().toISOString().split('T')[0]}.json`;

            // Create download
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = createElement('a', {
                href: url,
                download: filename,
                style: 'display: none'
            });

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            this.toastManager.success('Export Complete', 'Data exported successfully');

        } catch (error) {
            this.toastManager.error('Export Failed', error.message);
        }
    }

    /**
     * Show settings modal
     */
    showSettingsModal() {
        if (!this.modalManager) return;

        this.modalManager.showSettingsModal((settings) => {
            this.handleSettingsUpdate(settings);
        });
    }

    /**
     * Handle settings update
     */
    handleSettingsUpdate(settings) {
        if (this.configManager) {
            this.configManager.updateConfig(settings);
            this.toastManager.success('Settings Updated', 'Settings saved successfully');
        }
    }

    // Utility Methods
    /**
     * Handle resize
     */
    handleResize() {
        // Update responsive elements if needed
        this.updateAllCounts();
    }

    /**
     * Highlight search results
     */
    highlightSearchResults(query) {
        if (!query) {
            this.clearSearchHighlights();
            return;
        }

        // Highlight in all visible lists
        const lists = [
            this.elements.availableCardsList,
            this.elements.usedCardsList,
            this.elements.archivedCardsList,
            this.elements.pmEmailsList,
            this.elements.ueEmails2525List,
            this.elements.ueEmails2515List,
            this.elements.ueEmails251List,
            this.elements.usedEmailsList,
            this.elements.archivedEmailsList,
            this.elements.combinationsList
        ];

        lists.forEach(list => {
            if (list) {
                this.highlightInElement(list, query);
            }
        });
    }

    /**
     * Highlight text in element
     */
    highlightInElement(element, query) {
        const items = element.querySelectorAll('.list-item, .combination-item');
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');

        items.forEach(item => {
            const textElements = item.querySelectorAll('.card-text, .email-text');
            let hasMatch = false;

            textElements.forEach(textEl => {
                const originalText = textEl.textContent;
                if (regex.test(originalText)) {
                    hasMatch = true;
                    const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                    textEl.innerHTML = highlightedText;
                }
            });

            // Show/hide item based on match
            item.style.display = hasMatch ? 'block' : 'none';
        });
    }

    /**
     * Clear search highlights
     */
    clearSearchHighlights() {
        const highlights = document.querySelectorAll('mark');
        highlights.forEach(mark => {
            const parent = mark.parentNode;
            parent.replaceChild(document.createTextNode(mark.textContent), mark);
            parent.normalize();
        });

        // Show all items
        const items = document.querySelectorAll('.list-item, .combination-item');
        items.forEach(item => {
            item.style.display = 'block';
        });
    }

    /**
     * Update undo/redo button states
     */
    updateHistoryButtons() {
        if (!this.historyManager) return;

        if (this.elements.undoBtn) {
            this.elements.undoBtn.disabled = !this.historyManager.canUndo();
        }

        if (this.elements.redoBtn) {
            this.elements.redoBtn.disabled = !this.historyManager.canRedo();
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        // Remove event listeners
        this.elements.tabBtns?.forEach(btn => {
            btn.removeEventListener('click', this.handleTabChange);
        });

        // Clear element cache
        this.elements = {};

        // Clear manager references
        this.searchManager = null;
        this.historyManager = null;
        this.modalManager = null;
        this.configManager = null;
    }
}
