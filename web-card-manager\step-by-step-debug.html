<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step-by-Step Debug</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 20px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            border-left: 3px solid #00ff00;
        }
        .debug-step {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #333;
        }
        .debug-step.pending {
            background: rgba(255, 255, 0, 0.1);
            border-left-color: #ffff00;
        }
        .debug-step.success {
            background: rgba(0, 255, 0, 0.1);
            border-left-color: #00ff00;
        }
        .debug-step.error {
            background: rgba(255, 0, 0, 0.1);
            border-left-color: #ff0000;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .step-details {
            font-size: 11px;
            color: #ccc;
        }
        .main-app-debug {
            margin-right: 420px;
        }
    </style>
</head>
<body>
    <div id="app" class="app main-app-debug">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Loading Card Manager...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <!-- Header Toolbar -->
            <header class="toolbar">
                <div class="toolbar-left">
                    <h1 class="app-title">
                        <i class="fas fa-credit-card"></i>
                        Card Manager
                    </h1>
                </div>
                <div class="toolbar-center">
                    <div class="search-container">
                        <input type="text" id="global-search" class="search-input" placeholder="Search cards, emails, combinations...">
                        <button id="clear-search" class="icon-btn" title="Clear Search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="toolbar-right">
                    <button id="undo-btn" class="icon-btn" title="Undo" disabled>
                        <i class="fas fa-undo"></i>
                    </button>
                    <button id="redo-btn" class="icon-btn" title="Redo" disabled>
                        <i class="fas fa-redo"></i>
                    </button>
                    <button id="import-btn" class="icon-btn" title="Import Data">
                        <i class="fas fa-file-import"></i>
                    </button>
                    <button id="export-btn" class="icon-btn" title="Export Data">
                        <i class="fas fa-file-export"></i>
                    </button>
                    <button id="settings-btn" class="icon-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="main-content">
                <!-- Tab Navigation -->
                <nav class="tab-navigation">
                    <button class="tab-btn active" data-tab="cards">
                        <i class="fas fa-credit-card"></i>
                        Cards
                        <span class="tab-count" id="cards-count">0</span>
                    </button>
                    <button class="tab-btn" data-tab="emails">
                        <i class="fas fa-envelope"></i>
                        Emails
                        <span class="tab-count" id="emails-count">0</span>
                    </button>
                    <button class="tab-btn" data-tab="combinations">
                        <i class="fas fa-link"></i>
                        Combinations
                        <span class="tab-count" id="combinations-count">0</span>
                    </button>
                </nav>

                <!-- Cards Tab -->
                <div class="tab-panel active" id="cards-tab">
                    <div class="tab-content">
                        <!-- Card Input Section -->
                        <section class="input-section">
                            <h3>Add Cards</h3>
                            <div class="input-group">
                                <input 
                                    type="text" 
                                    id="card-input" 
                                    class="form-input" 
                                    placeholder="Enter card: number,month,year,cvv,zip"
                                    autocomplete="off"
                                >
                                <button id="add-card-btn" class="primary-btn">
                                    <i class="fas fa-plus"></i>
                                    Add Card
                                </button>
                            </div>
                        </section>

                        <!-- Available Cards -->
                        <section class="list-section">
                            <h3>Available Cards</h3>
                            <div class="list-container">
                                <div class="list" id="available-cards-list"></div>
                            </div>
                        </section>
                    </div>
                </div>

                <!-- Minimal other tabs for testing -->
                <div class="tab-panel" id="emails-tab">
                    <div class="tab-content">
                        <section class="input-section">
                            <h3>Add Email</h3>
                            <div class="input-group">
                                <input 
                                    type="email" 
                                    id="email-input" 
                                    class="form-input" 
                                    placeholder="Enter email address"
                                    autocomplete="off"
                                >
                                <select id="email-type-select" class="form-select">
                                    <option value="pm3035">PM 30/35</option>
                                    <option value="ue2525">UE 25/25</option>
                                    <option value="ue2515">UE 25/15</option>
                                    <option value="ue251">UE 25/1</option>
                                </select>
                                <button id="add-email-btn" class="primary-btn">
                                    <i class="fas fa-plus"></i>
                                    Add Email
                                </button>
                            </div>
                        </section>
                        <div id="pm-emails-list"></div>
                        <div id="ue-emails-25-25-list"></div>
                        <div id="ue-emails-25-15-list"></div>
                        <div id="ue-emails-25-1-list"></div>
                        <div id="used-emails-list"></div>
                        <div id="archived-emails-list"></div>
                    </div>
                </div>

                <div class="tab-panel" id="combinations-tab">
                    <div class="tab-content">
                        <div id="card-select"></div>
                        <div id="email-select"></div>
                        <div id="combination-email-type"></div>
                        <button id="combine-btn">Combine</button>
                        <input type="checkbox" id="auto-select-checkbox">
                        <div id="combinations-list"></div>
                        <div id="latest-combination"></div>
                        <div id="latest-combination-content"></div>
                    </div>
                </div>
            </main>

            <!-- Status Bar -->
            <footer class="status-bar">
                <div class="status-left">
                    <span id="status-message">Ready</span>
                </div>
                <div class="status-right">
                    <span id="save-status">Auto-save: On</span>
                    <span id="total-cards">Cards: 0</span>
                    <span id="total-emails">Emails: 0</span>
                    <span id="total-combinations">Combinations: 0</span>
                </div>
            </footer>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Debug Panel -->
    <div class="debug-panel" id="debug-panel">
        <h3>🐛 Initialization Debug</h3>
        <div id="debug-steps"></div>
    </div>

    <script type="module">
        const debugSteps = document.getElementById('debug-steps');
        let currentStep = 0;
        
        const steps = [
            'Import CardManagerApp',
            'Create App Instance',
            'Initialize Managers',
            'Connect Managers',
            'Setup Event Listeners',
            'Apply Initial Config',
            'Load Initial Data',
            'Show Application'
        ];

        function addDebugStep(title, status = 'pending', details = '') {
            const step = document.createElement('div');
            step.className = `debug-step ${status}`;
            step.innerHTML = `
                <div class="step-title">${title}</div>
                <div class="step-details">${details}</div>
            `;
            debugSteps.appendChild(step);
            return step;
        }

        function updateStep(step, status, details = '') {
            step.className = `debug-step ${status}`;
            if (details) {
                step.querySelector('.step-details').textContent = details;
            }
        }

        async function debugInitialization() {
            const stepElements = [];
            
            // Add all steps as pending
            steps.forEach(stepName => {
                stepElements.push(addDebugStep(stepName, 'pending'));
            });

            try {
                // Step 1: Import CardManagerApp
                updateStep(stepElements[0], 'pending', 'Importing main application...');
                const { CardManagerApp } = await import('./js/main.js');
                updateStep(stepElements[0], 'success', 'CardManagerApp imported successfully');

                // Step 2: Create App Instance
                updateStep(stepElements[1], 'pending', 'Creating application instance...');
                const app = new CardManagerApp();
                updateStep(stepElements[1], 'success', 'App instance created');

                // Step 3: Initialize Managers (we'll intercept this)
                updateStep(stepElements[2], 'pending', 'Initializing managers...');
                
                // Override the init method to add debugging
                const originalInit = app.init.bind(app);
                app.init = async function() {
                    try {
                        console.log('🚀 Starting initialization...');
                        updateStep(stepElements[2], 'pending', 'Creating managers...');
                        
                        // Initialize managers
                        await this.initializeManagers();
                        updateStep(stepElements[2], 'success', 'All managers initialized');
                        
                        // Connect managers
                        updateStep(stepElements[3], 'pending', 'Connecting managers...');
                        this.connectManagers();
                        updateStep(stepElements[3], 'success', 'Managers connected');
                        
                        // Setup event listeners
                        updateStep(stepElements[4], 'pending', 'Setting up event listeners...');
                        this.setupEventListeners();
                        updateStep(stepElements[4], 'success', 'Event listeners set up');
                        
                        // Apply initial config
                        updateStep(stepElements[5], 'pending', 'Applying configuration...');
                        await this.applyInitialConfig();
                        updateStep(stepElements[5], 'success', 'Configuration applied');
                        
                        // Load initial data
                        updateStep(stepElements[6], 'pending', 'Loading saved data...');
                        await this.loadInitialData();
                        updateStep(stepElements[6], 'success', 'Data loaded');
                        
                        // Show application
                        updateStep(stepElements[7], 'pending', 'Showing application...');
                        await this.showApplication();
                        updateStep(stepElements[7], 'success', 'Application shown successfully!');
                        
                        console.log('✅ Initialization completed successfully!');
                        
                    } catch (error) {
                        const currentStepIndex = stepElements.findIndex(step => 
                            step.className.includes('pending')
                        );
                        if (currentStepIndex >= 0) {
                            updateStep(stepElements[currentStepIndex], 'error', 
                                `Error: ${error.message}`);
                        }
                        console.error('❌ Initialization failed:', error);
                        throw error;
                    }
                };

                // Start initialization
                await app.init();

            } catch (error) {
                console.error('Debug initialization failed:', error);
                addDebugStep('FATAL ERROR', 'error', error.message);
            }
        }

        // Start debugging when DOM is ready
        document.addEventListener('DOMContentLoaded', debugInitialization);
    </script>
</body>
</html>
