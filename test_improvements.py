"""
Test script to verify the card manager improvements work correctly.

This script tests the new modules and performance optimizations.
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager
from utils import validate_card, validate_email, parse_bulk_cards, parse_bulk_emails
from ui_manager import UIManager, WidgetCache
from error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, BackupManager
from config_manager import ConfigManager, AppConfig
from search_manager import SearchManager, SearchFilter, SearchType
from history_manager import HistoryManager, ActionType

class TestCardManagerImprovements(unittest.TestCase):
    """Test suite for card manager improvements."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.data_manager = DataManager()
        self.config_manager = ConfigManager()
        self.error_handler = ErrorHandler()
        
        # Mock app for UI manager
        self.mock_app = Mock()
        self.mock_app.data_manager = self.data_manager
        self.mock_app.config_manager = self.config_manager
        
        self.ui_manager = UIManager(self.mock_app)
        self.search_manager = SearchManager(self.data_manager)
        self.history_manager = HistoryManager(self.data_manager)
    
    def test_card_validation(self):
        """Test card validation functionality."""
        # Valid card
        valid_card = "****************,12,25,123,12345"
        is_valid, error = validate_card(valid_card)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # Invalid card - wrong format
        invalid_card = "****************"
        is_valid, error = validate_card(invalid_card)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # Invalid card - empty
        is_valid, error = validate_card("")
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_email_validation(self):
        """Test email validation functionality."""
        # Valid email
        valid_email = "<EMAIL>"
        is_valid = validate_email(valid_email)
        self.assertTrue(is_valid)

        # Invalid email
        invalid_email = "not-an-email"
        is_valid = validate_email(invalid_email)
        self.assertFalse(is_valid)
    
    def test_bulk_parsing(self):
        """Test bulk parsing functionality."""
        # Test bulk card parsing
        card_text = "****************,12,25,123,12345\n****************,01,26,456,67890"
        valid_cards, invalid_cards = parse_bulk_cards(card_text)

        self.assertEqual(len(valid_cards), 2)
        self.assertEqual(len(invalid_cards), 0)

        # Test bulk email parsing
        email_text = "<EMAIL>\<EMAIL>\ninvalid-email"
        valid_emails, invalid_emails = parse_bulk_emails(email_text)

        self.assertEqual(len(valid_emails), 2)
        self.assertEqual(len(invalid_emails), 1)
    
    def test_config_manager(self):
        """Test configuration management."""
        # Test auto-save setting
        self.assertTrue(self.config_manager.is_auto_save_enabled())

        # Test theme setting
        self.config_manager.set_theme("light")
        self.assertEqual(self.config_manager.get_theme(), "light")

        # Test window geometry
        self.config_manager.set_window_geometry(1000, 700, 100, 50)
        geometry = self.config_manager.get_window_geometry()
        self.assertEqual(geometry['width'], 1000)
        self.assertEqual(geometry['height'], 700)
    
    def test_search_functionality(self):
        """Test search and filtering functionality."""
        # Add test data
        self.data_manager.cards = [
            "****************,12,25,123,12345",
            "****************,01,26,456,67890"
        ]
        self.data_manager.emails = ["<EMAIL>", "<EMAIL>"]
        
        # Test card search
        search_filter = SearchFilter(query="4111", search_type=SearchType.CONTAINS)
        results = self.search_manager.search_cards(search_filter)
        
        self.assertEqual(len(results), 1)
        self.assertIn("****************", results[0].item)
        
        # Test email search
        search_filter = SearchFilter(query="gmail", search_type=SearchType.CONTAINS)
        results = self.search_manager.search_emails(search_filter)
        
        self.assertEqual(len(results), 1)
        self.assertIn("gmail", results[0].item)
    
    def test_history_manager(self):
        """Test undo/redo functionality."""
        # Test recording an action
        self.history_manager.record_action(
            ActionType.ADD_CARD,
            "Add test card",
            {'card': '****************,12,25,123,12345'},
            {}
        )
        
        self.assertTrue(self.history_manager.can_undo())
        self.assertFalse(self.history_manager.can_redo())
        
        # Test undo description
        description = self.history_manager.get_undo_description()
        self.assertEqual(description, "Add test card")
    
    def test_ui_manager_caching(self):
        """Test UI manager widget caching."""
        # Test widget cache creation
        cache = WidgetCache()
        self.assertIsNone(cache.available_cards_frame)
        self.assertIsNone(cache.used_cards_frame)

        # Test state tracking
        self.assertFalse(self.ui_manager.has_data_changed())

        # Simulate data change
        self.ui_manager.mark_data_changed()
        self.assertTrue(self.ui_manager.has_data_changed())
    
    def test_error_handler(self):
        """Test error handling and backup functionality."""
        # Test backup manager
        backup_manager = BackupManager()

        # Test backup creation (may succeed if data file exists)
        try:
            result = backup_manager.create_backup("test")
            # Should return True or False depending on whether data file exists
            self.assertIsInstance(result, bool)
        except Exception as e:
            self.fail(f"Backup creation should not raise exception: {e}")
    
    def test_data_integrity(self):
        """Test data integrity validation."""
        # Test with valid data
        test_data = {
            'cards': ['****************,12,25,123,12345'],
            'emails': ['<EMAIL>'],
            'combinations': [],
            'used_cards': [],
            'used_emails': [],
            'all_used_cards': [],
            'all_used_emails': [],
            'archived_cards': [],
            'archived_emails': []
        }

        # Test that error handler can be created without crashing
        self.assertIsNotNone(self.error_handler)

        # Test safe data loading (will return default values if no file exists)
        success, data, error_msg = self.error_handler.safe_load_data()
        self.assertIsInstance(success, bool)
        self.assertIsInstance(data, (dict, type(None)))

def run_tests():
    """Run all tests and display results."""
    print("Running Card Manager Improvement Tests...")
    print("=" * 50)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCardManagerImprovements)
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ All tests passed! The improvements are working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
