<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Card Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .debug-info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
        .warning {
            color: #ffd43b;
        }
    </style>
</head>
<body>
    <h1>Card Manager Debug</h1>
    <div id="debug-output" class="debug-info">
        <p>Starting debug...</p>
    </div>

    <script type="module">
        const debugOutput = document.getElementById('debug-output');
        
        function log(message, type = 'info') {
            const p = document.createElement('p');
            p.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            p.className = type;
            debugOutput.appendChild(p);
            console.log(message);
        }

        async function testModuleImports() {
            log('Testing module imports...', 'info');
            
            try {
                log('Importing DataManager...', 'info');
                const { DataManager } = await import('./js/modules/DataManager.js');
                log('✓ DataManager imported successfully', 'success');
                
                log('Importing UIManager...', 'info');
                const { UIManager } = await import('./js/modules/UIManager.js');
                log('✓ UIManager imported successfully', 'success');
                
                log('Importing ToastManager...', 'info');
                const { ToastManager } = await import('./js/modules/ToastManager.js');
                log('✓ ToastManager imported successfully', 'success');
                
                log('Importing ModalManager...', 'info');
                const { ModalManager } = await import('./js/modules/ModalManager.js');
                log('✓ ModalManager imported successfully', 'success');
                
                log('Importing SearchManager...', 'info');
                const { SearchManager } = await import('./js/modules/SearchManager.js');
                log('✓ SearchManager imported successfully', 'success');
                
                log('Importing HistoryManager...', 'info');
                const { HistoryManager } = await import('./js/modules/HistoryManager.js');
                log('✓ HistoryManager imported successfully', 'success');
                
                log('Importing ConfigManager...', 'info');
                const { ConfigManager } = await import('./js/modules/ConfigManager.js');
                log('✓ ConfigManager imported successfully', 'success');
                
                log('Importing AutoSaveManager...', 'info');
                const { AutoSaveManager } = await import('./js/modules/AutoSaveManager.js');
                log('✓ AutoSaveManager imported successfully', 'success');
                
                log('Importing validation utilities...', 'info');
                const validation = await import('./js/utils/validation.js');
                log('✓ Validation utilities imported successfully', 'success');
                
                log('Importing helper utilities...', 'info');
                const helpers = await import('./js/utils/helpers.js');
                log('✓ Helper utilities imported successfully', 'success');
                
                log('All modules imported successfully!', 'success');
                
                // Test basic instantiation
                log('Testing basic instantiation...', 'info');
                
                const configManager = new ConfigManager();
                log('✓ ConfigManager instantiated', 'success');
                
                const dataManager = new DataManager();
                log('✓ DataManager instantiated', 'success');
                
                const toastManager = new ToastManager();
                log('✓ ToastManager instantiated', 'success');
                
                // Test initialization
                log('Testing initialization...', 'info');
                
                await configManager.init();
                log('✓ ConfigManager initialized', 'success');
                
                await toastManager.init();
                log('✓ ToastManager initialized', 'success');
                
                await dataManager.init();
                log('✓ DataManager initialized', 'success');
                
                log('All tests passed! The modules are working correctly.', 'success');
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }

        // Run tests
        testModuleImports();
    </script>
</body>
</html>
