"""
Test script to verify what users actually see in the Card Manager application.
This script will create the application and check if the toolbar and improvements are visible.
"""
import sys
import os
import customtkinter as ctk
import time

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_elements():
    """Test if UI elements are actually created and visible."""
    print("🔍 Testing Card Manager UI Elements...")
    
    try:
        from card_manager import CardManagerApp
        
        # Create a test root window
        root = ctk.CTk()
        root.withdraw()  # Hide the window for testing
        
        # Create the app
        app = CardManagerApp(root)
        
        # Check if toolbar elements exist
        toolbar_elements = []
        
        if hasattr(app, 'undo_button'):
            toolbar_elements.append("✅ Undo button")
        else:
            toolbar_elements.append("❌ Undo button MISSING")
            
        if hasattr(app, 'redo_button'):
            toolbar_elements.append("✅ Redo button")
        else:
            toolbar_elements.append("❌ Redo button MISSING")
            
        if hasattr(app, 'search_entry'):
            toolbar_elements.append("✅ Search entry")
        else:
            toolbar_elements.append("❌ Search entry MISSING")
            
        if hasattr(app, 'search_button'):
            toolbar_elements.append("✅ Search button")
        else:
            toolbar_elements.append("❌ Search button MISSING")
            
        if hasattr(app, 'performance_label'):
            toolbar_elements.append("✅ Performance indicator")
        else:
            toolbar_elements.append("❌ Performance indicator MISSING")
        
        # Check if methods exist
        method_checks = []
        
        if hasattr(app, 'create_toolbar'):
            method_checks.append("✅ create_toolbar method")
        else:
            method_checks.append("❌ create_toolbar method MISSING")
            
        if hasattr(app, 'on_tab_change'):
            method_checks.append("✅ on_tab_change method")
        else:
            method_checks.append("❌ on_tab_change method MISSING")
            
        if hasattr(app, 'undo_action'):
            method_checks.append("✅ undo_action method")
        else:
            method_checks.append("❌ undo_action method MISSING")
            
        if hasattr(app, 'perform_search'):
            method_checks.append("✅ perform_search method")
        else:
            method_checks.append("❌ perform_search method MISSING")
        
        # Print results
        print("\n📋 Toolbar Elements:")
        for element in toolbar_elements:
            print(f"   {element}")
            
        print("\n📋 Methods:")
        for method in method_checks:
            print(f"   {method}")
        
        # Check theme
        current_mode = ctk.get_appearance_mode()
        print(f"\n🎨 Theme: {current_mode}")
        
        # Test tab change performance
        print("\n⚡ Testing tab change performance...")
        start_time = time.time()
        
        # Simulate tab changes
        for _ in range(5):
            app.on_tab_change()
        
        end_time = time.time()
        tab_change_time = end_time - start_time
        
        print(f"   5 tab changes took: {tab_change_time:.4f} seconds")
        print(f"   Average per tab change: {tab_change_time/5:.4f} seconds")
        
        if tab_change_time < 0.1:
            print("   ✅ Tab switching performance: EXCELLENT")
        elif tab_change_time < 0.5:
            print("   ✅ Tab switching performance: GOOD")
        else:
            print("   ⚠️  Tab switching performance: SLOW")
        
        # Clean up
        root.destroy()
        
        # Count successes
        success_count = sum(1 for item in toolbar_elements + method_checks if "✅" in item)
        total_count = len(toolbar_elements) + len(method_checks)
        
        print(f"\n📊 UI Test Results: {success_count}/{total_count} elements found")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ UI test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_regression():
    """Test if there are any obvious performance regressions."""
    print("\n🚀 Testing Performance Regression...")
    
    try:
        # Test import time
        start_time = time.time()
        from card_manager import CardManagerApp
        import_time = time.time() - start_time
        
        print(f"   Import time: {import_time:.4f} seconds")
        
        if import_time < 1.0:
            print("   ✅ Import performance: GOOD")
        else:
            print("   ⚠️  Import performance: SLOW")
        
        # Test initialization time
        root = ctk.CTk()
        root.withdraw()
        
        start_time = time.time()
        app = CardManagerApp(root)
        init_time = time.time() - start_time
        
        print(f"   Initialization time: {init_time:.4f} seconds")
        
        if init_time < 2.0:
            print("   ✅ Initialization performance: GOOD")
        else:
            print("   ⚠️  Initialization performance: SLOW")
        
        root.destroy()
        
        return import_time < 1.0 and init_time < 2.0
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Card Manager Actual UI Test")
    print("=" * 50)
    
    ui_test_passed = test_ui_elements()
    perf_test_passed = test_performance_regression()
    
    print("\n" + "=" * 50)
    print("📋 FINAL RESULTS:")
    
    if ui_test_passed:
        print("✅ UI Elements: All toolbar elements are present and functional")
    else:
        print("❌ UI Elements: Some toolbar elements are missing")
    
    if perf_test_passed:
        print("✅ Performance: No significant regression detected")
    else:
        print("❌ Performance: Potential regression detected")
    
    if ui_test_passed and perf_test_passed:
        print("\n🎉 SUCCESS: The Card Manager improvements are working correctly!")
        print("   Users should see:")
        print("   • Toolbar at the top with undo/redo buttons")
        print("   • Search interface")
        print("   • Dark theme")
        print("   • Fast tab switching")
    else:
        print("\n⚠️  ISSUES DETECTED: Some improvements are not working as expected")
        
        if not ui_test_passed:
            print("   • Toolbar elements may not be visible to users")
        if not perf_test_passed:
            print("   • Performance may be worse than expected")
    
    return ui_test_passed and perf_test_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
