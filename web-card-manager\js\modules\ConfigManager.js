/**
 * Config Manager - Handles application configuration and settings
 */

export class ConfigManager {
    constructor() {
        this.config = {};
        this.defaultConfig = {
            theme: 'dark',
            autoSaveInterval: 30,
            maxHistorySize: 50,
            toastDuration: 4000,
            maxToasts: 5,
            searchDebounceDelay: 300,
            animationsEnabled: true,
            soundEnabled: false,
            compactMode: false,
            showTimestamps: true,
            confirmDeletions: true,
            autoSelectOldest: true,
            copyFormat: 'wool',
            language: 'en'
        };
        this.storageKey = 'card-manager-config';
    }

    /**
     * Initialize config manager
     */
    async init() {
        console.log('⚙️ Initializing Config Manager...');
        
        // Load config from storage
        this.loadConfig();
        
        // Apply initial theme
        this.applyTheme();
        
        console.log('✅ Config Manager initialized');
    }

    /**
     * Load configuration from localStorage
     */
    loadConfig() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const parsedConfig = JSON.parse(stored);
                this.config = { ...this.defaultConfig, ...parsedConfig };
            } else {
                this.config = { ...this.defaultConfig };
            }
        } catch (error) {
            console.error('Failed to load config:', error);
            this.config = { ...this.defaultConfig };
        }
    }

    /**
     * Save configuration to localStorage
     */
    saveConfig() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.config));
        } catch (error) {
            console.error('Failed to save config:', error);
        }
    }

    /**
     * Get configuration value
     */
    get(key) {
        return this.config[key] !== undefined ? this.config[key] : this.defaultConfig[key];
    }

    /**
     * Set configuration value
     */
    set(key, value) {
        this.config[key] = value;
        this.saveConfig();
        
        // Apply changes that need immediate effect
        this.applyConfigChange(key, value);
    }

    /**
     * Update multiple configuration values
     */
    updateConfig(updates) {
        const oldConfig = { ...this.config };
        
        Object.keys(updates).forEach(key => {
            this.config[key] = updates[key];
        });
        
        this.saveConfig();
        
        // Apply changes
        Object.keys(updates).forEach(key => {
            if (oldConfig[key] !== updates[key]) {
                this.applyConfigChange(key, updates[key]);
            }
        });
    }

    /**
     * Apply configuration change
     */
    applyConfigChange(key, value) {
        switch (key) {
            case 'theme':
                this.applyTheme();
                break;
                
            case 'animationsEnabled':
                this.applyAnimationSettings();
                break;
                
            case 'compactMode':
                this.applyCompactMode();
                break;
                
            case 'language':
                this.applyLanguage();
                break;
        }
    }

    /**
     * Apply theme
     */
    applyTheme() {
        const theme = this.get('theme');
        const root = document.documentElement;
        
        if (theme === 'auto') {
            // Use system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
        } else {
            root.setAttribute('data-theme', theme);
        }
    }

    /**
     * Apply animation settings
     */
    applyAnimationSettings() {
        const animationsEnabled = this.get('animationsEnabled');
        const root = document.documentElement;
        
        if (animationsEnabled) {
            root.classList.remove('no-animations');
        } else {
            root.classList.add('no-animations');
        }
    }

    /**
     * Apply compact mode
     */
    applyCompactMode() {
        const compactMode = this.get('compactMode');
        const root = document.documentElement;
        
        if (compactMode) {
            root.classList.add('compact-mode');
        } else {
            root.classList.remove('compact-mode');
        }
    }

    /**
     * Apply language settings
     */
    applyLanguage() {
        const language = this.get('language');
        document.documentElement.lang = language;
        
        // Here you would typically load language files
        // For now, we'll just set the lang attribute
    }

    /**
     * Reset to default configuration
     */
    resetToDefaults() {
        this.config = { ...this.defaultConfig };
        this.saveConfig();
        
        // Apply all changes
        Object.keys(this.defaultConfig).forEach(key => {
            this.applyConfigChange(key, this.defaultConfig[key]);
        });
    }

    /**
     * Get all configuration
     */
    getAll() {
        return { ...this.config };
    }

    /**
     * Get default configuration
     */
    getDefaults() {
        return { ...this.defaultConfig };
    }

    /**
     * Check if config has been modified from defaults
     */
    isModified() {
        return JSON.stringify(this.config) !== JSON.stringify(this.defaultConfig);
    }

    /**
     * Get modified keys
     */
    getModifiedKeys() {
        const modified = [];
        
        Object.keys(this.defaultConfig).forEach(key => {
            if (this.config[key] !== this.defaultConfig[key]) {
                modified.push(key);
            }
        });
        
        return modified;
    }

    /**
     * Export configuration
     */
    exportConfig() {
        return {
            config: this.getAll(),
            defaults: this.getDefaults(),
            exportedAt: Date.now(),
            version: '1.0.0'
        };
    }

    /**
     * Import configuration
     */
    importConfig(configData) {
        try {
            if (!configData || !configData.config) {
                throw new Error('Invalid config data');
            }
            
            // Validate config keys
            const validKeys = Object.keys(this.defaultConfig);
            const importedConfig = {};
            
            Object.keys(configData.config).forEach(key => {
                if (validKeys.includes(key)) {
                    importedConfig[key] = configData.config[key];
                }
            });
            
            // Merge with defaults
            this.config = { ...this.defaultConfig, ...importedConfig };
            this.saveConfig();
            
            // Apply all changes
            Object.keys(importedConfig).forEach(key => {
                this.applyConfigChange(key, importedConfig[key]);
            });
            
            return true;
            
        } catch (error) {
            console.error('Failed to import config:', error);
            return false;
        }
    }

    /**
     * Validate configuration value
     */
    validateValue(key, value) {
        switch (key) {
            case 'theme':
                return ['dark', 'light', 'auto'].includes(value);
                
            case 'autoSaveInterval':
                return typeof value === 'number' && value >= 5 && value <= 300;
                
            case 'maxHistorySize':
                return typeof value === 'number' && value >= 1 && value <= 100;
                
            case 'toastDuration':
                return typeof value === 'number' && value >= 1000 && value <= 10000;
                
            case 'maxToasts':
                return typeof value === 'number' && value >= 1 && value <= 10;
                
            case 'searchDebounceDelay':
                return typeof value === 'number' && value >= 100 && value <= 1000;
                
            case 'copyFormat':
                return ['wool', 'fusion', 'otp'].includes(value);
                
            case 'language':
                return ['en', 'es', 'fr', 'de'].includes(value);
                
            case 'animationsEnabled':
            case 'soundEnabled':
            case 'compactMode':
            case 'showTimestamps':
            case 'confirmDeletions':
            case 'autoSelectOldest':
                return typeof value === 'boolean';
                
            default:
                return true;
        }
    }

    /**
     * Set configuration value with validation
     */
    setWithValidation(key, value) {
        if (!this.validateValue(key, value)) {
            throw new Error(`Invalid value for ${key}: ${value}`);
        }
        
        this.set(key, value);
    }

    /**
     * Listen for system theme changes
     */
    setupThemeListener() {
        if (this.get('theme') === 'auto') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            const handleThemeChange = (e) => {
                if (this.get('theme') === 'auto') {
                    this.applyTheme();
                }
            };
            
            mediaQuery.addEventListener('change', handleThemeChange);
            
            // Return cleanup function
            return () => {
                mediaQuery.removeEventListener('change', handleThemeChange);
            };
        }
        
        return null;
    }

    /**
     * Get configuration schema for UI generation
     */
    getSchema() {
        return {
            theme: {
                type: 'select',
                label: 'Theme',
                options: [
                    { value: 'dark', label: 'Dark' },
                    { value: 'light', label: 'Light' },
                    { value: 'auto', label: 'Auto (System)' }
                ],
                default: 'dark'
            },
            autoSaveInterval: {
                type: 'number',
                label: 'Auto-save interval (seconds)',
                min: 5,
                max: 300,
                default: 30
            },
            maxHistorySize: {
                type: 'number',
                label: 'Maximum undo history size',
                min: 1,
                max: 100,
                default: 50
            },
            animationsEnabled: {
                type: 'checkbox',
                label: 'Enable animations',
                default: true
            },
            compactMode: {
                type: 'checkbox',
                label: 'Compact mode',
                default: false
            },
            confirmDeletions: {
                type: 'checkbox',
                label: 'Confirm deletions',
                default: true
            },
            autoSelectOldest: {
                type: 'checkbox',
                label: 'Auto-select oldest items',
                default: true
            }
        };
    }

    /**
     * Cleanup resources
     */
    destroy() {
        // Save final config
        this.saveConfig();
    }
}
