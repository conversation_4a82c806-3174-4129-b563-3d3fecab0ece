<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Card Formatting Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4f7cff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b5bdb;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-pass {
            background: #1a4d1a;
            color: #4ade80;
            border: 1px solid #4ade80;
        }
        .test-fail {
            background: #4d1a1a;
            color: #f87171;
            border: 1px solid #f87171;
        }
        textarea {
            width: 100%;
            height: 100px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
        }
        input {
            width: 100%;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Card Formatting Fix Test</h1>
        
        <div class="test-section">
            <h2>📋 Test Console</h2>
            <div id="log" class="log">Starting card formatting tests...\n</div>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h2>🎯 Automated Tests</h2>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="testSpecificCase()">Test Specific Case (****************,07,30,997,94112)</button>
            <button onclick="testBulkProcessing()">Test Bulk Processing</button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Manual Testing</h2>
            <h3>Single Card Test:</h3>
            <input type="text" id="single-card-input" placeholder="Enter card: number,month,year,cvv,zip" value="****************,07,30,997,94112">
            <button onclick="testSingleCard()">Test Single Card</button>
            
            <h3>Bulk Cards Test:</h3>
            <textarea id="bulk-cards-input" placeholder="Enter multiple cards, one per line">****************,07,30,997,94112
****************,12,25,456,90210
1234567890123456,01,99,789,12345</textarea>
            <button onclick="testBulkCards()">Test Bulk Cards</button>
        </div>
    </div>

    <script type="module">
        import { validateCard, formatCard, parseBulkCards } from './js/utils/validation.js';
        
        // Make functions available globally for button clicks
        window.validateCard = validateCard;
        window.formatCard = formatCard;
        window.parseBulkCards = parseBulkCards;

        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function addTestResult(testName, passed, expected, actual) {
            const resultsEl = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = passed ? 'test-result test-pass' : 'test-result test-fail';
            resultDiv.innerHTML = `
                <strong>${testName}:</strong> ${passed ? '✅ PASS' : '❌ FAIL'}<br>
                Expected: ${expected}<br>
                Actual: ${actual}
            `;
            resultsEl.appendChild(resultDiv);
        }

        window.clearLog = clearLog;

        window.runAllTests = function() {
            log('🚀 Running comprehensive card formatting tests...');
            document.getElementById('test-results').innerHTML = '';

            const testCases = [
                {
                    name: 'User Reported Issue',
                    input: '****************,07,30,997,94112',
                    expected: '****************,07,30,997,94112'
                },
                {
                    name: '2-digit year (25)',
                    input: '****************,12,25,456,90210',
                    expected: '****************,12,25,456,90210'
                },
                {
                    name: '2-digit year (99)',
                    input: '1234567890123456,01,99,789,12345',
                    expected: '1234567890123456,01,99,789,12345'
                },
                {
                    name: '4-digit year (2025)',
                    input: '9876543210987654,06,2025,321,54321',
                    expected: '9876543210987654,06,2025,321,54321'
                },
                {
                    name: 'Single digit month',
                    input: '1111222233334444,5,30,123,67890',
                    expected: '1111222233334444,05,30,123,67890'
                },
                {
                    name: '4-digit ZIP',
                    input: '****************,03,28,987,1234',
                    expected: '****************,03,28,987,01234'
                }
            ];

            let passedTests = 0;
            let totalTests = testCases.length;

            testCases.forEach(testCase => {
                try {
                    const result = formatCard(testCase.input);
                    const passed = result === testCase.expected;
                    
                    if (passed) {
                        passedTests++;
                        log(`✅ ${testCase.name}: PASS`);
                    } else {
                        log(`❌ ${testCase.name}: FAIL - Expected: ${testCase.expected}, Got: ${result}`);
                    }
                    
                    addTestResult(testCase.name, passed, testCase.expected, result);
                } catch (error) {
                    log(`❌ ${testCase.name}: ERROR - ${error.message}`);
                    addTestResult(testCase.name, false, testCase.expected, `ERROR: ${error.message}`);
                }
            });

            log(`\n📊 Test Summary: ${passedTests}/${totalTests} tests passed`);
            if (passedTests === totalTests) {
                log('🎉 All tests passed! Card formatting fix is working correctly.');
            } else {
                log('⚠️ Some tests failed. Please review the results above.');
            }
        };

        window.testSpecificCase = function() {
            log('🎯 Testing specific user-reported case...');
            const input = '****************,07,30,997,94112';
            const expected = '****************,07,30,997,94112';
            
            try {
                const result = formatCard(input);
                const passed = result === expected;
                
                log(`Input: ${input}`);
                log(`Expected: ${expected}`);
                log(`Actual: ${result}`);
                log(`Result: ${passed ? '✅ PASS - Year preserved correctly!' : '❌ FAIL - Year was modified!'}`);
                
                addTestResult('User Reported Case', passed, expected, result);
            } catch (error) {
                log(`❌ Error testing specific case: ${error.message}`);
            }
        };

        window.testBulkProcessing = function() {
            log('📦 Testing bulk card processing...');
            const input = `****************,07,30,997,94112
****************,12,25,456,90210
1234567890123456,01,99,789,12345`;
            
            const expected = [
                '****************,07,30,997,94112',
                '****************,12,25,456,90210',
                '1234567890123456,01,99,789,12345'
            ];
            
            try {
                const result = parseBulkCards(input);
                const passed = JSON.stringify(result) === JSON.stringify(expected);
                
                log(`Input lines: ${input.split('\n').length}`);
                log(`Expected: ${JSON.stringify(expected, null, 2)}`);
                log(`Actual: ${JSON.stringify(result, null, 2)}`);
                log(`Result: ${passed ? '✅ PASS - All years preserved!' : '❌ FAIL - Some years were modified!'}`);
                
                addTestResult('Bulk Processing', passed, JSON.stringify(expected), JSON.stringify(result));
            } catch (error) {
                log(`❌ Error testing bulk processing: ${error.message}`);
            }
        };

        window.testSingleCard = function() {
            const input = document.getElementById('single-card-input').value.trim();
            if (!input) {
                log('❌ Please enter a card to test');
                return;
            }
            
            log(`🔍 Testing single card: ${input}`);
            
            try {
                // First validate
                const validation = validateCard(input);
                log(`Validation: ${validation.isValid ? '✅ Valid' : '❌ Invalid - ' + validation.error}`);
                
                if (validation.isValid) {
                    // Then format
                    const formatted = formatCard(input);
                    log(`Original: ${input}`);
                    log(`Formatted: ${formatted}`);
                    
                    // Check if year was preserved
                    const originalParts = input.split(',');
                    const formattedParts = formatted.split(',');
                    const yearPreserved = originalParts[2] === formattedParts[2];
                    
                    log(`Year preservation: ${yearPreserved ? '✅ Preserved' : '❌ Modified'} (${originalParts[2]} → ${formattedParts[2]})`);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        };

        window.testBulkCards = function() {
            const input = document.getElementById('bulk-cards-input').value.trim();
            if (!input) {
                log('❌ Please enter cards to test');
                return;
            }
            
            log(`📦 Testing bulk cards...`);
            log(`Input:\n${input}`);
            
            try {
                const result = parseBulkCards(input);
                log(`\nProcessed ${result.length} cards:`);
                result.forEach((card, index) => {
                    log(`${index + 1}. ${card}`);
                });
                
                // Check year preservation
                const inputLines = input.split('\n').filter(line => line.trim());
                let yearsPreserved = 0;
                
                inputLines.forEach((line, index) => {
                    if (index < result.length) {
                        const originalYear = line.split(',')[2];
                        const processedYear = result[index].split(',')[2];
                        if (originalYear === processedYear) {
                            yearsPreserved++;
                        } else {
                            log(`⚠️ Year modified in line ${index + 1}: ${originalYear} → ${processedYear}`);
                        }
                    }
                });
                
                log(`\n📊 Year preservation: ${yearsPreserved}/${inputLines.length} cards preserved original year format`);
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        };

        // Auto-run tests on page load
        setTimeout(() => {
            log('🔄 Auto-running tests...');
            window.runAllTests();
        }, 1000);
    </script>
</body>
</html>
