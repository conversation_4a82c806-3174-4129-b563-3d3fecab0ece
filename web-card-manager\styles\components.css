/* Components CSS - UI components and form elements */

/* Form Elements */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--bg-secondary);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
    font-family: 'Courier New', monospace;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    align-items: end;
}

.input-group {
    display: flex;
    gap: var(--spacing-sm);
    align-items: stretch;
}

.input-group .form-input {
    flex: 1;
}

.input-help {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.primary-btn {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.primary-btn:hover:not(:disabled) {
    background-color: var(--accent-hover);
    border-color: var(--accent-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.secondary-btn {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

.secondary-btn:hover:not(:disabled) {
    background-color: var(--bg-hover);
    border-color: var(--border-secondary);
}

.danger-btn {
    background-color: var(--danger);
    color: white;
    border-color: var(--danger);
}

.danger-btn:hover:not(:disabled) {
    background-color: var(--danger-hover);
    border-color: var(--danger-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn.large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
}

.btn.small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    user-select: none;
    font-size: var(--font-size-sm);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-sm);
    background-color: var(--bg-tertiary);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label:hover .checkmark {
    border-color: var(--accent-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Sections */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.section-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.section-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.section-header h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.input-section {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

/* Lists */
.lists-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.list-section {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: column;
    min-height: 300px;
}

.item-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 400px;
    overflow-y: auto;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-tertiary);
}

.item-list::-webkit-scrollbar {
    width: 8px;
}

.item-list::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

.item-list::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: var(--radius-sm);
}

.item-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

.list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    gap: var(--spacing-sm);
}

.list-item:hover {
    background-color: var(--bg-hover);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.list-item-content {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    word-break: break-all;
    color: var(--text-primary);
}

.list-item-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.list-item:hover .list-item-actions {
    opacity: 1;
}

.list-item-btn {
    padding: var(--spacing-xs);
    border: none;
    background: none;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.list-item-btn:hover {
    background-color: var(--bg-active);
    color: var(--text-primary);
}

.list-item-btn.danger:hover {
    background-color: var(--danger);
    color: white;
}

.list-item-btn.success:hover {
    background-color: var(--success);
    color: white;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-muted);
    flex: 1;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state p {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.empty-state small {
    font-size: var(--font-size-sm);
    opacity: 0.7;
}

/* Email Sections */
.email-sections {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.email-type-section {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.email-type-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Combinations */
.combination-section {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.combination-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-actions {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-md);
}

.auto-select-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.latest-combination {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    margin-top: var(--spacing-lg);
}

.combinations-list-section {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    flex: 1;
}

.combinations-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 500px;
    overflow-y: auto;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-tertiary);
}

.combination-item {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    transition: all var(--transition-fast);
}

.combination-item:hover {
    background-color: var(--bg-hover);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.combination-item.latest {
    border-color: var(--accent-primary);
    background-color: var(--bg-tertiary);
}

.combination-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.combination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.email-type-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    color: white;
}

.email-type-badge.pm { background-color: #8b5cf6; }
.email-type-badge.ue-25-25 { background-color: #06b6d4; }
.email-type-badge.ue-25-15 { background-color: #10b981; }
.email-type-badge.ue-25-1 { background-color: #f59e0b; }

.combination-content {
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-primary);
    word-break: break-all;
    margin-bottom: var(--spacing-sm);
}

.combination-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.combination-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.combination-btn:hover {
    background-color: var(--bg-hover);
    border-color: var(--border-secondary);
}

.combination-btn.wool { border-color: #8b5cf6; color: #8b5cf6; }
.combination-btn.fusion { border-color: #06b6d4; color: #06b6d4; }
.combination-btn.otp { border-color: #10b981; color: #10b981; }
.combination-btn.delete { border-color: var(--danger); color: var(--danger); }

.combination-btn.wool:hover { background-color: #8b5cf6; color: white; }
.combination-btn.fusion:hover { background-color: #06b6d4; color: white; }
.combination-btn.otp:hover { background-color: #10b981; color: white; }
.combination-btn.delete:hover { background-color: var(--danger); color: white; }

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    padding: var(--spacing-lg);
}

.modal {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.modal-close:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.bulk-help {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
}

.bulk-help p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.settings-section {
    margin-bottom: var(--spacing-lg);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h4 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    padding-bottom: var(--spacing-sm);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 400px;
    width: 100%;
}

.toast {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    position: relative;
    overflow: hidden;
}

.toast.success {
    border-left: 4px solid var(--success);
}

.toast.error {
    border-left: 4px solid var(--danger);
}

.toast.warning {
    border-left: 4px solid var(--warning);
}

.toast.info {
    border-left: 4px solid var(--accent-primary);
}

.toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
}

.toast.success .toast-icon { color: var(--success); }
.toast.error .toast-icon { color: var(--danger); }
.toast.warning .toast-icon { color: var(--warning); }
.toast.info .toast-icon { color: var(--accent-primary); }

.toast-content {
    flex: 1;
}

.toast-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.toast-close:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: var(--accent-primary);
    transition: width linear;
}

.toast.success .toast-progress { background-color: var(--success); }
.toast.error .toast-progress { background-color: var(--danger); }
.toast.warning .toast-progress { background-color: var(--warning); }

/* Responsive adjustments */
@media (max-width: 768px) {
    .lists-container {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .section-actions {
        justify-content: center;
    }

    .input-group {
        flex-direction: column;
    }

    .combination-header {
        flex-direction: column;
        align-items: stretch;
    }

    .combination-actions {
        justify-content: center;
    }

    .modal {
        margin: var(--spacing-md);
        max-width: none;
    }

    .toast-container {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }
}
