<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Card Manager Initialization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        .loading-screen.hidden {
            display: none;
        }
        .main-app {
            padding: 20px;
        }
        .main-app.hidden {
            display: none;
        }
        .debug-info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div>
            <p>Loading Card Manager...</p>
        </div>
    </div>

    <!-- Main Application -->
    <div id="main-app" class="main-app hidden">
        <h1>Card Manager Test</h1>
        <div id="debug-output" class="debug-info">
            <p>Application loaded successfully!</p>
        </div>
        <!-- Toast container -->
        <div id="toast-container"></div>
    </div>

    <script type="module">
        const debugOutput = document.getElementById('debug-output');
        
        function log(message, type = 'info') {
            const p = document.createElement('p');
            p.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            p.className = type;
            debugOutput.appendChild(p);
            console.log(message);
        }

        async function testInitialization() {
            try {
                log('Starting initialization test...', 'info');
                
                // Import main app
                const { CardManagerApp } = await import('./js/main.js');
                log('✓ Main app imported successfully', 'success');
                
                // Create app instance
                const app = new CardManagerApp();
                log('✓ App instance created', 'success');
                
                // Initialize
                await app.init();
                log('✓ App initialized successfully', 'success');
                
                log('All tests passed!', 'success');
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
                
                // Show main app anyway for debugging
                const loadingScreen = document.getElementById('loading-screen');
                const mainApp = document.getElementById('main-app');
                
                if (loadingScreen) loadingScreen.classList.add('hidden');
                if (mainApp) mainApp.classList.remove('hidden');
            }
        }

        // Run test when DOM is ready
        document.addEventListener('DOMContentLoaded', testInitialization);
    </script>
</body>
</html>
