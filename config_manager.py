"""
Configuration management for user preferences and application settings.

Handles loading, saving, and managing user preferences, themes, and configurable options.
"""
import json
import os
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import logging

@dataclass
class AppConfig:
    """Application configuration settings."""
    # Auto-save settings
    auto_save_enabled: bool = True
    auto_save_interval: int = 60  # seconds
    
    # UI settings
    theme: str = "dark"  # "dark" or "light"
    window_width: int = 1200
    window_height: int = 800
    window_x: Optional[int] = None
    window_y: Optional[int] = None
    
    # Performance settings
    use_incremental_updates: bool = True
    max_visible_items: int = 1000
    
    # Backup settings
    max_backups: int = 10
    backup_on_startup: bool = True
    
    # Copy format preferences
    default_copy_format: str = "fusion"  # "wool", "fusion", "otp"
    
    # Search settings
    search_case_sensitive: bool = False
    search_highlight_matches: bool = True
    
    # Import/Export settings
    default_export_format: str = "csv"
    include_headers_in_export: bool = True
    
    # Validation settings
    strict_card_validation: bool = True
    strict_email_validation: bool = True
    
    # Debug settings
    debug_mode: bool = False
    log_level: str = "INFO"

class ConfigManager:
    """Manages application configuration and user preferences."""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = AppConfig()
        self.load_config()
    
    def load_config(self) -> bool:
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Update config with loaded data
                for key, value in data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                    else:
                        logging.warning(f"Unknown config key: {key}")
                
                logging.info("Configuration loaded successfully")
                return True
            else:
                # Create default config file
                self.save_config()
                logging.info("Created default configuration file")
                return True
                
        except Exception as e:
            logging.error(f"Failed to load configuration: {e}")
            # Use default configuration
            self.config = AppConfig()
            return False
    
    def save_config(self) -> bool:
        """Save configuration to file."""
        try:
            config_dict = asdict(self.config)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logging.info("Configuration saved successfully")
            return True
            
        except Exception as e:
            logging.error(f"Failed to save configuration: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        return getattr(self.config, key, default)
    
    def set(self, key: str, value: Any) -> bool:
        """Set a configuration value."""
        try:
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                return True
            else:
                logging.warning(f"Unknown config key: {key}")
                return False
        except Exception as e:
            logging.error(f"Failed to set config {key}={value}: {e}")
            return False
    
    def update_window_geometry(self, width: int, height: int, x: int, y: int):
        """Update window geometry settings."""
        self.config.window_width = width
        self.config.window_height = height
        self.config.window_x = x
        self.config.window_y = y

    def set_window_geometry(self, width: int, height: int, x: int, y: int):
        """Set window geometry settings and save."""
        self.update_window_geometry(width, height, x, y)
        self.save_config()
    
    def get_window_geometry(self) -> Dict[str, Optional[int]]:
        """Get window geometry settings."""
        return {
            'width': self.config.window_width,
            'height': self.config.window_height,
            'x': self.config.window_x,
            'y': self.config.window_y
        }
    
    def toggle_theme(self) -> str:
        """Toggle between dark and light themes."""
        self.config.theme = "light" if self.config.theme == "dark" else "dark"
        return self.config.theme
    
    def get_theme(self) -> str:
        """Get current theme."""
        return self.config.theme

    def set_theme(self, theme: str) -> bool:
        """Set the theme."""
        if theme in ["dark", "light"]:
            self.config.theme = theme
            self.save_config()
            return True
        return False
    
    def set_auto_save_interval(self, interval: int) -> bool:
        """Set auto-save interval in seconds."""
        if interval < 10:  # Minimum 10 seconds
            logging.warning("Auto-save interval too short, setting to 10 seconds")
            interval = 10
        elif interval > 3600:  # Maximum 1 hour
            logging.warning("Auto-save interval too long, setting to 1 hour")
            interval = 3600
        
        self.config.auto_save_interval = interval
        return True
    
    def is_auto_save_enabled(self) -> bool:
        """Check if auto-save is enabled."""
        return self.config.auto_save_enabled
    
    def enable_auto_save(self, enabled: bool = True):
        """Enable or disable auto-save."""
        self.config.auto_save_enabled = enabled
    
    def get_auto_save_interval(self) -> int:
        """Get auto-save interval in seconds."""
        return self.config.auto_save_interval
    
    def set_copy_format(self, format_type: str) -> bool:
        """Set default copy format."""
        valid_formats = ["wool", "fusion", "otp"]
        if format_type.lower() in valid_formats:
            self.config.default_copy_format = format_type.lower()
            return True
        else:
            logging.warning(f"Invalid copy format: {format_type}")
            return False
    
    def get_copy_format(self) -> str:
        """Get default copy format."""
        return self.config.default_copy_format
    
    def set_validation_strictness(self, strict_cards: bool, strict_emails: bool):
        """Set validation strictness."""
        self.config.strict_card_validation = strict_cards
        self.config.strict_email_validation = strict_emails
    
    def is_strict_validation_enabled(self) -> Dict[str, bool]:
        """Get validation strictness settings."""
        return {
            'cards': self.config.strict_card_validation,
            'emails': self.config.strict_email_validation
        }
    
    def set_performance_settings(self, incremental_updates: bool, max_items: int):
        """Set performance-related settings."""
        self.config.use_incremental_updates = incremental_updates
        self.config.max_visible_items = max(100, min(10000, max_items))  # Clamp between 100-10000
    
    def get_performance_settings(self) -> Dict[str, Union[bool, int]]:
        """Get performance settings."""
        return {
            'incremental_updates': self.config.use_incremental_updates,
            'max_visible_items': self.config.max_visible_items
        }
    
    def set_debug_mode(self, enabled: bool):
        """Enable or disable debug mode."""
        self.config.debug_mode = enabled
        # Update log level based on debug mode
        self.config.log_level = "DEBUG" if enabled else "INFO"
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled."""
        return self.config.debug_mode
    
    def get_log_level(self) -> str:
        """Get current log level."""
        return self.config.log_level
    
    def reset_to_defaults(self):
        """Reset configuration to default values."""
        self.config = AppConfig()
        logging.info("Configuration reset to defaults")
    
    def export_config(self, filename: str) -> bool:
        """Export configuration to a file."""
        try:
            config_dict = asdict(self.config)
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Configuration exported to {filename}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to export configuration: {e}")
            return False
    
    def import_config(self, filename: str) -> bool:
        """Import configuration from a file."""
        try:
            if not os.path.exists(filename):
                logging.error(f"Config file not found: {filename}")
                return False
            
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate and update config
            for key, value in data.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                else:
                    logging.warning(f"Unknown config key during import: {key}")
            
            logging.info(f"Configuration imported from {filename}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to import configuration: {e}")
            return False
    
    def get_all_settings(self) -> Dict[str, Any]:
        """Get all configuration settings as a dictionary."""
        return asdict(self.config)
    
    def validate_config(self) -> bool:
        """Validate current configuration settings."""
        try:
            # Check theme
            if self.config.theme not in ["dark", "light"]:
                self.config.theme = "dark"
            
            # Check auto-save interval
            if self.config.auto_save_interval < 10:
                self.config.auto_save_interval = 60
            
            # Check window dimensions
            if self.config.window_width < 800:
                self.config.window_width = 1200
            if self.config.window_height < 600:
                self.config.window_height = 800
            
            # Check copy format
            if self.config.default_copy_format not in ["wool", "fusion", "otp"]:
                self.config.default_copy_format = "fusion"
            
            # Check max visible items
            if self.config.max_visible_items < 100:
                self.config.max_visible_items = 1000
            
            return True
            
        except Exception as e:
            logging.error(f"Config validation failed: {e}")
            return False
