<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test - Card Manager</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: var(--font-family);
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        .test-btn {
            margin: 10px;
            padding: 15px 30px;
            font-size: 16px;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: var(--bg-card);
            border: 1px solid var(--border-glass);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Modal Visibility Test</h1>
        <p>This page tests the modal functionality in isolation.</p>
        
        <div class="status" id="status">
            <strong>Status:</strong> <span id="status-text">Initializing...</span>
        </div>
        
        <button id="test-cards-modal" class="btn primary-btn test-btn">
            📋 Test Cards Modal
        </button>
        
        <button id="test-emails-modal" class="btn secondary-btn test-btn">
            📧 Test Emails Modal
        </button>
        
        <div id="console-output" style="margin-top: 20px; text-align: left; background: #1a1a1a; color: #00ff00; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
            <strong>Console Output:</strong><br>
        </div>
    </div>

    <!-- Modal overlay -->
    <div id="modal-overlay" class="modal-overlay hidden"></div>

    <script type="module">
        import { ModalManager } from './js/modules/ModalManager.js';
        
        // Override console.log to show output on page
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToOutput(message, type = 'log') {
            const color = type === 'error' ? '#ff4444' : '#00ff00';
            consoleOutput.innerHTML += `<span style="color: ${color}">${new Date().toLocaleTimeString()}: ${message}</span><br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addToOutput(args.join(' '), 'log');
        };
        
        console.error = (...args) => {
            originalError(...args);
            addToOutput(args.join(' '), 'error');
        };
        
        // Initialize modal manager
        let modalManager;
        
        async function initTest() {
            try {
                console.log('🚀 Initializing Modal Test...');
                
                modalManager = new ModalManager();
                await modalManager.init();
                
                console.log('✅ Modal Manager initialized successfully');
                document.getElementById('status-text').textContent = 'Ready for testing';
                
                // Setup test buttons
                document.getElementById('test-cards-modal').addEventListener('click', () => {
                    console.log('🖱️ Testing Cards Modal...');
                    modalManager.showBulkAddModal(
                        'Test Cards Modal',
                        'Enter test cards (one per line)\nFormat: number,month,year,cvv,zip',
                        (data, emailType) => {
                            console.log('📋 Cards submitted:', data);
                            console.log('📋 Email type:', emailType);
                        }
                    );
                });
                
                document.getElementById('test-emails-modal').addEventListener('click', () => {
                    console.log('🖱️ Testing Emails Modal...');
                    modalManager.showBulkAddModal(
                        'Test Emails Modal',
                        'Enter test emails (one per line)',
                        (data, emailType) => {
                            console.log('📧 Emails submitted:', data);
                            console.log('📧 Email type:', emailType);
                        }
                    );
                });
                
            } catch (error) {
                console.error('❌ Test initialization failed:', error);
                document.getElementById('status-text').textContent = 'Initialization failed';
            }
        }
        
        // Start test when page loads
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
