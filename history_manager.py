"""
Undo/Redo functionality and action history management.

Provides comprehensive undo/redo capabilities for all user actions in the Card Manager application.
"""
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import copy

class ActionType(Enum):
    """Types of actions that can be undone/redone."""
    ADD_CARD = "add_card"
    REMOVE_CARD = "remove_card"
    ADD_EMAIL = "add_email"
    REMOVE_EMAIL = "remove_email"
    CREATE_COMBINATION = "create_combination"
    DELETE_COMBINATION = "delete_combination"
    ARCHIVE_CARD = "archive_card"
    ARCHIVE_EMAIL = "archive_email"
    UNARCHIVE_CARD = "unarchive_card"
    UNARCHIVE_EMAIL = "unarchive_email"
    BULK_ADD_CARDS = "bulk_add_cards"
    BULK_ADD_EMAILS = "bulk_add_emails"
    CLEAR_ALL_CARDS = "clear_all_cards"
    CLEAR_ALL_EMAILS = "clear_all_emails"
    REMOVE_USED_CARDS = "remove_used_cards"
    REMOVE_USED_EMAILS = "remove_used_emails"

@dataclass
class Action:
    """Represents a single action that can be undone/redone."""
    action_type: ActionType
    timestamp: datetime
    description: str
    data: Dict[str, Any] = field(default_factory=dict)
    undo_data: Dict[str, Any] = field(default_factory=dict)
    
    def __str__(self):
        return f"{self.action_type.value}: {self.description}"

class HistoryManager:
    """Manages action history and undo/redo functionality."""
    
    def __init__(self, data_manager, max_history: int = 50):
        self.data_manager = data_manager
        self.max_history = max_history
        self.undo_stack: List[Action] = []
        self.redo_stack: List[Action] = []
        self.action_handlers = self._setup_action_handlers()
    
    def _setup_action_handlers(self) -> Dict[ActionType, Callable]:
        """Setup handlers for different action types."""
        return {
            ActionType.ADD_CARD: self._undo_add_card,
            ActionType.REMOVE_CARD: self._undo_remove_card,
            ActionType.ADD_EMAIL: self._undo_add_email,
            ActionType.REMOVE_EMAIL: self._undo_remove_email,
            ActionType.CREATE_COMBINATION: self._undo_create_combination,
            ActionType.DELETE_COMBINATION: self._undo_delete_combination,
            ActionType.ARCHIVE_CARD: self._undo_archive_card,
            ActionType.ARCHIVE_EMAIL: self._undo_archive_email,
            ActionType.UNARCHIVE_CARD: self._undo_unarchive_card,
            ActionType.UNARCHIVE_EMAIL: self._undo_unarchive_email,
            ActionType.BULK_ADD_CARDS: self._undo_bulk_add_cards,
            ActionType.BULK_ADD_EMAILS: self._undo_bulk_add_emails,
            ActionType.CLEAR_ALL_CARDS: self._undo_clear_all_cards,
            ActionType.CLEAR_ALL_EMAILS: self._undo_clear_all_emails,
            ActionType.REMOVE_USED_CARDS: self._undo_remove_used_cards,
            ActionType.REMOVE_USED_EMAILS: self._undo_remove_used_emails,
        }
    
    def record_action(self, action_type: ActionType, description: str, 
                     data: Dict[str, Any], undo_data: Dict[str, Any]):
        """Record a new action in the history."""
        action = Action(
            action_type=action_type,
            timestamp=datetime.now(),
            description=description,
            data=data,
            undo_data=undo_data
        )
        
        # Add to undo stack
        self.undo_stack.append(action)
        
        # Clear redo stack when new action is performed
        self.redo_stack.clear()
        
        # Limit history size
        if len(self.undo_stack) > self.max_history:
            self.undo_stack.pop(0)
    
    def can_undo(self) -> bool:
        """Check if undo is possible."""
        return len(self.undo_stack) > 0
    
    def can_redo(self) -> bool:
        """Check if redo is possible."""
        return len(self.redo_stack) > 0
    
    def get_undo_description(self) -> Optional[str]:
        """Get description of the next action to undo."""
        if self.can_undo():
            return self.undo_stack[-1].description
        return None
    
    def get_redo_description(self) -> Optional[str]:
        """Get description of the next action to redo."""
        if self.can_redo():
            return self.redo_stack[-1].description
        return None
    
    def undo(self) -> bool:
        """Undo the last action."""
        if not self.can_undo():
            return False
        
        action = self.undo_stack.pop()
        
        try:
            # Execute undo
            handler = self.action_handlers.get(action.action_type)
            if handler:
                handler(action)
                
                # Move action to redo stack
                self.redo_stack.append(action)
                return True
            else:
                # No handler found, put action back
                self.undo_stack.append(action)
                return False
                
        except Exception as e:
            # Error during undo, put action back
            self.undo_stack.append(action)
            raise e
    
    def redo(self) -> bool:
        """Redo the last undone action."""
        if not self.can_redo():
            return False
        
        action = self.redo_stack.pop()
        
        try:
            # Execute redo (reverse of undo)
            self._redo_action(action)
            
            # Move action back to undo stack
            self.undo_stack.append(action)
            return True
            
        except Exception as e:
            # Error during redo, put action back
            self.redo_stack.append(action)
            raise e
    
    def _redo_action(self, action: Action):
        """Redo an action by applying its original data."""
        if action.action_type == ActionType.ADD_CARD:
            card = action.data['card']
            self.data_manager.cards.append(card)
        
        elif action.action_type == ActionType.REMOVE_CARD:
            card = action.data['card']
            if card in self.data_manager.cards:
                self.data_manager.cards.remove(card)
            # Also remove from used/archived sets
            self.data_manager.used_cards.discard(card)
            self.data_manager.all_used_cards.discard(card)
            self.data_manager.archived_cards.discard(card)
        
        elif action.action_type == ActionType.ADD_EMAIL:
            email = action.data['email']
            email_type = action.data['email_type']
            email_list = self.data_manager.get_email_list_by_type(email_type)
            email_list.append(email)
        
        elif action.action_type == ActionType.REMOVE_EMAIL:
            email = action.data['email']
            email_type = action.data['email_type']
            email_list = self.data_manager.get_email_list_by_type(email_type)
            if email in email_list:
                email_list.remove(email)
            # Also remove from used/archived sets
            self.data_manager.used_emails.discard(email)
            self.data_manager.all_used_emails.discard(email)
            self.data_manager.archived_emails.discard(email)
        
        elif action.action_type == ActionType.CREATE_COMBINATION:
            combination = action.data['combination']
            self.data_manager.combinations.append(combination)
            # Mark items as used
            card = combination['card']
            email = combination['email']
            self.data_manager.used_cards.add(card)
            self.data_manager.used_emails.add(email)
            self.data_manager.all_used_cards.add(card)
            self.data_manager.all_used_emails.add(email)
        
        elif action.action_type == ActionType.DELETE_COMBINATION:
            index = action.data['index']
            # Remove combination at index
            if 0 <= index < len(self.data_manager.combinations):
                del self.data_manager.combinations[index]
        
        # Add more redo handlers as needed...
    
    # Undo handlers
    def _undo_add_card(self, action: Action):
        """Undo adding a card."""
        card = action.data['card']
        if card in self.data_manager.cards:
            self.data_manager.cards.remove(card)
    
    def _undo_remove_card(self, action: Action):
        """Undo removing a card."""
        card = action.undo_data['card']
        was_used = action.undo_data.get('was_used', False)
        was_archived = action.undo_data.get('was_archived', False)
        was_currently_used = action.undo_data.get('was_currently_used', False)
        
        # Restore card
        self.data_manager.cards.append(card)
        
        # Restore status
        if was_used:
            self.data_manager.all_used_cards.add(card)
        if was_archived:
            self.data_manager.archived_cards.add(card)
        if was_currently_used:
            self.data_manager.used_cards.add(card)
    
    def _undo_add_email(self, action: Action):
        """Undo adding an email."""
        email = action.data['email']
        email_type = action.data['email_type']
        email_list = self.data_manager.get_email_list_by_type(email_type)
        if email in email_list:
            email_list.remove(email)
    
    def _undo_remove_email(self, action: Action):
        """Undo removing an email."""
        email = action.undo_data['email']
        email_type = action.undo_data['email_type']
        was_used = action.undo_data.get('was_used', False)
        was_archived = action.undo_data.get('was_archived', False)
        was_currently_used = action.undo_data.get('was_currently_used', False)
        
        # Restore email
        email_list = self.data_manager.get_email_list_by_type(email_type)
        email_list.append(email)
        
        # Restore status
        if was_used:
            self.data_manager.all_used_emails.add(email)
        if was_archived:
            self.data_manager.archived_emails.add(email)
        if was_currently_used:
            self.data_manager.used_emails.add(email)
    
    def _undo_create_combination(self, action: Action):
        """Undo creating a combination."""
        combination = action.data['combination']
        
        # Remove combination
        if combination in self.data_manager.combinations:
            self.data_manager.combinations.remove(combination)
        
        # Restore card and email status
        card = combination['card']
        email = combination['email']
        
        self.data_manager.used_cards.discard(card)
        self.data_manager.used_emails.discard(email)
        
        # Don't remove from all_used sets as they might have been used before
    
    def _undo_delete_combination(self, action: Action):
        """Undo deleting a combination."""
        combination = action.undo_data['combination']
        index = action.undo_data['index']
        
        # Insert combination back at original position
        if index <= len(self.data_manager.combinations):
            self.data_manager.combinations.insert(index, combination)
        else:
            self.data_manager.combinations.append(combination)
    
    def _undo_archive_card(self, action: Action):
        """Undo archiving a card."""
        card = action.data['card']
        self.data_manager.archived_cards.discard(card)
        # Card should already be in the main cards list
    
    def _undo_archive_email(self, action: Action):
        """Undo archiving an email."""
        email = action.data['email']
        self.data_manager.archived_emails.discard(email)
        # Email should already be in the appropriate email list
    
    def _undo_unarchive_card(self, action: Action):
        """Undo unarchiving a card."""
        card = action.data['card']
        self.data_manager.archived_cards.add(card)
    
    def _undo_unarchive_email(self, action: Action):
        """Undo unarchiving an email."""
        email = action.data['email']
        self.data_manager.archived_emails.add(email)
    
    def _undo_bulk_add_cards(self, action: Action):
        """Undo bulk adding cards."""
        cards = action.data['cards']
        for card in cards:
            if card in self.data_manager.cards:
                self.data_manager.cards.remove(card)
    
    def _undo_bulk_add_emails(self, action: Action):
        """Undo bulk adding emails."""
        emails = action.data['emails']
        email_type = action.data['email_type']
        email_list = self.data_manager.get_email_list_by_type(email_type)
        
        for email in emails:
            if email in email_list:
                email_list.remove(email)
    
    def _undo_clear_all_cards(self, action: Action):
        """Undo clearing all cards."""
        cards = action.undo_data['cards']
        self.data_manager.cards.extend(cards)
    
    def _undo_clear_all_emails(self, action: Action):
        """Undo clearing all emails."""
        emails_data = action.undo_data['emails']
        for email_type, emails in emails_data.items():
            email_list = self.data_manager.get_email_list_by_type(email_type)
            email_list.extend(emails)
    
    def _undo_remove_used_cards(self, action: Action):
        """Undo removing used cards."""
        removed_cards = action.undo_data['removed_cards']
        self.data_manager.cards.extend(removed_cards)
        
        # Restore their used status
        for card in removed_cards:
            self.data_manager.all_used_cards.add(card)
    
    def _undo_remove_used_emails(self, action: Action):
        """Undo removing used emails."""
        removed_emails = action.undo_data['removed_emails']
        
        for email_type, emails in removed_emails.items():
            email_list = self.data_manager.get_email_list_by_type(email_type)
            email_list.extend(emails)
            
            # Restore their used status
            for email in emails:
                self.data_manager.all_used_emails.add(email)
    
    def get_history(self, limit: int = 20) -> List[str]:
        """Get recent action history."""
        history = []
        
        # Get recent undo actions (most recent first)
        for action in reversed(self.undo_stack[-limit:]):
            history.append(f"✓ {action.description}")
        
        return history
    
    def clear_history(self):
        """Clear all history."""
        self.undo_stack.clear()
        self.redo_stack.clear()
    
    def get_stats(self) -> Dict[str, int]:
        """Get history statistics."""
        return {
            'undo_available': len(self.undo_stack),
            'redo_available': len(self.redo_stack),
            'total_actions': len(self.undo_stack) + len(self.redo_stack)
        }
