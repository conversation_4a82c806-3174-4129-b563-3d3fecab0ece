<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Fixed - Card Manager</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💳</text></svg>">
    <style>
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            background: rgba(0, 0, 0, 0.95);
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            z-index: 10000;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .debug-header {
            color: #00ccff;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }
        .debug-section {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 4px;
        }
        .debug-section h4 {
            color: #ffcc00;
            margin: 0 0 10px 0;
        }
        .status-ok { color: #00ff00; }
        .status-error { color: #ff4444; }
        .status-warning { color: #ff8c00; }
        .test-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        .test-btn:hover { background: #005a99; }
    </style>
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Loading Card Manager...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <header class="toolbar">
                <div class="toolbar-left">
                    <h1 class="app-title">
                        <i class="fas fa-credit-card"></i>
                        Card Manager - Debug Fixed
                    </h1>
                </div>
            </header>
            
            <main class="main-content">
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="cards">
                        <i class="fas fa-credit-card"></i>
                        Cards
                        <span class="tab-count" id="cards-count">0</span>
                    </button>
                </div>
                
                <div class="tab-panel active" id="cards-tab">
                    <div class="tab-content">
                        <section class="input-section">
                            <h3>Add Cards</h3>
                            <div class="input-group">
                                <input 
                                    type="text" 
                                    id="card-input" 
                                    class="form-input" 
                                    placeholder="Enter card: number,month,year,cvv,zip"
                                    autocomplete="off"
                                >
                                <button id="add-card-btn" class="primary-btn">
                                    <i class="fas fa-plus"></i>
                                    Add Card
                                </button>
                            </div>
                        </section>
                    </div>
                </div>
            </main>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Debug Panel -->
    <div class="debug-panel">
        <div class="debug-header">🔧 Debug Panel - Fixed Version</div>
        
        <div class="debug-section">
            <h4>🧪 Quick Tests</h4>
            <button class="test-btn" onclick="testHelpers()">Test Helpers</button>
            <button class="test-btn" onclick="testModules()">Test Modules</button>
            <button class="test-btn" onclick="testInit()">Test Init</button>
            <button class="test-btn" onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="debug-section">
            <h4>📊 Status</h4>
            <div id="debug-status">Ready for testing...</div>
        </div>
        
        <div class="debug-section">
            <h4>📝 Console Logs</h4>
            <div id="debug-logs" style="max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script type="module">
        const debugStatus = document.getElementById('debug-status');
        const debugLogs = document.getElementById('debug-logs');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span class="status-${type}">${message}</span>`;
            debugLogs.appendChild(logEntry);
            debugLogs.scrollTop = debugLogs.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(message, type = 'ok') {
            debugStatus.innerHTML = `<span class="status-${type}">${message}</span>`;
        }
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'ok');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        // Global test functions
        window.testHelpers = async function() {
            try {
                addLog('Testing helpers.js...', 'warning');
                const helpers = await import('./js/utils/helpers.js');
                addLog(`✅ Helpers loaded: ${Object.keys(helpers).join(', ')}`, 'ok');
                
                // Test deepClone specifically
                const { deepClone } = helpers;
                const testObj = { a: 1, b: { c: 2 } };
                const cloned = deepClone(testObj);
                
                if (JSON.stringify(cloned) === JSON.stringify(testObj)) {
                    addLog('✅ deepClone function working correctly', 'ok');
                } else {
                    addLog('❌ deepClone function failed', 'error');
                }
                
                updateStatus('Helpers test completed', 'ok');
                
            } catch (error) {
                addLog(`❌ Helpers test failed: ${error.message}`, 'error');
                updateStatus('Helpers test failed', 'error');
            }
        };
        
        window.testModules = async function() {
            try {
                addLog('Testing module imports...', 'warning');
                
                const modules = [
                    './js/modules/DataManager.js',
                    './js/modules/UIManager.js',
                    './js/modules/ToastManager.js',
                    './js/modules/ConfigManager.js'
                ];
                
                for (const module of modules) {
                    const imported = await import(module);
                    addLog(`✅ ${module.split('/').pop()} loaded`, 'ok');
                }
                
                updateStatus('Module imports completed', 'ok');
                
            } catch (error) {
                addLog(`❌ Module test failed: ${error.message}`, 'error');
                updateStatus('Module test failed', 'error');
            }
        };
        
        window.testInit = async function() {
            try {
                addLog('Testing full initialization...', 'warning');
                
                const { CardManagerApp } = await import('./js/main.js');
                const app = new CardManagerApp();
                
                addLog('Starting initialization...', 'warning');
                await app.init();
                
                addLog('🎉 Initialization completed successfully!', 'ok');
                updateStatus('Full initialization successful', 'ok');
                
            } catch (error) {
                addLog(`❌ Initialization failed: ${error.message}`, 'error');
                addLog(`Stack: ${error.stack}`, 'error');
                updateStatus('Initialization failed', 'error');
            }
        };
        
        window.clearLogs = function() {
            debugLogs.innerHTML = '';
            updateStatus('Logs cleared', 'ok');
        };
        
        // Auto-run basic tests
        document.addEventListener('DOMContentLoaded', () => {
            addLog('🌟 Debug panel loaded', 'ok');
            updateStatus('Ready for testing', 'ok');
        });
    </script>
</body>
</html>
