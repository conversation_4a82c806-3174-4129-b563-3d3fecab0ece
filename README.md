# Card Manager

A modern GUI application that allows you to manage cards and emails, combine them, and delete combinations.

## Features

- Add and manage cards
- Add and manage emails
- Combine cards with emails
- Delete combinations
- Both cards and emails can only be used in one combination
- Separate sections for available and used cards/emails
- Individual remove buttons for each card and email
- "Remove Used" buttons to remove all used cards/emails at once
- Copy button for each combination
- Auto-selection of cards and emails for quick combinations
- Bulk entry of cards and emails
- Custom combination format (card,email)
- Modern UI with customtkinter
- Data is saved between sessions

## Requirements

- Python 3.6 or higher
- customtkinter (`pip install customtkinter`)
- pyperclip (`pip install pyperclip`) - for clipboard functionality

## How to Run

1. Make sure you have Python installed
2. Run the application:

```
python card_manager.py
```

## Usage Instructions

### Adding Cards

1. Go to the "Cards" tab
2. Enter a card number in the input field
3. Click "Add Card"

### Adding Cards in Bulk

1. Go to the "Cards" tab
2. Click "Bulk Add"
3. Paste multiple cards in the format shown in the example
4. Click "Add Cards"

### Adding Emails

1. Go to the "Emails" tab
2. Enter an email address in the input field
3. Click "Add Email"

### Adding Emails in Bulk

1. Go to the "Emails" tab
2. Click "Bulk Add"
3. Paste multiple emails in the format shown in the example
4. Click "Add Emails"

### Creating Combinations

1. Go to the "Combinations" tab
2. By default, a card and email will be automatically selected
3. You can manually select a card and email from the dropdowns if desired
4. Click "Combine"
5. After combining, a new card and email will be automatically selected

### Deleting Combinations

1. Go to the "Combinations" tab
2. Click the "Delete" button next to the combination you want to delete

### Removing Cards and Emails

1. Go to the "Cards" or "Emails" tab
2. Click the "Remove" button next to the card or email you want to remove
3. Note: You can only remove cards and emails that are not used in combinations

### Removing Used Cards and Emails

1. Go to the "Cards" or "Emails" tab
2. Click the "Remove Used" button at the top of the list
3. This will remove all cards or emails that have been used in combinations

### Copying Combinations

1. Go to the "Combinations" tab
2. Click the "Copy" button next to the combination you want to copy
3. The combination will be copied to your clipboard

### Saving Data

- Click the "Save Data" button at the bottom of the window to save your data
- Data is automatically loaded when you start the application

## Notes

- Cards and emails are separated into "Available" and "Used" sections
- Once a card or email has been used, it remains in the "Used" section permanently
- Cards and emails that are currently in a combination are marked with "(In Use)"
- Only cards and emails that have never been used appear in the dropdown when creating combinations
- When you delete a combination, the card and email remain in the "Used" section but are no longer marked as "In Use"
- Each card and email in the "Available" section has its own remove button
- "Remove Used" buttons allow you to remove all used cards or emails at once
- Each combination has its own copy and delete buttons
- The copy button copies the combination to your clipboard
- The auto-select feature can be toggled on/off with the checkbox in the Combinations tab
- The application uses customtkinter for a modern look and feel
- The combination format is: `card,email`
- Bulk entry allows you to quickly add multiple cards or emails at once
