/**
 * Modal Manager - Handles modal dialogs
 */

import { createElement, animateElement, readFileAsText } from '../utils/helpers.js';

export class ModalManager {
    constructor() {
        this.activeModal = null;
        this.overlay = null;
    }

    /**
     * Initialize modal manager
     */
    async init() {
        console.log('📋 Initializing Modal Manager...');

        // Use existing overlay or create new one
        this.overlay = document.getElementById('modal-overlay');
        if (!this.overlay) {
            this.overlay = createElement('div', {
                className: 'modal-overlay',
                id: 'modal-overlay'
            });
            document.body.appendChild(this.overlay);
        }

        // Clear any existing content
        this.overlay.innerHTML = '';
        this.overlay.classList.add('hidden');

        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.close();
            }
        });

        console.log('✅ Modal Manager initialized');
    }

    /**
     * Show bulk add modal
     */
    showBulkAddModal(title, placeholder, onSubmit) {
        console.log('📋 ModalManager.showBulkAddModal called');
        console.log('Title:', title);
        console.log('Placeholder:', placeholder);

        const modal = this.createBulkAddModal(title, placeholder, onSubmit);
        console.log('📋 Modal created:', modal);

        // Test: Try direct overlay manipulation first
        console.log('📋 Testing direct overlay manipulation...');
        if (this.overlay) {
            console.log('📋 Overlay exists, attempting direct show...');
            this.overlay.appendChild(modal);
            this.overlay.classList.remove('hidden');
            this.overlay.style.display = 'flex';
            this.overlay.style.zIndex = '9999';
            this.overlay.style.position = 'fixed';
            this.overlay.style.top = '0';
            this.overlay.style.left = '0';
            this.overlay.style.width = '100vw';
            this.overlay.style.height = '100vh';
            this.overlay.style.backgroundColor = 'rgba(255, 0, 0, 0.8)'; // Red background for visibility test
            console.log('📋 Direct overlay manipulation completed');
        } else {
            console.error('❌ Overlay is null in showBulkAddModal');
        }

        this.show(modal);
        console.log('📋 Modal show() called');
    }

    /**
     * Create bulk add modal
     */
    createBulkAddModal(title, placeholder, onSubmit) {
        const modal = createElement('div', { className: 'modal bulk-add-modal' });
        
        // Header
        const header = createElement('div', { className: 'modal-header' });
        const titleEl = createElement('h2', { className: 'modal-title' }, title);
        const closeBtn = createElement('button', { 
            className: 'modal-close',
            'aria-label': 'Close modal'
        }, '×');
        closeBtn.addEventListener('click', () => this.close());
        
        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // Body
        const body = createElement('div', { className: 'modal-body' });
        
        // Email type selector (for emails)
        let emailTypeSelect = null;
        if (title.toLowerCase().includes('email')) {
            const emailTypeGroup = createElement('div', { className: 'form-group' });
            const emailTypeLabel = createElement('label', {}, 'Email Type:');
            emailTypeSelect = createElement('select', { 
                className: 'form-control',
                id: 'bulk-email-type'
            });
            
            const emailTypes = [
                { value: 'pm_emails', label: 'PM emails 30 off 35' },
                { value: 'ue_emails_25_25', label: 'UE emails 25 off 25' },
                { value: 'ue_emails_25_15', label: 'UE emails 25 off 15' },
                { value: 'ue_emails_25_1', label: 'UE emails 25 off 1' }
            ];
            
            emailTypes.forEach(type => {
                const option = createElement('option', { value: type.value }, type.label);
                emailTypeSelect.appendChild(option);
            });
            
            emailTypeGroup.appendChild(emailTypeLabel);
            emailTypeGroup.appendChild(emailTypeSelect);
            body.appendChild(emailTypeGroup);
        }
        
        // Textarea
        const textareaGroup = createElement('div', { className: 'form-group' });
        const textareaLabel = createElement('label', {}, 'Data:');
        const textarea = createElement('textarea', {
            className: 'form-control bulk-textarea',
            placeholder: placeholder,
            rows: '10'
        });
        
        textareaGroup.appendChild(textareaLabel);
        textareaGroup.appendChild(textarea);
        body.appendChild(textareaGroup);
        
        // Footer
        const footer = createElement('div', { className: 'modal-footer' });
        const cancelBtn = createElement('button', { 
            className: 'btn btn-secondary'
        }, 'Cancel');
        const submitBtn = createElement('button', { 
            className: 'btn btn-primary'
        }, 'Add');
        
        cancelBtn.addEventListener('click', () => this.close());
        submitBtn.addEventListener('click', () => {
            const data = textarea.value.trim();
            if (!data) return;
            
            const emailType = emailTypeSelect ? emailTypeSelect.value : null;
            onSubmit(data, emailType);
            this.close();
        });
        
        footer.appendChild(cancelBtn);
        footer.appendChild(submitBtn);
        
        // Assemble modal
        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        
        // Focus textarea when shown
        setTimeout(() => textarea.focus(), 100);
        
        return modal;
    }

    /**
     * Show import modal
     */
    showImportModal(onImport) {
        const modal = this.createImportModal(onImport);
        this.show(modal);
    }

    /**
     * Create import modal
     */
    createImportModal(onImport) {
        const modal = createElement('div', { className: 'modal import-modal' });
        
        // Header
        const header = createElement('div', { className: 'modal-header' });
        const titleEl = createElement('h2', { className: 'modal-title' }, 'Import Data');
        const closeBtn = createElement('button', { 
            className: 'modal-close',
            'aria-label': 'Close modal'
        }, '×');
        closeBtn.addEventListener('click', () => this.close());
        
        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // Body
        const body = createElement('div', { className: 'modal-body' });
        
        // File input
        const fileGroup = createElement('div', { className: 'form-group' });
        const fileLabel = createElement('label', {}, 'Select JSON file:');
        const fileInput = createElement('input', {
            type: 'file',
            className: 'form-control',
            accept: '.json'
        });
        
        fileGroup.appendChild(fileLabel);
        fileGroup.appendChild(fileInput);
        body.appendChild(fileGroup);
        
        // Or divider
        const divider = createElement('div', { className: 'import-divider' }, 'OR');
        body.appendChild(divider);
        
        // Textarea for JSON
        const textareaGroup = createElement('div', { className: 'form-group' });
        const textareaLabel = createElement('label', {}, 'Paste JSON data:');
        const textarea = createElement('textarea', {
            className: 'form-control import-textarea',
            placeholder: 'Paste your exported JSON data here...',
            rows: '8'
        });
        
        textareaGroup.appendChild(textareaLabel);
        textareaGroup.appendChild(textarea);
        body.appendChild(textareaGroup);
        
        // Warning
        const warning = createElement('div', { className: 'import-warning' });
        warning.innerHTML = '⚠️ <strong>Warning:</strong> Importing will replace all current data. Make sure to export your current data first if you want to keep it.';
        body.appendChild(warning);
        
        // Footer
        const footer = createElement('div', { className: 'modal-footer' });
        const cancelBtn = createElement('button', { 
            className: 'btn btn-secondary'
        }, 'Cancel');
        const importBtn = createElement('button', { 
            className: 'btn btn-danger'
        }, 'Import');
        
        cancelBtn.addEventListener('click', () => this.close());
        importBtn.addEventListener('click', async () => {
            try {
                let data = null;
                
                // Try file first
                if (fileInput.files.length > 0) {
                    const fileContent = await readFileAsText(fileInput.files[0]);
                    data = JSON.parse(fileContent);
                } else if (textarea.value.trim()) {
                    data = JSON.parse(textarea.value.trim());
                } else {
                    throw new Error('Please select a file or paste JSON data');
                }
                
                onImport(data);
                this.close();
                
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        });
        
        footer.appendChild(cancelBtn);
        footer.appendChild(importBtn);
        
        // Assemble modal
        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        
        return modal;
    }

    /**
     * Show settings modal
     */
    showSettingsModal(onSave) {
        const modal = this.createSettingsModal(onSave);
        this.show(modal);
    }

    /**
     * Create settings modal
     */
    createSettingsModal(onSave) {
        const modal = createElement('div', { className: 'modal settings-modal' });
        
        // Header
        const header = createElement('div', { className: 'modal-header' });
        const titleEl = createElement('h2', { className: 'modal-title' }, 'Settings');
        const closeBtn = createElement('button', { 
            className: 'modal-close',
            'aria-label': 'Close modal'
        }, '×');
        closeBtn.addEventListener('click', () => this.close());
        
        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // Body
        const body = createElement('div', { className: 'modal-body' });
        
        // Theme setting
        const themeGroup = createElement('div', { className: 'form-group' });
        const themeLabel = createElement('label', {}, 'Theme:');
        const themeSelect = createElement('select', { 
            className: 'form-control',
            id: 'theme-select'
        });
        
        const themes = [
            { value: 'dark', label: 'Dark' },
            { value: 'light', label: 'Light' },
            { value: 'auto', label: 'Auto (System)' }
        ];
        
        themes.forEach(theme => {
            const option = createElement('option', { value: theme.value }, theme.label);
            themeSelect.appendChild(option);
        });
        
        // Set current theme
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'dark';
        themeSelect.value = currentTheme;
        
        themeGroup.appendChild(themeLabel);
        themeGroup.appendChild(themeSelect);
        body.appendChild(themeGroup);
        
        // Auto-save setting
        const autoSaveGroup = createElement('div', { className: 'form-group' });
        const autoSaveLabel = createElement('label', {}, 'Auto-save interval (seconds):');
        const autoSaveInput = createElement('input', {
            type: 'number',
            className: 'form-control',
            min: '5',
            max: '300',
            value: '30'
        });
        
        autoSaveGroup.appendChild(autoSaveLabel);
        autoSaveGroup.appendChild(autoSaveInput);
        body.appendChild(autoSaveGroup);
        
        // Footer
        const footer = createElement('div', { className: 'modal-footer' });
        const cancelBtn = createElement('button', { 
            className: 'btn btn-secondary'
        }, 'Cancel');
        const saveBtn = createElement('button', { 
            className: 'btn btn-primary'
        }, 'Save');
        
        cancelBtn.addEventListener('click', () => this.close());
        saveBtn.addEventListener('click', () => {
            const settings = {
                theme: themeSelect.value,
                autoSaveInterval: parseInt(autoSaveInput.value, 10)
            };
            
            onSave(settings);
            this.close();
        });
        
        footer.appendChild(cancelBtn);
        footer.appendChild(saveBtn);
        
        // Assemble modal
        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        
        return modal;
    }

    /**
     * Show modal
     */
    async show(modal) {
        console.log('📋 ModalManager.show() START - immediate entry');

        try {
            console.log('📋 ModalManager.show() called - inside try block');
            console.log('📋 Modal element:', modal);
            console.log('📋 Overlay element:', this.overlay);
            console.log('📋 this object:', this);

            if (!this.overlay) {
                console.error('❌ Overlay element is null!');
                return;
            }

            console.log('📋 Overlay classes before:', this.overlay.className);
            console.log('📋 Overlay style before:', this.overlay.style.cssText);

            if (this.activeModal) {
                console.log('📋 Closing existing modal...');
                await this.close();
            }

            console.log('📋 Setting active modal and appending to overlay...');
            this.activeModal = modal;
            this.overlay.appendChild(modal);
            this.overlay.classList.remove('hidden');

            console.log('📋 Overlay classes after removing hidden:', this.overlay.className);
            console.log('📋 Overlay style after:', this.overlay.style.cssText);
            console.log('📋 Modal appended to overlay');

            // Force display and z-index to ensure visibility
            console.log('📋 Applying forced styles...');
            this.overlay.style.display = 'flex';
            this.overlay.style.zIndex = '2000';
            this.overlay.style.position = 'fixed';
            this.overlay.style.top = '0';
            this.overlay.style.left = '0';
            this.overlay.style.width = '100%';
            this.overlay.style.height = '100%';
            this.overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';

            console.log('📋 Forced overlay styles applied');
            console.log('📋 Final overlay style:', this.overlay.style.cssText);

            // Skip animation for now to test basic visibility
            console.log('📋 Skipping animation, modal should be visible now');

            // Focus first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                console.log('📋 Focusing first input:', firstInput);
                setTimeout(() => firstInput.focus(), 100);
            } else {
                console.log('📋 No input found to focus');
            }

            console.log('📋 Modal show() completed successfully');

        } catch (error) {
            console.error('❌ Error in modal show():', error);
            console.error('❌ Error stack:', error.stack);
        }
    }

    /**
     * Close modal
     */
    async close() {
        if (!this.activeModal) return;

        try {
            await animateElement(this.activeModal, 'fade-out');
        } catch (error) {
            // Animation failed, continue with close
            console.warn('Modal close animation failed:', error);
        }

        this.overlay.classList.add('hidden');

        if (this.activeModal && this.activeModal.parentNode) {
            this.activeModal.parentNode.removeChild(this.activeModal);
        }

        this.activeModal = null;
    }

    /**
     * Check if modal is open
     */
    isOpen() {
        return this.activeModal !== null;
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.activeModal) {
            this.close();
        }
        
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
        
        this.overlay = null;
        this.activeModal = null;
    }
}
