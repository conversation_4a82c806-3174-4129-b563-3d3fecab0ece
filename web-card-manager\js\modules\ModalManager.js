/**
 * Modal Manager - Handles modal dialogs
 */

import { createElement, animateElement, readFileAsText } from '../utils/helpers.js';

export class ModalManager {
    constructor() {
        this.activeModal = null;
        this.overlay = null;
    }

    /**
     * Initialize modal manager
     */
    async init() {
        console.log('📋 Initializing Modal Manager...');

        // Use existing overlay or create new one
        this.overlay = document.getElementById('modal-overlay');
        if (!this.overlay) {
            this.overlay = createElement('div', {
                className: 'modal-overlay',
                id: 'modal-overlay'
            });
            document.body.appendChild(this.overlay);
        }

        // Clear any existing content
        this.overlay.innerHTML = '';
        this.overlay.classList.add('hidden');

        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.closeSync();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeSync();
            }
        });

        console.log('✅ Modal Manager initialized');
    }

    /**
     * Show bulk add modal
     */
    showBulkAddModal(title, placeholder, onSubmit) {
        console.log('📋 ModalManager.showBulkAddModal called');
        console.log('Title:', title);
        console.log('Placeholder:', placeholder);

        try {
            const modal = this.createBulkAddModal(title, placeholder, onSubmit);
            console.log('📋 Modal created:', modal);

            // Close any existing modal first
            if (this.activeModal) {
                console.log('📋 Closing existing modal...');
                this.closeSync();
            }

            // Direct synchronous show implementation
            console.log('📋 Showing modal with direct implementation...');
            if (!this.overlay) {
                console.error('❌ Overlay is null in showBulkAddModal');
                return;
            }

            // Set active modal
            this.activeModal = modal;

            // Clear overlay and append modal
            this.overlay.innerHTML = '';
            this.overlay.appendChild(modal);

            // Remove hidden class and apply styles
            this.overlay.classList.remove('hidden');

            // CRITICAL: Remove any existing display:none first (preserves functional fix)
            this.overlay.style.removeProperty('display');

            // Apply sleek dark theme modal styling with functional fixes preserved
            this.overlay.style.setProperty('display', 'flex', 'important');
            this.overlay.style.alignItems = 'center';
            this.overlay.style.justifyContent = 'center';
            this.overlay.style.position = 'fixed';
            this.overlay.style.top = '0px';
            this.overlay.style.left = '0px';
            this.overlay.style.right = '0px';
            this.overlay.style.bottom = '0px';
            this.overlay.style.width = '100vw';
            this.overlay.style.height = '100vh';
            this.overlay.style.zIndex = '2000';
            this.overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.75)'; // Clean dark overlay - NO BLUR
            this.overlay.style.backdropFilter = 'none'; // Remove blur completely
            this.overlay.style.visibility = 'visible';
            this.overlay.style.opacity = '1';
            this.overlay.style.pointerEvents = 'auto';

            // Apply sleek dark modal with modern gradient design
            modal.style.display = 'block';
            modal.style.position = 'relative';
            modal.style.zIndex = '2001';
            modal.style.background = 'linear-gradient(145deg, #1e2329, #181c22)'; // Clean dark gradient - NO TRANSPARENCY
            modal.style.backdropFilter = 'none'; // Remove blur completely
            modal.style.border = '1px solid rgba(100, 120, 150, 0.3)'; // Subtle blue-gray border
            modal.style.padding = '28px';
            modal.style.borderRadius = '20px';
            modal.style.maxWidth = '520px';
            modal.style.width = '92%';
            modal.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.4), 0 10px 20px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'; // Layered shadows with inner highlight
            modal.style.color = '#e8eaed'; // Light text for dark theme

            // Style all form elements for dark theme
            this.applyDarkThemeFormStyling(modal);

            console.log('📋 Applied sleek dark theme modal styling');

            console.log('📋 Modal displayed successfully with professional styling');

            // Focus first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                setTimeout(() => {
                    firstInput.focus();
                    console.log('📋 Focused first input');
                }, 100);
            }

        } catch (error) {
            console.error('❌ Error in showBulkAddModal:', error);
            console.error('❌ Error stack:', error.stack);
        }
    }

    /**
     * Apply dark theme styling to form elements
     */
    applyDarkThemeFormStyling(modal) {
        // Style modal header elements
        const title = modal.querySelector('.modal-title');
        if (title) {
            title.style.color = '#ffffff';
            title.style.fontSize = '1.5rem';
            title.style.fontWeight = '600';
            title.style.margin = '0';
        }

        const closeBtn = modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.1)';
            closeBtn.style.border = '1px solid rgba(255, 255, 255, 0.2)';
            closeBtn.style.color = '#ffffff';
            closeBtn.style.borderRadius = '8px';
            closeBtn.style.padding = '8px 12px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.transition = 'all 0.2s ease';
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
            });
            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.background = 'rgba(255, 255, 255, 0.1)';
            });
        }

        // Style labels
        const labels = modal.querySelectorAll('label');
        labels.forEach(label => {
            label.style.color = '#b8bcc8';
            label.style.fontSize = '0.9rem';
            label.style.fontWeight = '500';
            label.style.marginBottom = '8px';
            label.style.display = 'block';
        });

        // Style form controls (inputs, selects, textareas)
        const formControls = modal.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.style.background = 'rgba(40, 45, 55, 0.8)';
            control.style.border = '1px solid rgba(100, 120, 150, 0.3)';
            control.style.borderRadius = '8px';
            control.style.color = '#ffffff';
            control.style.padding = '12px';
            control.style.fontSize = '0.95rem';
            control.style.transition = 'all 0.2s ease';
            control.style.outline = 'none';

            // Focus states
            control.addEventListener('focus', () => {
                control.style.borderColor = 'rgba(100, 150, 200, 0.6)';
                control.style.boxShadow = '0 0 0 3px rgba(100, 150, 200, 0.1)';
            });
            control.addEventListener('blur', () => {
                control.style.borderColor = 'rgba(100, 120, 150, 0.3)';
                control.style.boxShadow = 'none';
            });
        });

        // Style buttons
        const buttons = modal.querySelectorAll('.btn');
        buttons.forEach(btn => {
            btn.style.borderRadius = '10px';
            btn.style.padding = '12px 24px';
            btn.style.fontSize = '0.95rem';
            btn.style.fontWeight = '500';
            btn.style.cursor = 'pointer';
            btn.style.transition = 'all 0.2s ease';
            btn.style.border = 'none';
            btn.style.outline = 'none';

            if (btn.classList.contains('btn-primary')) {
                btn.style.background = 'linear-gradient(135deg, #4f7cff, #3b5bdb)';
                btn.style.color = '#ffffff';
                btn.style.boxShadow = '0 4px 12px rgba(79, 124, 255, 0.3)';

                btn.addEventListener('mouseenter', () => {
                    btn.style.transform = 'translateY(-1px)';
                    btn.style.boxShadow = '0 6px 16px rgba(79, 124, 255, 0.4)';
                });
                btn.addEventListener('mouseleave', () => {
                    btn.style.transform = 'translateY(0)';
                    btn.style.boxShadow = '0 4px 12px rgba(79, 124, 255, 0.3)';
                });
            } else {
                btn.style.background = 'rgba(255, 255, 255, 0.1)';
                btn.style.color = '#b8bcc8';
                btn.style.border = '1px solid rgba(255, 255, 255, 0.2)';

                btn.addEventListener('mouseenter', () => {
                    btn.style.background = 'rgba(255, 255, 255, 0.15)';
                    btn.style.color = '#ffffff';
                });
                btn.addEventListener('mouseleave', () => {
                    btn.style.background = 'rgba(255, 255, 255, 0.1)';
                    btn.style.color = '#b8bcc8';
                });
            }
        });

        // Style form groups
        const formGroups = modal.querySelectorAll('.form-group');
        formGroups.forEach(group => {
            group.style.marginBottom = '20px';
        });

        // Style modal sections
        const header = modal.querySelector('.modal-header');
        if (header) {
            header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
            header.style.paddingBottom = '16px';
            header.style.marginBottom = '24px';
            header.style.display = 'flex';
            header.style.justifyContent = 'space-between';
            header.style.alignItems = 'center';
        }

        const footer = modal.querySelector('.modal-footer');
        if (footer) {
            footer.style.borderTop = '1px solid rgba(255, 255, 255, 0.1)';
            footer.style.paddingTop = '20px';
            footer.style.marginTop = '24px';
            footer.style.display = 'flex';
            footer.style.gap = '12px';
            footer.style.justifyContent = 'flex-end';
        }
    }

    /**
     * Create bulk add modal
     */
    createBulkAddModal(title, placeholder, onSubmit) {
        const modal = createElement('div', { className: 'modal bulk-add-modal' });
        
        // Header
        const header = createElement('div', { className: 'modal-header' });
        const titleEl = createElement('h2', { className: 'modal-title' }, title);
        const closeBtn = createElement('button', { 
            className: 'modal-close',
            'aria-label': 'Close modal'
        }, '×');
        closeBtn.addEventListener('click', () => this.closeSync());
        
        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // Body
        const body = createElement('div', { className: 'modal-body' });
        
        // Email type selector (for emails)
        let emailTypeSelect = null;
        if (title.toLowerCase().includes('email')) {
            const emailTypeGroup = createElement('div', { className: 'form-group' });
            const emailTypeLabel = createElement('label', {}, 'Email Type:');
            emailTypeSelect = createElement('select', { 
                className: 'form-control',
                id: 'bulk-email-type'
            });
            
            const emailTypes = [
                { value: 'pm_emails', label: 'PM emails 30 off 35' },
                { value: 'ue_emails_25_25', label: 'UE emails 25 off 25' },
                { value: 'ue_emails_25_15', label: 'UE emails 25 off 15' },
                { value: 'ue_emails_25_1', label: 'UE emails 25 off 1' }
            ];
            
            emailTypes.forEach(type => {
                const option = createElement('option', { value: type.value }, type.label);
                emailTypeSelect.appendChild(option);
            });
            
            emailTypeGroup.appendChild(emailTypeLabel);
            emailTypeGroup.appendChild(emailTypeSelect);
            body.appendChild(emailTypeGroup);
        }
        
        // Textarea
        const textareaGroup = createElement('div', { className: 'form-group' });
        const textareaLabel = createElement('label', {}, 'Data:');
        const textarea = createElement('textarea', {
            className: 'form-control bulk-textarea',
            placeholder: placeholder,
            rows: '10'
        });
        
        textareaGroup.appendChild(textareaLabel);
        textareaGroup.appendChild(textarea);
        body.appendChild(textareaGroup);
        
        // Footer
        const footer = createElement('div', { className: 'modal-footer' });
        const cancelBtn = createElement('button', { 
            className: 'btn btn-secondary'
        }, 'Cancel');
        const submitBtn = createElement('button', { 
            className: 'btn btn-primary'
        }, 'Add');
        
        cancelBtn.addEventListener('click', () => this.closeSync());
        submitBtn.addEventListener('click', () => {
            const data = textarea.value.trim();
            if (!data) return;
            
            const emailType = emailTypeSelect ? emailTypeSelect.value : null;
            onSubmit(data, emailType);
            this.closeSync();
        });
        
        footer.appendChild(cancelBtn);
        footer.appendChild(submitBtn);
        
        // Assemble modal
        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        
        // Focus textarea when shown
        setTimeout(() => textarea.focus(), 100);
        
        return modal;
    }

    /**
     * Show import modal
     */
    showImportModal(onImport) {
        console.log('📋 ModalManager.showImportModal called');

        try {
            const modal = this.createImportModal(onImport);
            console.log('📋 Import modal created:', modal);

            // Close any existing modal first
            if (this.activeModal) {
                console.log('📋 Closing existing modal...');
                this.closeSync();
            }

            // Use direct synchronous show implementation (same as bulk add)
            console.log('📋 Showing import modal with direct implementation...');
            if (!this.overlay) {
                console.error('❌ Overlay is null in showImportModal');
                return;
            }

            // Set active modal and append to overlay
            this.activeModal = modal;
            this.overlay.appendChild(modal);

            // Apply clean dark overlay styling (no blur)
            this.overlay.style.setProperty('display', 'flex', 'important');
            this.overlay.style.alignItems = 'center';
            this.overlay.style.justifyContent = 'center';
            this.overlay.style.position = 'fixed';
            this.overlay.style.top = '0px';
            this.overlay.style.left = '0px';
            this.overlay.style.right = '0px';
            this.overlay.style.bottom = '0px';
            this.overlay.style.width = '100vw';
            this.overlay.style.height = '100vh';
            this.overlay.style.zIndex = '2000';
            this.overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.75)'; // Clean dark overlay - NO BLUR
            this.overlay.style.backdropFilter = 'none'; // Remove blur completely
            this.overlay.style.visibility = 'visible';
            this.overlay.style.opacity = '1';
            this.overlay.style.pointerEvents = 'auto';

            // Apply clean dark modal styling (no blur)
            modal.style.display = 'block';
            modal.style.position = 'relative';
            modal.style.zIndex = '2001';
            modal.style.background = 'linear-gradient(145deg, #1e2329, #181c22)'; // Clean dark gradient - NO TRANSPARENCY
            modal.style.backdropFilter = 'none'; // Remove blur completely
            modal.style.border = '1px solid rgba(100, 120, 150, 0.3)'; // Subtle blue-gray border
            modal.style.padding = '28px';
            modal.style.borderRadius = '20px';
            modal.style.maxWidth = '520px';
            modal.style.width = '92%';
            modal.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.4), 0 10px 20px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'; // Layered shadows with inner highlight
            modal.style.color = '#e8eaed'; // Light text for dark theme

            // Style all form elements for dark theme
            this.applyDarkThemeFormStyling(modal);

            console.log('📋 Import modal displayed successfully with professional styling');

            // Focus first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                setTimeout(() => {
                    firstInput.focus();
                    console.log('📋 Focused first input in import modal');
                }, 100);
            }

        } catch (error) {
            console.error('❌ Error in showImportModal:', error);
            console.error('❌ Error stack:', error.stack);
        }
    }

    /**
     * Create import modal
     */
    createImportModal(onImport) {
        const modal = createElement('div', { className: 'modal import-modal' });
        
        // Header
        const header = createElement('div', { className: 'modal-header' });
        const titleEl = createElement('h2', { className: 'modal-title' }, 'Import Data');
        const closeBtn = createElement('button', { 
            className: 'modal-close',
            'aria-label': 'Close modal'
        }, '×');
        closeBtn.addEventListener('click', () => this.closeSync());

        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // Body
        const body = createElement('div', { className: 'modal-body' });
        
        // File input
        const fileGroup = createElement('div', { className: 'form-group' });
        const fileLabel = createElement('label', {}, 'Select JSON file:');
        const fileInput = createElement('input', {
            type: 'file',
            className: 'form-control',
            accept: '.json'
        });
        
        fileGroup.appendChild(fileLabel);
        fileGroup.appendChild(fileInput);
        body.appendChild(fileGroup);
        
        // Or divider
        const divider = createElement('div', { className: 'import-divider' }, 'OR');
        body.appendChild(divider);
        
        // Textarea for JSON
        const textareaGroup = createElement('div', { className: 'form-group' });
        const textareaLabel = createElement('label', {}, 'Paste JSON data:');
        const textarea = createElement('textarea', {
            className: 'form-control import-textarea',
            placeholder: 'Paste your exported JSON data here...',
            rows: '8'
        });
        
        textareaGroup.appendChild(textareaLabel);
        textareaGroup.appendChild(textarea);
        body.appendChild(textareaGroup);
        
        // Warning
        const warning = createElement('div', { className: 'import-warning' });
        warning.innerHTML = '⚠️ <strong>Warning:</strong> Importing will replace all current data. Make sure to export your current data first if you want to keep it.';
        body.appendChild(warning);
        
        // Footer
        const footer = createElement('div', { className: 'modal-footer' });
        const cancelBtn = createElement('button', { 
            className: 'btn btn-secondary'
        }, 'Cancel');
        const importBtn = createElement('button', { 
            className: 'btn btn-danger'
        }, 'Import');
        
        cancelBtn.addEventListener('click', () => this.closeSync());
        importBtn.addEventListener('click', async () => {
            try {
                let data = null;
                
                // Try file first
                if (fileInput.files.length > 0) {
                    const fileContent = await readFileAsText(fileInput.files[0]);
                    data = JSON.parse(fileContent);
                } else if (textarea.value.trim()) {
                    data = JSON.parse(textarea.value.trim());
                } else {
                    throw new Error('Please select a file or paste JSON data');
                }
                
                onImport(data);
                this.closeSync();
                
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        });
        
        footer.appendChild(cancelBtn);
        footer.appendChild(importBtn);
        
        // Assemble modal
        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        
        return modal;
    }

    /**
     * Show settings modal
     */
    showSettingsModal(onSave) {
        const modal = this.createSettingsModal(onSave);
        this.show(modal);
    }

    /**
     * Create settings modal
     */
    createSettingsModal(onSave) {
        const modal = createElement('div', { className: 'modal settings-modal' });
        
        // Header
        const header = createElement('div', { className: 'modal-header' });
        const titleEl = createElement('h2', { className: 'modal-title' }, 'Settings');
        const closeBtn = createElement('button', { 
            className: 'modal-close',
            'aria-label': 'Close modal'
        }, '×');
        closeBtn.addEventListener('click', () => this.closeSync());

        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // Body
        const body = createElement('div', { className: 'modal-body' });
        
        // Theme setting
        const themeGroup = createElement('div', { className: 'form-group' });
        const themeLabel = createElement('label', {}, 'Theme:');
        const themeSelect = createElement('select', { 
            className: 'form-control',
            id: 'theme-select'
        });
        
        const themes = [
            { value: 'dark', label: 'Dark' },
            { value: 'light', label: 'Light' },
            { value: 'auto', label: 'Auto (System)' }
        ];
        
        themes.forEach(theme => {
            const option = createElement('option', { value: theme.value }, theme.label);
            themeSelect.appendChild(option);
        });
        
        // Set current theme
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'dark';
        themeSelect.value = currentTheme;
        
        themeGroup.appendChild(themeLabel);
        themeGroup.appendChild(themeSelect);
        body.appendChild(themeGroup);
        
        // Auto-save setting
        const autoSaveGroup = createElement('div', { className: 'form-group' });
        const autoSaveLabel = createElement('label', {}, 'Auto-save interval (seconds):');
        const autoSaveInput = createElement('input', {
            type: 'number',
            className: 'form-control',
            min: '5',
            max: '300',
            value: '30'
        });
        
        autoSaveGroup.appendChild(autoSaveLabel);
        autoSaveGroup.appendChild(autoSaveInput);
        body.appendChild(autoSaveGroup);
        
        // Footer
        const footer = createElement('div', { className: 'modal-footer' });
        const cancelBtn = createElement('button', { 
            className: 'btn btn-secondary'
        }, 'Cancel');
        const saveBtn = createElement('button', { 
            className: 'btn btn-primary'
        }, 'Save');
        
        cancelBtn.addEventListener('click', () => this.closeSync());
        saveBtn.addEventListener('click', () => {
            const settings = {
                theme: themeSelect.value,
                autoSaveInterval: parseInt(autoSaveInput.value, 10)
            };
            
            onSave(settings);
            this.closeSync();
        });
        
        footer.appendChild(cancelBtn);
        footer.appendChild(saveBtn);
        
        // Assemble modal
        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        
        return modal;
    }

    /**
     * Show modal
     */
    async show(modal) {
        console.log('📋 ModalManager.show() START - immediate entry');

        try {
            console.log('📋 ModalManager.show() called - inside try block');
            console.log('📋 Modal element:', modal);
            console.log('📋 Overlay element:', this.overlay);
            console.log('📋 this object:', this);

            if (!this.overlay) {
                console.error('❌ Overlay element is null!');
                return;
            }

            console.log('📋 Overlay classes before:', this.overlay.className);
            console.log('📋 Overlay style before:', this.overlay.style.cssText);

            if (this.activeModal) {
                console.log('📋 Closing existing modal...');
                this.closeSync();
            }

            console.log('📋 Setting active modal and appending to overlay...');
            this.activeModal = modal;
            this.overlay.appendChild(modal);
            this.overlay.classList.remove('hidden');

            console.log('📋 Overlay classes after removing hidden:', this.overlay.className);
            console.log('📋 Overlay style after:', this.overlay.style.cssText);
            console.log('📋 Modal appended to overlay');

            // Force display and z-index to ensure visibility
            console.log('📋 Applying forced styles...');
            this.overlay.style.display = 'flex';
            this.overlay.style.zIndex = '2000';
            this.overlay.style.position = 'fixed';
            this.overlay.style.top = '0';
            this.overlay.style.left = '0';
            this.overlay.style.width = '100%';
            this.overlay.style.height = '100%';
            this.overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';

            console.log('📋 Forced overlay styles applied');
            console.log('📋 Final overlay style:', this.overlay.style.cssText);

            // Skip animation for now to test basic visibility
            console.log('📋 Skipping animation, modal should be visible now');

            // Focus first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                console.log('📋 Focusing first input:', firstInput);
                setTimeout(() => firstInput.focus(), 100);
            } else {
                console.log('📋 No input found to focus');
            }

            console.log('📋 Modal show() completed successfully');

        } catch (error) {
            console.error('❌ Error in modal show():', error);
            console.error('❌ Error stack:', error.stack);
        }
    }

    /**
     * Close modal
     */
    async close() {
        if (!this.activeModal) return;

        try {
            await animateElement(this.activeModal, 'fade-out');
        } catch (error) {
            // Animation failed, continue with close
            console.warn('Modal close animation failed:', error);
        }

        this.overlay.classList.add('hidden');

        if (this.activeModal && this.activeModal.parentNode) {
            this.activeModal.parentNode.removeChild(this.activeModal);
        }

        this.activeModal = null;
    }

    /**
     * Close modal synchronously (without animation)
     */
    closeSync() {
        console.log('📋 Closing modal synchronously...');

        if (!this.activeModal) {
            console.log('📋 No active modal to close');
            return;
        }

        // Hide overlay and clear all inline styles (preserves functional fix)
        this.overlay.classList.add('hidden');
        this.overlay.style.cssText = ''; // Clear ALL inline styles to prevent conflicts

        // Remove modal from DOM
        if (this.activeModal && this.activeModal.parentNode) {
            this.activeModal.parentNode.removeChild(this.activeModal);
        }

        this.activeModal = null;
        console.log('📋 Modal closed synchronously');
    }

    /**
     * Check if modal is open
     */
    isOpen() {
        return this.activeModal !== null;
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.activeModal) {
            this.closeSync();
        }
        
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
        
        this.overlay = null;
        this.activeModal = null;
    }
}
