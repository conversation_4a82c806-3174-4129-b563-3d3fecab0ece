main.js:352 🌟 DOM Content Loaded - Starting Card Manager...
main.js:38 🚀 Initializing Card Manager...
main.js:41 📺 Step 1: Showing loading screen...
main.js:43 ✅ Step 1 completed: Loading screen shown
main.js:46 📋 Step 2: Initializing managers...
ConfigManager.js:31 ⚙️ Initializing Config Manager...
ConfigManager.js:39 ✅ Config Manager initialized
ToastManager.js:19 🍞 Initializing Toast Manager...
ToastManager.js:31 ✅ Toast Manager initialized
DataManager.js:42 📊 Initializing Data Manager...
DataManager.js:531 ✅ Data loaded successfully
DataManager.js:47 ✅ Data Manager initialized
SearchManager.js:26 🔍 Initializing Search Manager...
SearchManager.js:27 ✅ Search Manager initialized
HistoryManager.js:21 ⏱️ Initializing History Manager...
HistoryManager.js:29 ✅ History Manager initialized
ModalManager.js:17 📋 Initializing Modal Manager...
ModalManager.js:46 ✅ Modal Manager initialized
UIManager.js:38 🎨 Initializing UI Manager...
UIManager.js:220 ✅ Adding click listener to settings-btn
UIManager.js:243 🔧 Setting up bulk operation listeners...
UIManager.js:250 Bulk add cards button: <button id=​"bulk-add-cards-btn" class=​"secondary-btn">​…​</button>​flex
UIManager.js:251 Bulk add emails button: <button id=​"bulk-add-emails-btn" class=​"secondary-btn">​…​</button>​flex
UIManager.js:256 ✅ Adding click listener to bulk-add-cards-btn
UIManager.js:269 ✅ Adding click listener to bulk-add-emails-btn
UIManager.js:286 🔧 Initial bulk listener setup: 2/2 buttons found
UIManager.js:52 ✅ UI Manager initialized
AutoSaveManager.js:27 💾 Initializing Auto Save Manager...
AutoSaveManager.js:44 ✅ Auto Save Manager initialized
main.js:48 ✅ Step 2 completed: Managers initialized
main.js:51 👂 Step 3: Setting up event listeners...
main.js:53 ✅ Step 3 completed: Event listeners set up
main.js:56 ⚙️ Step 4: Applying initial configuration...
main.js:58 ✅ Step 4 completed: Configuration applied
main.js:61 💾 Step 5: Loading initial data...
DataManager.js:531 ✅ Data loaded successfully
main.js:63 ✅ Step 5 completed: Data loaded
main.js:66 🎨 Step 6: Showing application...
main.js:227 🎨 Showing application...
main.js:232 Loading screen element: <div id=​"loading-screen" class=​"loading-screen" style=​"display:​ none;​">​…​</div>​
main.js:233 Main app element: <div id=​"main-app" class=​"main-app" style=​"display:​ flex;​">​…​</div>​flex
main.js:246 Hiding loading screen...
main.js:250 Showing main app...
main.js:254 ✅ Application shown successfully
main.js:68 ✅ Step 6 completed: Application shown
main.js:75 🎉 Card Manager initialized successfully in 448.70ms
main.js:360 🎉 Card Manager fully loaded and ready!
cs.min.js:1 cornhusk, communicator, TypeError: Failed to construct 'URL': Invalid URL, Invalid url: 
extractOriginPath @ cs.min.js:1
c @ cs.min.js:1
(anonymous) @ cs.min.js:1
main.js:1 [UI] Error while fetching feature flags
error @ main.js:1
(anonymous) @ main.js:1
lM @ main.js:1
a @ main.js:1
invoke @ polyfills.js:1
onInvoke @ main.js:1
invoke @ polyfills.js:1
run @ polyfills.js:1
(anonymous) @ polyfills.js:1
invokeTask @ polyfills.js:1
onInvokeTask @ main.js:1
invokeTask @ polyfills.js:1
runTask @ polyfills.js:1
w @ polyfills.js:1
Promise.then
Ae @ polyfills.js:1
G @ polyfills.js:1
scheduleTask @ polyfills.js:1
onScheduleTask @ polyfills.js:1
scheduleTask @ polyfills.js:1
scheduleTask @ polyfills.js:1
scheduleMicroTask @ polyfills.js:1
Se @ polyfills.js:1
Te @ polyfills.js:1
(anonymous) @ polyfills.js:1
(anonymous) @ polyfills.js:1
Fe @ polyfills.js:1
main.js:1 [UI] Error loading user-notice state from storage Not found: viewCountMultiUserDetails
error @ main.js:1
(anonymous) @ main.js:1
lM @ main.js:1
a @ main.js:1
invoke @ polyfills.js:1
onInvoke @ main.js:1
invoke @ polyfills.js:1
run @ polyfills.js:1
(anonymous) @ polyfills.js:1
invokeTask @ polyfills.js:1
onInvokeTask @ main.js:1
invokeTask @ polyfills.js:1
runTask @ polyfills.js:1
w @ polyfills.js:1
Promise.then
Ae @ polyfills.js:1
G @ polyfills.js:1
scheduleTask @ polyfills.js:1
onScheduleTask @ polyfills.js:1
scheduleTask @ polyfills.js:1
scheduleTask @ polyfills.js:1
scheduleMicroTask @ polyfills.js:1
Se @ polyfills.js:1
Te @ polyfills.js:1
(anonymous) @ polyfills.js:1
(anonymous) @ polyfills.js:1
Fe @ polyfills.js:1
browserFingerprint.min.js:147 Canvas2D: Multiple readback operations using getImageData are faster with the willReadFrequently attribute set to true. See: https://html.spec.whatwg.org/multipage/canvas.html#concept-canvas-will-read-frequently
fnBrowserFontSmoothingEnabled @ browserFingerprint.min.js:147
fnBrowserScreen @ browserFingerprint.min.js:115
collectDFP @ browserFingerprint.min.js:1382
(anonymous) @ main.js:1
invoke @ polyfills.js:1
onInvoke @ main.js:1
invoke @ polyfills.js:1
run @ polyfills.js:1
(anonymous) @ polyfills.js:1
invokeTask @ polyfills.js:1
onInvokeTask @ main.js:1
invokeTask @ polyfills.js:1
runTask @ polyfills.js:1
w @ polyfills.js:1
Promise.then
Ae @ polyfills.js:1
G @ polyfills.js:1
scheduleTask @ polyfills.js:1
onScheduleTask @ polyfills.js:1
scheduleTask @ polyfills.js:1
scheduleTask @ polyfills.js:1
scheduleMicroTask @ polyfills.js:1
Se @ polyfills.js:1
Te @ polyfills.js:1
(anonymous) @ polyfills.js:1
(anonymous) @ polyfills.js:1
Fe @ polyfills.js:1
index.html:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
index.html:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
index.html:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
chrome-error://chromewebdata/:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
UIManager.js:258 🖱️ Bulk add cards button clicked!
UIManager.js:259 Event details: PointerEvent {isTrusted: true, pointerId: 1, width: 1, height: 1, pressure: 0, …}
UIManager.js:260 Modal manager available: true
UIManager.js:1397 📋 showBulkAddModal called with type: cards
UIManager.js:1398 📋 Modal manager available: true
UIManager.js:1399 📋 Modal manager instance: ModalManager {activeModal: null, overlay: div#modal-overlay.modal-overlay.hidden}
UIManager.js:1415 📋 Calling modalManager.showBulkAddModal...
UIManager.js:1416 📋 Modal manager showBulkAddModal method: function
ModalManager.js:53 📋 ModalManager.showBulkAddModal called
ModalManager.js:54 Title: Bulk Add Cards
ModalManager.js:55 Placeholder: Enter cards (one per line)
Format: number,month,year,cvv,zip
ModalManager.js:59 📋 Modal created: <div class=​"modal bulk-add-modal" style=​"display:​ block;​ position:​ relative;​ z-index:​ 1000000;​ background-color:​ white;​ padding:​ 20px;​ border-radius:​ 8px;​ max-width:​ 500px;​ width:​ 90%;​ box-shadow:​ rgba(0, 0, 0, 0.5)​ 0px 10px 30px;​">​…​</div>​
ModalManager.js:68 📋 Showing modal with direct implementation...
ModalManager.js:113 📋 Applied MAXIMUM visibility styles
ModalManager.js:114 📋 Overlay computed style: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
ModalManager.js:115 📋 Modal computed style: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
ModalManager.js:117 📋 Modal displayed successfully
ModalManager.js:118 📋 Overlay final style: align-items: center; justify-content: center; position: fixed; inset: 0px; width: 100vw; height: 100vh; z-index: 999999; background-color: rgba(255, 0, 0, 0.8); backdrop-filter: none; visibility: visible; opacity: 1; pointer-events: auto;
ModalManager.js:121 📋 DOM INSPECTION:
ModalManager.js:122 📋 Overlay element: <div id=​"modal-overlay" class=​"modal-overlay" style=​"align-items:​ center;​ justify-content:​ center;​ position:​ fixed;​ inset:​ 0px;​ width:​ 100vw;​ height:​ 100vh;​ z-index:​ 999999;​ background-color:​ rgba(255, 0, 0, 0.8)​;​ backdrop-filter:​ none;​ visibility:​ visible;​ opacity:​ 1;​ pointer-events:​ auto;​">​…​</div>​flex
ModalManager.js:123 📋 Overlay parent: <div id=​"app" class=​"app">​…​</div>​flex
ModalManager.js:124 📋 Overlay children count: 1
ModalManager.js:125 📋 Overlay getBoundingClientRect: DOMRect {x: 0, y: 0, width: 1143.3333740234375, height: 1026.666748046875, top: 0, …}
ModalManager.js:126 📋 Modal element: <div class=​"modal bulk-add-modal" style=​"display:​ block;​ position:​ relative;​ z-index:​ 1000000;​ background-color:​ white;​ padding:​ 20px;​ border-radius:​ 8px;​ max-width:​ 500px;​ width:​ 90%;​ box-shadow:​ rgba(0, 0, 0, 0.5)​ 0px 10px 30px;​">​…​</div>​
ModalManager.js:127 📋 Modal getBoundingClientRect: DOMRect {x: 346.66668701171875, y: 260.5824890136719, width: 450.0000305175781, height: 469.4844055175781, top: 260.5824890136719, …}
ModalManager.js:128 📋 Document body: <body>​…​</body>​
ModalManager.js:129 📋 Document body children: (5) ['DIV#app', 'SCRIPT#', 'DIV#cap-one-ext-container', 'LINK#', 'IFRAME#']
ModalManager.js:133 📋 Overlay is in DOM: true
ModalManager.js:137 📋 Modal is in overlay: true
UIManager.js:1423 📋 Modal manager call completed successfully
ModalManager.js:144 📋 Focused first input
ModalManager.js:551 📋 Closing modal synchronously...
ModalManager.js:568 📋 Modal closed synchronously
UIManager.js:258 🖱️ Bulk add cards button clicked!
UIManager.js:259 Event details: PointerEvent {isTrusted: true, pointerId: 1, width: 1, height: 1, pressure: 0, …}
UIManager.js:260 Modal manager available: true
UIManager.js:1397 📋 showBulkAddModal called with type: cards
UIManager.js:1398 📋 Modal manager available: true
UIManager.js:1399 📋 Modal manager instance: ModalManager {activeModal: null, overlay: div#modal-overlay.modal-overlay.hidden}
UIManager.js:1415 📋 Calling modalManager.showBulkAddModal...
UIManager.js:1416 📋 Modal manager showBulkAddModal method: function
ModalManager.js:53 📋 ModalManager.showBulkAddModal called
ModalManager.js:54 Title: Bulk Add Cards
ModalManager.js:55 Placeholder: Enter cards (one per line)
Format: number,month,year,cvv,zip
ModalManager.js:59 📋 Modal created: <div class=​"modal bulk-add-modal" style=​"display:​ block;​ position:​ relative;​ z-index:​ 1000000;​ background-color:​ white;​ padding:​ 20px;​ border-radius:​ 8px;​ max-width:​ 500px;​ width:​ 90%;​ box-shadow:​ rgba(0, 0, 0, 0.5)​ 0px 10px 30px;​">​…​</div>​
ModalManager.js:68 📋 Showing modal with direct implementation...
ModalManager.js:113 📋 Applied MAXIMUM visibility styles
ModalManager.js:114 📋 Overlay computed style: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
ModalManager.js:115 📋 Modal computed style: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
ModalManager.js:117 📋 Modal displayed successfully
ModalManager.js:118 📋 Overlay final style: align-items: center; justify-content: center; position: fixed; inset: 0px; width: 100vw; height: 100vh; z-index: 999999; background-color: rgba(255, 0, 0, 0.8); backdrop-filter: none; visibility: visible; opacity: 1; pointer-events: auto; display: none;
ModalManager.js:121 📋 DOM INSPECTION:
ModalManager.js:122 📋 Overlay element: <div id=​"modal-overlay" class=​"modal-overlay" style=​"align-items:​ center;​ justify-content:​ center;​ position:​ fixed;​ inset:​ 0px;​ width:​ 100vw;​ height:​ 100vh;​ z-index:​ 999999;​ background-color:​ rgba(255, 0, 0, 0.8)​;​ backdrop-filter:​ none;​ visibility:​ visible;​ opacity:​ 1;​ pointer-events:​ auto;​ display:​ none;​">​…​</div>​
ModalManager.js:123 📋 Overlay parent: <div id=​"app" class=​"app">​…​</div>​flex
ModalManager.js:124 📋 Overlay children count: 1
ModalManager.js:125 📋 Overlay getBoundingClientRect: DOMRect {x: 0, y: 0, width: 0, height: 0, top: 0, …}
ModalManager.js:126 📋 Modal element: <div class=​"modal bulk-add-modal" style=​"display:​ block;​ position:​ relative;​ z-index:​ 1000000;​ background-color:​ white;​ padding:​ 20px;​ border-radius:​ 8px;​ max-width:​ 500px;​ width:​ 90%;​ box-shadow:​ rgba(0, 0, 0, 0.5)​ 0px 10px 30px;​">​…​</div>​
ModalManager.js:127 📋 Modal getBoundingClientRect: DOMRect {x: 0, y: 0, width: 0, height: 0, top: 0, …}
ModalManager.js:128 📋 Document body: <body>​…​</body>​
ModalManager.js:129 📋 Document body children: (5) ['DIV#app', 'SCRIPT#', 'DIV#cap-one-ext-container', 'LINK#', 'IFRAME#']
ModalManager.js:133 📋 Overlay is in DOM: true
ModalManager.js:137 📋 Modal is in overlay: true
UIManager.js:1423 📋 Modal manager call completed successfully
ModalManager.js:144 📋 Focused first input
