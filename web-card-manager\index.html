<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Manager - Modern Web Application</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Loading Card Manager...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <!-- Header Toolbar -->
            <header class="toolbar">
                <div class="toolbar-left">
                    <h1 class="app-title">
                        <i class="fas fa-credit-card"></i>
                        Card Manager
                    </h1>
                </div>
                
                <div class="toolbar-center">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input 
                            type="text" 
                            id="global-search" 
                            class="search-input" 
                            placeholder="Search cards, emails, or combinations..."
                            autocomplete="off"
                        >
                        <button id="clear-search" class="clear-search-btn" title="Clear search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="toolbar-right">
                    <div class="action-buttons">
                        <button id="undo-btn" class="toolbar-btn" title="Undo last action" disabled>
                            <i class="fas fa-undo"></i>
                            <span>Undo</span>
                        </button>
                        <button id="redo-btn" class="toolbar-btn" title="Redo last action" disabled>
                            <i class="fas fa-redo"></i>
                            <span>Redo</span>
                        </button>
                        <div class="divider"></div>
                        <button id="import-btn" class="toolbar-btn" title="Import data">
                            <i class="fas fa-file-import"></i>
                            <span>Import</span>
                        </button>
                        <button id="export-btn" class="toolbar-btn" title="Export data">
                            <i class="fas fa-file-export"></i>
                            <span>Export</span>
                        </button>
                        <div class="divider"></div>
                        <button id="settings-btn" class="toolbar-btn" title="Settings">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <div class="status-indicator">
                        <span id="performance-indicator" class="performance-badge">
                            <i class="fas fa-bolt"></i>
                            Optimized
                        </span>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="main-content">
                <!-- Tab Navigation -->
                <nav class="tab-navigation">
                    <button class="tab-btn active" data-tab="cards">
                        <i class="fas fa-credit-card"></i>
                        Cards
                        <span class="tab-count" id="cards-count">0</span>
                    </button>
                    <button class="tab-btn" data-tab="emails">
                        <i class="fas fa-envelope"></i>
                        Emails
                        <span class="tab-count" id="emails-count">0</span>
                    </button>
                    <button class="tab-btn" data-tab="combinations">
                        <i class="fas fa-link"></i>
                        Combinations
                        <span class="tab-count" id="combinations-count">0</span>
                    </button>
                </nav>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Cards Tab -->
                    <div id="cards-tab" class="tab-panel active">
                        <div class="tab-panel-content">
                            <!-- Cards Input Section -->
                            <section class="input-section">
                                <div class="section-header">
                                    <h2>Add Cards</h2>
                                    <div class="section-actions">
                                        <button id="bulk-add-cards-btn" class="secondary-btn">
                                            <i class="fas fa-plus-square"></i>
                                            Bulk Add
                                        </button>
                                        <button id="clear-all-cards-btn" class="danger-btn">
                                            <i class="fas fa-trash"></i>
                                            Clear All
                                        </button>
                                    </div>
                                </div>
                                <div class="input-group">
                                    <input 
                                        type="text" 
                                        id="card-input" 
                                        class="form-input" 
                                        placeholder="Enter card: number,month,year,cvv,zip"
                                        autocomplete="off"
                                    >
                                    <button id="add-card-btn" class="primary-btn">
                                        <i class="fas fa-plus"></i>
                                        Add Card
                                    </button>
                                </div>
                                <div class="input-help">
                                    Format: 1234567890123456,12,2025,123,12345
                                </div>
                            </section>

                            <!-- Cards Lists -->
                            <div class="lists-container">
                                <!-- Available Cards -->
                                <section class="list-section">
                                    <div class="section-header">
                                        <h3>Available Cards</h3>
                                        <div class="section-actions">
                                            <button id="copy-available-cards-btn" class="secondary-btn">
                                                <i class="fas fa-copy"></i>
                                                Copy All
                                            </button>
                                            <button id="copy-available-cards-2x-btn" class="secondary-btn">
                                                <i class="fas fa-copy"></i>
                                                Copy 2x
                                            </button>
                                        </div>
                                    </div>
                                    <div id="available-cards-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-credit-card"></i>
                                            <p>No available cards</p>
                                            <small>Add cards using the input above</small>
                                        </div>
                                    </div>
                                </section>

                                <!-- Used Cards -->
                                <section class="list-section">
                                    <div class="section-header">
                                        <h3>Used Cards</h3>
                                        <div class="section-actions">
                                            <button id="copy-used-cards-btn" class="secondary-btn">
                                                <i class="fas fa-copy"></i>
                                                Copy All
                                            </button>
                                            <button id="remove-used-cards-btn" class="danger-btn">
                                                <i class="fas fa-trash"></i>
                                                Remove All
                                            </button>
                                        </div>
                                    </div>
                                    <div id="used-cards-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-check-circle"></i>
                                            <p>No used cards</p>
                                            <small>Cards will appear here after being combined</small>
                                        </div>
                                    </div>
                                </section>

                                <!-- Archived Cards -->
                                <section class="list-section">
                                    <div class="section-header">
                                        <h3>Archived Cards</h3>
                                        <div class="section-actions">
                                            <button id="restore-archived-cards-btn" class="secondary-btn">
                                                <i class="fas fa-undo"></i>
                                                Restore All
                                            </button>
                                        </div>
                                    </div>
                                    <div id="archived-cards-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-archive"></i>
                                            <p>No archived cards</p>
                                            <small>Archived cards will appear here</small>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>

                    <!-- Emails Tab -->
                    <div id="emails-tab" class="tab-panel">
                        <div class="tab-panel-content">
                            <!-- Email Type Selection -->
                            <section class="input-section">
                                <div class="section-header">
                                    <h2>Add Emails</h2>
                                    <div class="email-type-selector">
                                        <select id="email-type-select" class="form-select">
                                            <option value="pm_emails">PM emails 30 off 35</option>
                                            <option value="ue_emails_25_25">UE emails 25 off 25</option>
                                            <option value="ue_emails_25_15">UE emails 25 off 15</option>
                                            <option value="ue_emails_25_1">UE emails 25 off 1</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="input-group">
                                    <input 
                                        type="email" 
                                        id="email-input" 
                                        class="form-input" 
                                        placeholder="Enter email address"
                                        autocomplete="off"
                                    >
                                    <button id="add-email-btn" class="primary-btn">
                                        <i class="fas fa-plus"></i>
                                        Add Email
                                    </button>
                                </div>
                                <div class="section-actions">
                                    <button id="bulk-add-emails-btn" class="secondary-btn">
                                        <i class="fas fa-plus-square"></i>
                                        Bulk Add
                                    </button>
                                    <button id="clear-all-emails-btn" class="danger-btn">
                                        <i class="fas fa-trash"></i>
                                        Clear All
                                    </button>
                                </div>
                            </section>

                            <!-- Email Lists by Type -->
                            <div class="email-sections">
                                <!-- PM Emails Section -->
                                <section class="email-type-section">
                                    <div class="section-header">
                                        <h3>PM Emails (30 off 35)</h3>
                                        <div class="section-actions">
                                            <button class="copy-emails-btn secondary-btn" data-type="pm_emails">
                                                <i class="fas fa-copy"></i>
                                                Copy
                                            </button>
                                            <button class="copy-emails-2x-btn secondary-btn" data-type="pm_emails">
                                                <i class="fas fa-copy"></i>
                                                Copy 2x
                                            </button>
                                            <button class="clear-emails-btn danger-btn" data-type="pm_emails">
                                                <i class="fas fa-trash"></i>
                                                Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div id="pm-emails-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-envelope"></i>
                                            <p>No PM emails</p>
                                        </div>
                                    </div>
                                </section>

                                <!-- UE Emails 25/25 Section -->
                                <section class="email-type-section">
                                    <div class="section-header">
                                        <h3>UE Emails (25 off 25)</h3>
                                        <div class="section-actions">
                                            <button class="copy-emails-btn secondary-btn" data-type="ue_emails_25_25">
                                                <i class="fas fa-copy"></i>
                                                Copy
                                            </button>
                                            <button class="copy-emails-2x-btn secondary-btn" data-type="ue_emails_25_25">
                                                <i class="fas fa-copy"></i>
                                                Copy 2x
                                            </button>
                                            <button class="clear-emails-btn danger-btn" data-type="ue_emails_25_25">
                                                <i class="fas fa-trash"></i>
                                                Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div id="ue-emails-25-25-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-envelope"></i>
                                            <p>No UE 25/25 emails</p>
                                        </div>
                                    </div>
                                </section>

                                <!-- UE Emails 25/15 Section -->
                                <section class="email-type-section">
                                    <div class="section-header">
                                        <h3>UE Emails (25 off 15)</h3>
                                        <div class="section-actions">
                                            <button class="copy-emails-btn secondary-btn" data-type="ue_emails_25_15">
                                                <i class="fas fa-copy"></i>
                                                Copy
                                            </button>
                                            <button class="copy-emails-2x-btn secondary-btn" data-type="ue_emails_25_15">
                                                <i class="fas fa-copy"></i>
                                                Copy 2x
                                            </button>
                                            <button class="clear-emails-btn danger-btn" data-type="ue_emails_25_15">
                                                <i class="fas fa-trash"></i>
                                                Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div id="ue-emails-25-15-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-envelope"></i>
                                            <p>No UE 25/15 emails</p>
                                        </div>
                                    </div>
                                </section>

                                <!-- UE Emails 25/1 Section -->
                                <section class="email-type-section">
                                    <div class="section-header">
                                        <h3>UE Emails (25 off 1)</h3>
                                        <div class="section-actions">
                                            <button class="copy-emails-btn secondary-btn" data-type="ue_emails_25_1">
                                                <i class="fas fa-copy"></i>
                                                Copy
                                            </button>
                                            <button class="copy-emails-2x-btn secondary-btn" data-type="ue_emails_25_1">
                                                <i class="fas fa-copy"></i>
                                                Copy 2x
                                            </button>
                                            <button class="clear-emails-btn danger-btn" data-type="ue_emails_25_1">
                                                <i class="fas fa-trash"></i>
                                                Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div id="ue-emails-25-1-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-envelope"></i>
                                            <p>No UE 25/1 emails</p>
                                        </div>
                                    </div>
                                </section>

                                <!-- Used Emails Section -->
                                <section class="email-type-section">
                                    <div class="section-header">
                                        <h3>Used Emails</h3>
                                        <div class="section-actions">
                                            <button id="remove-used-emails-btn" class="danger-btn">
                                                <i class="fas fa-trash"></i>
                                                Remove All
                                            </button>
                                        </div>
                                    </div>
                                    <div id="used-emails-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-check-circle"></i>
                                            <p>No used emails</p>
                                            <small>Emails will appear here after being combined</small>
                                        </div>
                                    </div>
                                </section>

                                <!-- Archived Emails Section -->
                                <section class="email-type-section">
                                    <div class="section-header">
                                        <h3>Archived Emails</h3>
                                        <div class="section-actions">
                                            <button id="restore-archived-emails-btn" class="secondary-btn">
                                                <i class="fas fa-undo"></i>
                                                Restore All
                                            </button>
                                        </div>
                                    </div>
                                    <div id="archived-emails-list" class="item-list">
                                        <div class="empty-state">
                                            <i class="fas fa-archive"></i>
                                            <p>No archived emails</p>
                                            <small>Archived emails will appear here</small>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>

                    <!-- Combinations Tab -->
                    <div id="combinations-tab" class="tab-panel">
                        <div class="tab-panel-content">
                            <!-- Combination Creation Section -->
                            <section class="combination-section">
                                <div class="section-header">
                                    <h2>Create Combination</h2>
                                    <div class="auto-select-container">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="auto-select-checkbox" checked>
                                            <span class="checkmark"></span>
                                            Auto-select oldest items
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="combination-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="card-select">Select Card:</label>
                                            <select id="card-select" class="form-select">
                                                <option value="">Choose a card...</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="email-select">Select Email:</label>
                                            <select id="email-select" class="form-select">
                                                <option value="">Choose an email...</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="combination-email-type">Email Type:</label>
                                            <select id="combination-email-type" class="form-select">
                                                <option value="pm_emails">PM 30/35</option>
                                                <option value="ue_emails_25_25">UE 25/25</option>
                                                <option value="ue_emails_25_15">UE 25/15</option>
                                                <option value="ue_emails_25_1">UE 25/1</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button id="combine-btn" class="primary-btn large">
                                            <i class="fas fa-link"></i>
                                            Create Combination
                                        </button>
                                    </div>
                                </div>

                                <!-- Latest Combination Display -->
                                <div id="latest-combination" class="latest-combination hidden">
                                    <div class="section-header">
                                        <h3>Latest Combination</h3>
                                    </div>
                                    <div id="latest-combination-content" class="combination-item latest">
                                        <!-- Latest combination will be displayed here -->
                                    </div>
                                </div>
                            </section>

                            <!-- All Combinations Section -->
                            <section class="combinations-list-section">
                                <div class="section-header">
                                    <h2>All Combinations</h2>
                                    <div class="section-actions">
                                        <button id="clear-all-combinations-btn" class="danger-btn">
                                            <i class="fas fa-trash"></i>
                                            Clear All
                                        </button>
                                    </div>
                                </div>
                                <div id="combinations-list" class="combinations-list">
                                    <div class="empty-state">
                                        <i class="fas fa-link"></i>
                                        <p>No combinations created</p>
                                        <small>Create combinations using the form above</small>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </main>

            <!-- Status Bar -->
            <footer class="status-bar">
                <div class="status-left">
                    <span id="status-message">Ready</span>
                </div>
                <div class="status-right">
                    <span id="save-status" class="save-status">
                        <i class="fas fa-save"></i>
                        Auto-saved
                    </span>
                    <span id="item-counts" class="item-counts">
                        Cards: <span id="total-cards">0</span> | 
                        Emails: <span id="total-emails">0</span> | 
                        Combinations: <span id="total-combinations">0</span>
                    </span>
                </div>
            </footer>
        </div>

        <!-- Modals and Overlays -->
        <div id="modal-overlay" class="modal-overlay hidden">
            <!-- Bulk Add Modal -->
            <div id="bulk-add-modal" class="modal">
                <div class="modal-header">
                    <h3 id="bulk-modal-title">Bulk Add Items</h3>
                    <button class="modal-close" id="bulk-modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="bulk-input">Enter items (one per line):</label>
                        <textarea 
                            id="bulk-input" 
                            class="form-textarea" 
                            rows="10" 
                            placeholder="Paste your items here, one per line..."
                        ></textarea>
                    </div>
                    <div class="bulk-help">
                        <p id="bulk-help-text">Format help will appear here</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="bulk-cancel-btn" class="secondary-btn">Cancel</button>
                    <button id="bulk-add-confirm-btn" class="primary-btn">
                        <i class="fas fa-plus"></i>
                        Add Items
                    </button>
                </div>
            </div>

            <!-- Settings Modal -->
            <div id="settings-modal" class="modal">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button class="modal-close" id="settings-modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="settings-section">
                        <h4>Appearance</h4>
                        <div class="form-group">
                            <label for="theme-select">Theme:</label>
                            <select id="theme-select" class="form-select">
                                <option value="dark">Dark</option>
                                <option value="light">Light</option>
                                <option value="auto">Auto</option>
                            </select>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h4>Auto-save</h4>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="auto-save-enabled" checked>
                                <span class="checkmark"></span>
                                Enable auto-save
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="auto-save-interval">Auto-save interval (seconds):</label>
                            <input type="number" id="auto-save-interval" class="form-input" value="60" min="10" max="300">
                        </div>
                    </div>
                    <div class="settings-section">
                        <h4>Search</h4>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="search-case-sensitive">
                                <span class="checkmark"></span>
                                Case-sensitive search
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="settings-cancel-btn" class="secondary-btn">Cancel</button>
                    <button id="settings-save-btn" class="primary-btn">
                        <i class="fas fa-save"></i>
                        Save Settings
                    </button>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Scripts -->
    <script type="module" src="js/main.js"></script>
</body>
</html>
