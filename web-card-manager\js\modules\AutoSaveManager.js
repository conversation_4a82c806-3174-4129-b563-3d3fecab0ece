/**
 * Auto Save Manager - Handles automatic data saving and status updates
 */

export class AutoSaveManager {
    constructor(dataManager, configManager) {
        this.dataManager = dataManager;
        this.configManager = configManager;
        this.toastManager = null;
        
        this.saveTimer = null;
        this.lastSaveTime = null;
        this.saveInProgress = false;
        this.pendingChanges = false;
        this.statusElement = null;
        
        // Default settings
        this.defaultInterval = 30000; // 30 seconds
        this.minInterval = 5000; // 5 seconds
        this.maxInterval = 300000; // 5 minutes
    }

    /**
     * Initialize auto-save manager
     */
    async init() {
        console.log('💾 Initializing Auto Save Manager...');
        
        // Create status element
        this.createStatusElement();
        
        // Set up data change listeners
        this.setupDataListeners();
        
        // Start auto-save timer
        this.startAutoSave();
        
        // Handle page visibility changes
        this.setupVisibilityListener();
        
        // Handle beforeunload
        this.setupUnloadListener();
        
        console.log('✅ Auto Save Manager initialized');
    }

    /**
     * Set toast manager reference
     */
    setToastManager(toastManager) {
        this.toastManager = toastManager;
    }

    /**
     * Create status element
     */
    createStatusElement() {
        // Look for existing status element
        this.statusElement = document.getElementById('save-status');
        
        if (!this.statusElement) {
            // Create status element
            this.statusElement = document.createElement('div');
            this.statusElement.id = 'save-status';
            this.statusElement.className = 'save-status';
            
            // Add to toolbar or create container
            const toolbar = document.querySelector('.toolbar');
            if (toolbar) {
                toolbar.appendChild(this.statusElement);
            } else {
                document.body.appendChild(this.statusElement);
            }
        }
        
        this.updateStatus('ready');
    }

    /**
     * Set up data change listeners
     */
    setupDataListeners() {
        // Listen to all data change events
        const events = [
            'cardAdded', 'cardsAdded', 'cardRemoved', 'cardArchived', 'cardRestored',
            'emailAdded', 'emailsAdded', 'emailRemoved',
            'combinationCreated', 'combinationDeleted',
            'dataImported', 'dataCleared'
        ];
        
        events.forEach(event => {
            this.dataManager.on(event, () => {
                this.markPendingChanges();
            });
        });
    }

    /**
     * Mark that there are pending changes
     */
    markPendingChanges() {
        if (!this.pendingChanges) {
            this.pendingChanges = true;
            this.updateStatus('pending');
        }
    }

    /**
     * Start auto-save timer
     */
    startAutoSave() {
        this.stopAutoSave();
        
        const interval = this.getAutoSaveInterval();
        
        this.saveTimer = setInterval(() => {
            if (this.pendingChanges && !this.saveInProgress) {
                this.performAutoSave();
            }
        }, interval);
    }

    /**
     * Stop auto-save timer
     */
    stopAutoSave() {
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
            this.saveTimer = null;
        }
    }

    /**
     * Get auto-save interval from config
     */
    getAutoSaveInterval() {
        if (this.configManager) {
            const configInterval = this.configManager.get('autoSaveInterval') * 1000;
            return Math.max(this.minInterval, Math.min(this.maxInterval, configInterval));
        }
        return this.defaultInterval;
    }

    /**
     * Perform auto-save
     */
    async performAutoSave() {
        if (this.saveInProgress) return;
        
        this.saveInProgress = true;
        this.updateStatus('saving');
        
        try {
            // Save data
            await this.dataManager.saveData();
            
            this.lastSaveTime = Date.now();
            this.pendingChanges = false;
            this.updateStatus('saved');
            
            // Show success status briefly
            setTimeout(() => {
                if (!this.pendingChanges) {
                    this.updateStatus('ready');
                }
            }, 2000);
            
        } catch (error) {
            console.error('Auto-save failed:', error);
            this.updateStatus('error');
            
            // Show error toast
            if (this.toastManager) {
                this.toastManager.error('Auto-save Failed', error.message);
            }
            
            // Retry after a delay
            setTimeout(() => {
                if (this.pendingChanges) {
                    this.performAutoSave();
                }
            }, 5000);
            
        } finally {
            this.saveInProgress = false;
        }
    }

    /**
     * Force save now
     */
    async forceSave() {
        if (this.saveInProgress) return false;
        
        this.pendingChanges = true;
        await this.performAutoSave();
        return true;
    }

    /**
     * Update status display
     */
    updateStatus(status) {
        if (!this.statusElement) return;
        
        // Remove all status classes
        this.statusElement.className = 'save-status';
        
        let text = '';
        let icon = '';
        
        switch (status) {
            case 'ready':
                text = 'Ready';
                icon = '✓';
                this.statusElement.classList.add('status-ready');
                break;
                
            case 'pending':
                text = 'Unsaved changes';
                icon = '●';
                this.statusElement.classList.add('status-pending');
                break;
                
            case 'saving':
                text = 'Saving...';
                icon = '⟳';
                this.statusElement.classList.add('status-saving');
                break;
                
            case 'saved':
                text = 'Saved';
                icon = '✓';
                this.statusElement.classList.add('status-saved');
                break;
                
            case 'error':
                text = 'Save failed';
                icon = '⚠';
                this.statusElement.classList.add('status-error');
                break;
        }
        
        this.statusElement.innerHTML = `<span class="status-icon">${icon}</span><span class="status-text">${text}</span>`;
        
        // Add timestamp for saved status
        if (status === 'saved' && this.lastSaveTime) {
            const timeStr = new Date(this.lastSaveTime).toLocaleTimeString();
            this.statusElement.title = `Last saved: ${timeStr}`;
        }
    }

    /**
     * Set up page visibility listener
     */
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is hidden, save immediately if there are changes
                if (this.pendingChanges && !this.saveInProgress) {
                    this.performAutoSave();
                }
            } else {
                // Page is visible again, restart auto-save timer
                this.startAutoSave();
            }
        });
    }

    /**
     * Set up beforeunload listener
     */
    setupUnloadListener() {
        window.addEventListener('beforeunload', (e) => {
            if (this.pendingChanges) {
                // Try to save synchronously
                try {
                    this.dataManager.saveDataSync();
                } catch (error) {
                    console.error('Failed to save on unload:', error);
                    
                    // Show warning to user
                    e.preventDefault();
                    e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                    return e.returnValue;
                }
            }
        });
    }

    /**
     * Get save status
     */
    getStatus() {
        return {
            pendingChanges: this.pendingChanges,
            saveInProgress: this.saveInProgress,
            lastSaveTime: this.lastSaveTime,
            autoSaveEnabled: this.saveTimer !== null
        };
    }

    /**
     * Get last save time formatted
     */
    getLastSaveTimeFormatted() {
        if (!this.lastSaveTime) return 'Never';
        
        const now = Date.now();
        const diff = now - this.lastSaveTime;
        
        if (diff < 60000) { // Less than 1 minute
            return 'Just now';
        } else if (diff < 3600000) { // Less than 1 hour
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
        } else {
            return new Date(this.lastSaveTime).toLocaleString();
        }
    }

    /**
     * Enable auto-save
     */
    enable() {
        if (!this.saveTimer) {
            this.startAutoSave();
            this.updateStatus('ready');
        }
    }

    /**
     * Disable auto-save
     */
    disable() {
        this.stopAutoSave();
        this.updateStatus('ready');
    }

    /**
     * Check if auto-save is enabled
     */
    isEnabled() {
        return this.saveTimer !== null;
    }

    /**
     * Update auto-save interval
     */
    updateInterval(seconds) {
        const interval = Math.max(5, Math.min(300, seconds)) * 1000;
        
        if (this.configManager) {
            this.configManager.set('autoSaveInterval', interval / 1000);
        }
        
        // Restart timer with new interval
        if (this.saveTimer) {
            this.startAutoSave();
        }
    }

    /**
     * Get statistics
     */
    getStats() {
        return {
            lastSaveTime: this.lastSaveTime,
            lastSaveTimeFormatted: this.getLastSaveTimeFormatted(),
            pendingChanges: this.pendingChanges,
            saveInProgress: this.saveInProgress,
            autoSaveEnabled: this.isEnabled(),
            autoSaveInterval: this.getAutoSaveInterval() / 1000
        };
    }

    /**
     * Reset status
     */
    reset() {
        this.pendingChanges = false;
        this.saveInProgress = false;
        this.lastSaveTime = null;
        this.updateStatus('ready');
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this.stopAutoSave();
        
        if (this.statusElement && this.statusElement.parentNode) {
            this.statusElement.parentNode.removeChild(this.statusElement);
        }
        
        this.statusElement = null;
        this.dataManager = null;
        this.configManager = null;
        this.toastManager = null;
    }
}
