"""
Card Manager Application

A GUI application that allows users to:
- Input and store cards and emails
- Combine them according to specified requirements
- Delete combinations
- Ensure emails can only be bound to a card once
- Uses customtkinter for a modern UI
"""
import customtkinter as ctk
from tkinter import messagebox, filedialog
import os
import time
import threading
import pyperclip  # For copying to clipboard
from typing import Optional, List, Dict, Any

from data_manager import DataManager
from utils import validate_email, validate_card, parse_bulk_cards, parse_bulk_emails, export_to_csv, import_from_csv
from ui_manager import UIManager
from error_handler import <PERSON>rror<PERSON>andler
from config_manager import ConfigManager
from search_manager import SearchManager, SearchFilter, SearchType
from history_manager import HistoryManager, ActionType
import db

# Set appearance mode and default color theme
ctk.set_appearance_mode("System")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class CardManagerApp:
    def __init__(self, root):
        self.root = root

        # Initialize managers first
        self.config_manager = ConfigManager()
        self.error_handler = ErrorHandler()

        # Apply window geometry from config
        geometry = self.config_manager.get_window_geometry()
        self.root.title("Card Manager")
        self.root.geometry(f"{geometry['width']}x{geometry['height']}")
        if geometry['x'] is not None and geometry['y'] is not None:
            self.root.geometry(f"{geometry['width']}x{geometry['height']}+{geometry['x']}+{geometry['y']}")
        self.root.minsize(800, 600)

        # Apply theme from config
        ctk.set_appearance_mode(self.config_manager.get_theme())

        # Initialize data manager
        self.data_manager = DataManager()

        # Initialize other managers
        self.ui_manager = UIManager(self)
        self.search_manager = SearchManager(self.data_manager)
        self.history_manager = HistoryManager(self.data_manager)

        # Initialize latest combination
        self.latest_combination = None

        # Track last save time
        self.last_save_time = time.time()

        # Auto-save timer
        self.auto_save_timer = None

        # Load saved data with enhanced error handling
        success, data, error_msg = self.error_handler.safe_load_data()
        if success and data:
            # Update data manager with loaded data
            self.data_manager.cards = data.get('cards', [])
            self.data_manager.emails = data.get('emails', [])
            self.data_manager.pm_emails = data.get('pm_emails', [])
            self.data_manager.ue_emails_25_25 = data.get('ue_emails_25_25', [])
            self.data_manager.ue_emails_25_15 = data.get('ue_emails_25_15', [])
            self.data_manager.ue_emails_25_1 = data.get('ue_emails_25_1', [])
            self.data_manager.combinations = data.get('combinations', [])
            self.data_manager.used_cards = set(data.get('used_cards', []))
            self.data_manager.used_emails = set(data.get('used_emails', []))
            self.data_manager.all_used_cards = set(data.get('all_used_cards', []))
            self.data_manager.all_used_emails = set(data.get('all_used_emails', []))
            self.data_manager.archived_cards = set(data.get('archived_cards', []))
            self.data_manager.archived_emails = set(data.get('archived_emails', []))

            if error_msg:
                messagebox.showwarning("Data Recovery", error_msg)
        else:
            messagebox.showerror("Load Error", "Failed to load data. Starting with empty data.")

        # Make sure all_used sets are initialized and include all used items
        self.data_manager.all_used_cards.update(self.data_manager.used_cards)
        self.data_manager.all_used_emails.update(self.data_manager.used_emails)

        # Debug information about initialization (only if debug mode is enabled)
        if self.config_manager.is_debug_mode():
            print("\nInitializing application...")
            print(f"Initial used_cards: {self.data_manager.used_cards}")
            print(f"Initial used_emails: {self.data_manager.used_emails}")
            print(f"Initial all_used_cards: {self.data_manager.all_used_cards}")
            print(f"Initial all_used_emails: {self.data_manager.all_used_emails}")

        # Verify that all_used sets include all used items
        for card in self.data_manager.used_cards:
            if card not in self.data_manager.all_used_cards:
                if self.config_manager.is_debug_mode():
                    print(f"WARNING: Used card {card} not in all_used_cards. Adding it.")
                self.data_manager.all_used_cards.add(card)

        for email in self.data_manager.used_emails:
            if email not in self.data_manager.all_used_emails:
                if self.config_manager.is_debug_mode():
                    print(f"WARNING: Used email {email} not in all_used_emails. Adding it.")
                self.data_manager.all_used_emails.add(email)

        # Set the latest combination if there are any combinations
        combinations = self.data_manager.get_combinations()
        if combinations:
            self.latest_combination = combinations[-1]  # The last combination is the latest

        # Create the main UI
        self.create_ui()

        # Update the lists using optimized UI manager
        self.ui_manager.force_full_refresh()

        # Update the latest combination display
        if hasattr(self, 'update_latest_combination'):
            self.update_latest_combination()

        # Start periodic auto-save timer (re-enabled with config support)
        if self.config_manager.is_auto_save_enabled():
            self.setup_auto_save_timer()

    def create_ui(self):
        """Create the user interface."""
        # Create a tabview (customtkinter's tabbed interface)
        self.tabview = ctk.CTkTabview(self.root)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)

        # Create tabs
        self.cards_tab = self.tabview.add("Cards")
        self.emails_tab = self.tabview.add("Emails")
        self.combine_tab = self.tabview.add("Combinations")

        # Set up each tab
        self.setup_cards_tab()
        self.setup_emails_tab()
        self.setup_combine_tab()

        # Add a status bar with save indicator
        status_frame = ctk.CTkFrame(self.root, height=25, corner_radius=0, fg_color=("#EEEEEE", "#333333"))
        status_frame.pack(side="bottom", fill="x")

        # Main status message (left side)
        self.status_var = ctk.StringVar()
        self.status_bar = ctk.CTkLabel(status_frame, textvariable=self.status_var, height=25,
                                     corner_radius=0, fg_color=("#EEEEEE", "#333333"))
        self.status_bar.pack(side="left", fill="x", expand=True, padx=5)
        self.status_var.set("Ready")

        # Save indicator (right side)
        self.save_status_var = ctk.StringVar()
        self.save_status_bar = ctk.CTkLabel(status_frame, textvariable=self.save_status_var, height=25,
                                          corner_radius=0, fg_color=("#EEEEEE", "#333333"),
                                          text_color=("#008800", "#00FF00"))
        self.save_status_bar.pack(side="right", padx=10)
        self.update_save_status()

        # Add save button
        self.save_button = ctk.CTkButton(self.root, text="Save Data", command=self.save_data)
        self.save_button.pack(side="bottom", pady=10)

    def setup_cards_tab(self):
        """Set up the Cards tab."""
        # Left frame for input
        left_frame = ctk.CTkFrame(self.cards_tab)
        left_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)

        # Add Card label
        ctk.CTkLabel(left_frame, text="Add Card", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 20))

        # Card input
        input_frame = ctk.CTkFrame(left_frame)
        input_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(input_frame, text="Card Number:").pack(anchor="w", padx=5, pady=5)
        self.card_entry = ctk.CTkEntry(input_frame, width=250)
        self.card_entry.pack(fill="x", padx=5, pady=5)

        # Buttons frame
        buttons_frame = ctk.CTkFrame(left_frame)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        # Add button
        add_button = ctk.CTkButton(buttons_frame, text="Add Card", command=self.add_card)
        add_button.pack(side="left", padx=5, pady=10, expand=True, fill="x")

        # Bulk Add button
        bulk_add_button = ctk.CTkButton(buttons_frame, text="Bulk Add", command=self.bulk_add_cards)
        bulk_add_button.pack(side="right", padx=5, pady=10, expand=True, fill="x")

        # Right frame for lists
        right_frame = ctk.CTkFrame(self.cards_tab)
        right_frame.pack(side="right", fill="both", expand=True, padx=10, pady=10)

        # Create a vertical layout for the three sections
        right_frame.grid_rowconfigure(0, weight=1)  # Available Cards
        right_frame.grid_rowconfigure(1, weight=1)  # Used Cards
        right_frame.grid_rowconfigure(2, weight=1)  # Archived Cards
        right_frame.grid_columnconfigure(0, weight=1)

        # Available Cards section
        available_frame = ctk.CTkFrame(right_frame)
        available_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # Available Cards header frame
        header_frame = ctk.CTkFrame(available_frame)
        header_frame.pack(fill="x", padx=10, pady=5)

        # Available Cards label with count
        self.available_cards_label = ctk.CTkLabel(header_frame, text="Available Cards (0)", font=ctk.CTkFont(size=16, weight="bold"))
        self.available_cards_label.pack(side="left", pady=5)

        # Buttons frame for available cards actions
        available_buttons_frame = ctk.CTkFrame(header_frame)
        available_buttons_frame.pack(side="right", fill="y")

        # Copy All Available Cards button
        copy_all_btn = ctk.CTkButton(available_buttons_frame, text="Copy All", command=self.copy_all_available_cards, width=80)
        copy_all_btn.pack(side="left", padx=5, pady=5)

        # Copy 2x Available Cards button
        copy_2x_btn = ctk.CTkButton(available_buttons_frame, text="Copy 2x", command=self.copy_all_available_cards_2x, width=80)
        copy_2x_btn.pack(side="left", padx=5, pady=5)

        # Delete All Available Cards button
        delete_all_btn = ctk.CTkButton(available_buttons_frame, text="Delete All", command=self.delete_all_available_cards, width=80,
                                     fg_color="#D32F2F", hover_color="#B71C1C")  # Red color for delete button
        delete_all_btn.pack(side="left", padx=5, pady=5)

        # Available Cards list with scrollable frame
        self.card_list_frame = ctk.CTkScrollableFrame(available_frame)
        self.card_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Used Cards section
        used_frame = ctk.CTkFrame(right_frame)
        used_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)

        # Used Cards header frame
        used_header_frame = ctk.CTkFrame(used_frame)
        used_header_frame.pack(fill="x", padx=10, pady=5)

        # Used Cards label with count
        self.used_cards_label = ctk.CTkLabel(used_header_frame, text="Used Cards (0)", font=ctk.CTkFont(size=16, weight="bold"))
        self.used_cards_label.pack(side="left", pady=5)

        # Buttons frame for used cards actions
        used_buttons_frame = ctk.CTkFrame(used_header_frame)
        used_buttons_frame.pack(side="right", fill="y")

        # Copy All Used Cards button
        copy_all_btn = ctk.CTkButton(used_buttons_frame, text="Copy All", command=self.copy_all_used_cards, width=80)
        copy_all_btn.pack(side="left", padx=5, pady=5)

        # Remove Used button
        remove_used_btn = ctk.CTkButton(used_buttons_frame, text="Remove Used", command=self.remove_used_cards, width=100)
        remove_used_btn.pack(side="left", padx=5, pady=5)

        # Used Cards list with scrollable frame
        self.used_card_list_frame = ctk.CTkScrollableFrame(used_frame)
        self.used_card_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Archived Cards section - with a distinctive background color
        archived_frame = ctk.CTkFrame(right_frame, fg_color=("#e0e0ff", "#2a2a4d"))  # Light blue/dark blue background
        archived_frame.grid(row=2, column=0, sticky="nsew", padx=5, pady=5)

        # Archived Cards header frame
        archived_header_frame = ctk.CTkFrame(archived_frame, fg_color=archived_frame.cget("fg_color"))
        archived_header_frame.pack(fill="x", padx=10, pady=5)

        # Archived Cards label with count
        self.archived_cards_label = ctk.CTkLabel(archived_header_frame, text="Archived Cards (0)",
                                              font=ctk.CTkFont(size=16, weight="bold"))
        self.archived_cards_label.pack(side="left", pady=5)

        # Archived Cards list with scrollable frame
        self.archived_card_list_frame = ctk.CTkScrollableFrame(archived_frame)
        self.archived_card_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

    def setup_emails_tab(self):
        """Set up the Emails tab."""
        # Left frame for input
        left_frame = ctk.CTkFrame(self.emails_tab)
        left_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)

        # Add Email label
        ctk.CTkLabel(left_frame, text="Add Email", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 20))

        # Email input
        input_frame = ctk.CTkFrame(left_frame)
        input_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(input_frame, text="Email Address:").pack(anchor="w", padx=5, pady=5)
        self.email_entry = ctk.CTkEntry(input_frame, width=250)
        self.email_entry.pack(fill="x", padx=5, pady=5)

        # Buttons frame
        buttons_frame = ctk.CTkFrame(left_frame)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        # Add button
        add_button = ctk.CTkButton(buttons_frame, text="Add Email", command=self.add_email)
        add_button.pack(side="left", padx=5, pady=10, expand=True, fill="x")

        # Bulk Add button
        bulk_add_button = ctk.CTkButton(buttons_frame, text="Bulk Add", command=self.bulk_add_emails)
        bulk_add_button.pack(side="right", padx=5, pady=10, expand=True, fill="x")

        # Right frame for lists
        right_frame = ctk.CTkFrame(self.emails_tab)
        right_frame.pack(side="right", fill="both", expand=True, padx=10, pady=10)

        # Create a vertical layout for the three sections
        right_frame.grid_rowconfigure(0, weight=1)  # Available Emails
        right_frame.grid_rowconfigure(1, weight=1)  # Used Emails
        right_frame.grid_rowconfigure(2, weight=1)  # Archived Emails
        right_frame.grid_columnconfigure(0, weight=1)

        # Email section with tabview for different email types
        email_frame = ctk.CTkFrame(right_frame)
        email_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # Create tabview for different email sections
        self.email_tabview = ctk.CTkTabview(email_frame)
        self.email_tabview.pack(fill="both", expand=True, padx=5, pady=5)

        # Create tabs for different email types
        self.pm_emails_tab = self.email_tabview.add("PM 30/35")
        self.ue_emails_25_25_tab = self.email_tabview.add("UE 25/25")
        self.ue_emails_25_15_tab = self.email_tabview.add("UE 25/15")
        self.ue_emails_25_1_tab = self.email_tabview.add("UE 25/1")

        # Create email sections for each tab
        self.create_email_section(self.pm_emails_tab, "PM emails 30 off 35", "pm_emails")
        self.create_email_section(self.ue_emails_25_25_tab, "UE emails 25 off 25", "ue_emails_25_25")
        self.create_email_section(self.ue_emails_25_15_tab, "UE emails 25 off 15", "ue_emails_25_15")
        self.create_email_section(self.ue_emails_25_1_tab, "UE emails 25 off 1", "ue_emails_25_1")

        # Used Emails section
        used_frame = ctk.CTkFrame(right_frame)
        used_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)

        # Used Emails header frame
        used_header_frame = ctk.CTkFrame(used_frame)
        used_header_frame.pack(fill="x", padx=10, pady=5)

        # Used Emails label with count
        self.used_emails_label = ctk.CTkLabel(used_header_frame, text="Used Emails (0)", font=ctk.CTkFont(size=16, weight="bold"))
        self.used_emails_label.pack(side="left", pady=5)

        # Remove Used button
        remove_used_btn = ctk.CTkButton(used_header_frame, text="Remove Used", command=self.remove_used_emails, width=120)
        remove_used_btn.pack(side="right", padx=5, pady=5)

        # Used Emails list with scrollable frame
        self.used_email_list_frame = ctk.CTkScrollableFrame(used_frame)
        self.used_email_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Archived Emails section - with a distinctive background color
        archived_frame = ctk.CTkFrame(right_frame, fg_color=("#e0e0ff", "#2a2a4d"))  # Light blue/dark blue background
        archived_frame.grid(row=2, column=0, sticky="nsew", padx=5, pady=5)

        # Archived Emails header frame
        archived_header_frame = ctk.CTkFrame(archived_frame, fg_color=archived_frame.cget("fg_color"))
        archived_header_frame.pack(fill="x", padx=10, pady=5)

        # Archived Emails label with count
        self.archived_emails_label = ctk.CTkLabel(archived_header_frame, text="Archived Emails (0)",
                                               font=ctk.CTkFont(size=16, weight="bold"))
        self.archived_emails_label.pack(side="left", pady=5)

        # Archived Emails list with scrollable frame
        self.archived_email_list_frame = ctk.CTkScrollableFrame(archived_frame)
        self.archived_email_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

    def create_email_section(self, parent_tab, section_title, email_type):
        """Create an email section for a specific email type."""
        # Available Emails header frame
        header_frame = ctk.CTkFrame(parent_tab)
        header_frame.pack(fill="x", padx=10, pady=5)

        # Available Emails label with count
        label_attr = f"available_{email_type}_label"
        setattr(self, label_attr, ctk.CTkLabel(header_frame, text=f"{section_title} (0)",
                                              font=ctk.CTkFont(size=16, weight="bold")))
        getattr(self, label_attr).pack(side="left", pady=5)

        # Buttons frame for available emails actions
        available_buttons_frame = ctk.CTkFrame(header_frame)
        available_buttons_frame.pack(side="right", fill="y")

        # Copy All Available Emails button
        copy_all_btn = ctk.CTkButton(available_buttons_frame, text="Copy All",
                                   command=lambda: self.copy_all_available_emails_by_type(email_type), width=80)
        copy_all_btn.pack(side="left", padx=5, pady=5)

        # Copy 2x Available Emails button
        copy_2x_btn = ctk.CTkButton(available_buttons_frame, text="Copy 2x",
                                  command=lambda: self.copy_all_available_emails_2x_by_type(email_type), width=80)
        copy_2x_btn.pack(side="left", padx=5, pady=5)

        # Delete All Available Emails button
        delete_all_btn = ctk.CTkButton(available_buttons_frame, text="Delete All",
                                     command=lambda: self.delete_all_available_emails_by_type(email_type), width=80,
                                     fg_color="#D32F2F", hover_color="#B71C1C")  # Red color for delete button
        delete_all_btn.pack(side="left", padx=5, pady=5)

        # Available Emails list with scrollable frame
        list_frame_attr = f"{email_type}_list_frame"
        setattr(self, list_frame_attr, ctk.CTkScrollableFrame(parent_tab))
        getattr(self, list_frame_attr).pack(fill="both", expand=True, padx=10, pady=10)

    def setup_combine_tab(self):
        """Set up the Combinations tab."""
        # Initialize latest combination
        self.latest_combination = None

        # Main container with single column layout
        main_container = ctk.CTkFrame(self.combine_tab)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # Configure grid - single column takes 100% of width
        main_container.grid_columnconfigure(0, weight=1)

        # Single column for all content
        content_column = ctk.CTkFrame(main_container)
        content_column.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # === COMBINATIONS SECTION ===

        # Create a very compact combined section at the top
        combined_frame = ctk.CTkFrame(content_column, height=60)
        combined_frame.pack(fill="x", padx=5, pady=(5, 2))
        combined_frame.pack_propagate(False)  # Prevent the frame from shrinking

        # Create a horizontal layout for the combined section
        combined_layout = ctk.CTkFrame(combined_frame)
        combined_layout.pack(fill="both", expand=True, padx=2, pady=2)

        # Configure grid for the horizontal layout
        combined_layout.grid_columnconfigure(0, weight=1)  # Latest combination
        combined_layout.grid_columnconfigure(1, weight=1)  # Create combination

        # Latest combination mini-section (left side)
        latest_mini_frame = ctk.CTkFrame(combined_layout, fg_color=("#e0f0ff", "#2a3b4d"))
        latest_mini_frame.grid(row=0, column=0, padx=2, pady=2, sticky="nsew")

        ctk.CTkLabel(latest_mini_frame, text="Latest",
                    font=ctk.CTkFont(size=10, weight="bold")).pack(pady=(1, 0))

        # Frame for the latest combination content
        self.latest_combination_frame = ctk.CTkFrame(latest_mini_frame, fg_color=latest_mini_frame.cget("fg_color"))
        self.latest_combination_frame.pack(fill="x", padx=2, pady=1)

        # Label for the latest combination
        self.latest_combination_label = ctk.CTkLabel(self.latest_combination_frame,
                                                   text="No combinations yet",
                                                   font=ctk.CTkFont(size=10))
        self.latest_combination_label.pack(pady=1)

        # Copy button for latest combination (Fusion format)
        self.latest_copy_btn = ctk.CTkButton(latest_mini_frame, text="Copy (Fusion)",
                                           command=self.copy_latest_combination,
                                           width=80, height=20,
                                           font=ctk.CTkFont(size=10))
        self.latest_copy_btn.pack(pady=(0, 1), padx=2)

        # Create combination mini-section (right side)
        create_mini_frame = ctk.CTkFrame(combined_layout)
        create_mini_frame.grid(row=0, column=1, padx=2, pady=2, sticky="nsew")

        # Card and email selection - no labels, in a horizontal layout
        selection_frame = ctk.CTkFrame(create_mini_frame)
        selection_frame.pack(fill="x", padx=2, pady=1)
        selection_frame.grid_columnconfigure(0, weight=1)
        selection_frame.grid_columnconfigure(1, weight=1)
        selection_frame.grid_columnconfigure(2, weight=1)

        # Card combo on the left
        self.card_combo = ctk.CTkComboBox(selection_frame, width=80, height=20,
                                        state="readonly", font=ctk.CTkFont(size=10))
        self.card_combo.grid(row=0, column=0, padx=1, pady=1, sticky="ew")

        # Email type selector in the middle
        self.email_type_combo = ctk.CTkComboBox(selection_frame, width=80, height=20,
                                              values=["PM 30/35", "UE 25/25", "UE 25/15", "UE 25/1"],
                                              state="readonly", font=ctk.CTkFont(size=10),
                                              command=self.on_email_type_changed)
        self.email_type_combo.grid(row=0, column=1, padx=1, pady=1, sticky="ew")
        self.email_type_combo.set("PM 30/35")  # Default selection

        # Email combo on the right
        self.email_combo = ctk.CTkComboBox(selection_frame, width=80, height=20,
                                         state="readonly", font=ctk.CTkFont(size=10))
        self.email_combo.grid(row=0, column=2, padx=1, pady=1, sticky="ew")

        # Bottom frame for checkbox and button
        bottom_frame = ctk.CTkFrame(create_mini_frame)
        bottom_frame.pack(fill="x", padx=2, pady=1)
        bottom_frame.grid_columnconfigure(0, weight=1)
        bottom_frame.grid_columnconfigure(1, weight=2)

        # Auto-select checkbox - even smaller
        self.auto_select_var = ctk.BooleanVar(value=True)
        auto_select_checkbox = ctk.CTkCheckBox(bottom_frame, text="Auto",
                                              font=ctk.CTkFont(size=10),
                                              variable=self.auto_select_var,
                                              command=self.toggle_auto_select)
        auto_select_checkbox.grid(row=0, column=0, padx=1, pady=1)

        # Combine button - smaller
        combine_button = ctk.CTkButton(bottom_frame, text="Combine",
                                      command=self.combine,
                                      font=ctk.CTkFont(size=10, weight="bold"),
                                      height=20)
        combine_button.grid(row=0, column=1, padx=1, pady=1, sticky="ew")

        # All combinations section - takes up the entire remaining space
        all_combinations_frame = ctk.CTkFrame(content_column)
        all_combinations_frame.pack(fill="both", expand=True, padx=5, pady=2)

        # Header with count
        self.combinations_header_frame = ctk.CTkFrame(all_combinations_frame)
        self.combinations_header_frame.pack(fill="x", padx=5, pady=2)

        self.combinations_count_label = ctk.CTkLabel(self.combinations_header_frame,
                                                  text="All Combinations (0)",
                                                  font=ctk.CTkFont(size=16, weight="bold"))
        self.combinations_count_label.pack(side="left", pady=2)

        # Combinations list with scrollable frame - maximize space
        self.combinations_list_frame = ctk.CTkScrollableFrame(all_combinations_frame)
        self.combinations_list_frame.pack(fill="both", expand=True, padx=5, pady=5)

    def update_latest_combination(self):
        """Update the latest combination display."""
        if self.latest_combination:
            # Clear the frame
            for widget in self.latest_combination_frame.winfo_children():
                widget.destroy()

            # Display the formatted combination with email type (shortened if needed)
            email_type = self.latest_combination.get('email_type', 'Unknown')
            formatted_text = f"{self.latest_combination['formatted']} ({email_type})"
            if len(formatted_text) > 25:  # Make it even shorter
                display_text = formatted_text[:22] + "..."
            else:
                display_text = formatted_text

            label = ctk.CTkLabel(self.latest_combination_frame,
                               text=display_text,
                               font=ctk.CTkFont(size=10))
            label.pack(pady=1)

            # Enable the copy button
            self.latest_copy_btn.configure(state="normal")
        else:
            # No combinations yet
            self.latest_combination_label.configure(text="No combinations yet")
            self.latest_copy_btn.configure(state="disabled")

    def copy_latest_combination(self):
        """Copy the latest combination to the clipboard in Fusion format."""
        if self.latest_combination:
            try:
                # Parse the combination to get card and email parts
                card_parts = self.latest_combination['card'].split(',')
                email = self.latest_combination['email']

                # Format for Fusion button: card_number,month,year,cvv,zip,email
                fusion_format = f"{card_parts[0]},{card_parts[1]},{card_parts[2]},{card_parts[3]},{card_parts[4]},{email}"

                pyperclip.copy(fusion_format)
                self.status_var.set(f"Copied Fusion format: {fusion_format}")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")

    def select_card_for_combination(self, card):
        """Select a card for combination from the archived cards section."""
        self.card_combo.set(card)
        self.status_var.set(f"Selected archived card: {card}")

        # If auto-select is enabled, also select an email
        if self.auto_select_var.get():
            # Get available emails (including archived)
            available_emails = self.data_manager.get_available_emails()
            available_emails.extend(list(self.data_manager.archived_emails))

            # Filter out emails that are currently in use
            available_emails = [email for email in available_emails
                               if email not in self.data_manager.used_emails]

            if available_emails:
                # Select the oldest email (last in the list)
                email = available_emails[-1]
                self.email_combo.set(email)
                self.status_var.set(f"Selected archived card: {card} and email: {email}")
            else:
                messagebox.showinfo("No Available Emails", "No available emails to select.")

    def select_email_for_combination(self, email):
        """Select an email for combination from the archived emails section."""
        self.email_combo.set(email)
        self.status_var.set(f"Selected archived email: {email}")

        # If auto-select is enabled, also select a card
        if self.auto_select_var.get():
            # Get available cards (including archived)
            available_cards = self.data_manager.get_available_cards()
            available_cards.extend(list(self.data_manager.archived_cards))

            # Filter out cards that are currently in use
            available_cards = [card for card in available_cards
                              if card not in self.data_manager.used_cards]

            if available_cards:
                # Select the oldest card (last in the list)
                card = available_cards[-1]
                self.card_combo.set(card)
                self.status_var.set(f"Selected card: {card} and archived email: {email}")
            else:
                messagebox.showinfo("No Available Cards", "No available cards to select.")

    def add_card(self):
        """Add a card to the data manager."""
        card = self.card_entry.get().strip()

        if not validate_card(card):
            messagebox.showerror("Invalid Card", "Please enter a valid card number.")
            return

        # Check if the card has already been used
        if card in self.data_manager.all_used_cards:
            messagebox.showwarning("Card Already Used",
                                "This card has already been used in a previous combination and cannot be reused.")
            self.card_entry.delete(0, "end")
            return

        # Check if the card is currently in use
        if card in self.data_manager.used_cards:
            messagebox.showwarning("Card In Use",
                                "This card is currently being used in an active combination.")
            self.card_entry.delete(0, "end")
            return

        if self.data_manager.add_card(card):
            # Record action for undo/redo
            self.history_manager.record_action(
                ActionType.ADD_CARD,
                f"Add card: {card[:20]}...",
                {'card': card},
                {}
            )

            if hasattr(self, 'status_var'):
                self.status_var.set(f"Card {card} added successfully.")
            self.card_entry.delete(0, "end")
            self.update_lists(['cards', 'comboboxes'])
            # Auto-save after adding a card
            self.save_data()
        else:
            messagebox.showinfo("Duplicate Card", "This card already exists in your available cards.")

    def bulk_add_cards(self):
        """Add multiple cards from a bulk input."""
        # Create a dialog for bulk input
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Bulk Add Cards")
        dialog.geometry("600x400")
        dialog.transient(self.root)  # Make dialog modal
        dialog.grab_set()

        # Instructions
        ctk.CTkLabel(dialog, text="Paste cards below, one per line:",
                    font=ctk.CTkFont(size=14)).pack(pady=(20, 10), padx=20)

        # Example format
        example = "****************,04,30,164,94112\n****************,04,30,164,94112"
        ctk.CTkLabel(dialog, text=f"Example format:\n{example}",
                    font=ctk.CTkFont(size=12)).pack(pady=(0, 10), padx=20)

        # Text area for input
        text_area = ctk.CTkTextbox(dialog, width=550, height=200)
        text_area.pack(padx=20, pady=10, fill="both", expand=True)

        # Function to process the input
        def process_input():
            bulk_text = text_area.get("1.0", "end")
            cards = parse_bulk_cards(bulk_text)

            # Add cards to data manager
            added_count = 0
            duplicate_count = 0
            already_used_count = 0

            for card in cards:
                if not validate_card(card):
                    continue

                # Skip cards that have already been used
                if card in self.data_manager.all_used_cards or card in self.data_manager.used_cards:
                    already_used_count += 1
                    continue

                # Try to add the card
                if self.data_manager.add_card(card):
                    added_count += 1
                else:
                    duplicate_count += 1

            # Update status and close dialog
            status_message = f"Added {added_count} cards from bulk input."
            if duplicate_count > 0:
                status_message += f" Skipped {duplicate_count} duplicate cards."
            if already_used_count > 0:
                status_message += f" Skipped {already_used_count} already used cards."

            self.status_var.set(status_message)
            self.update_lists()

            # Auto-save after bulk adding cards
            if added_count > 0:
                self.save_data()

            dialog.destroy()

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=20)

        ctk.CTkButton(button_frame, text="Add Cards", command=process_input).pack(side="left", padx=10, expand=True, fill="x")
        ctk.CTkButton(button_frame, text="Cancel", command=dialog.destroy).pack(side="right", padx=10, expand=True, fill="x")

    def add_email(self):
        """Add an email to the data manager."""
        email = self.email_entry.get().strip()

        if not validate_email(email):
            messagebox.showerror("Invalid Email", "Please enter a valid email address.")
            return

        # Check if the email has already been used
        if email in self.data_manager.all_used_emails:
            messagebox.showwarning("Email Already Used",
                                "This email has already been used in a previous combination and cannot be reused.")
            self.email_entry.delete(0, "end")
            return

        # Check if the email is currently in use
        if email in self.data_manager.used_emails:
            messagebox.showwarning("Email In Use",
                                "This email is currently being used in an active combination.")
            self.email_entry.delete(0, "end")
            return

        # For now, add to legacy emails (you can modify this to ask user which type)
        if self.data_manager.add_email(email, "legacy"):
            self.status_var.set(f"Email {email} added successfully.")
            self.email_entry.delete(0, "end")
            self.update_lists()
            # Auto-save after adding an email
            self.save_data()
        else:
            messagebox.showinfo("Duplicate Email", "This email already exists in your available emails.")

    def bulk_add_emails(self):
        """Add multiple emails from a bulk input."""
        # Create a dialog for bulk input
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Bulk Add Emails")
        dialog.geometry("600x450")
        dialog.transient(self.root)  # Make dialog modal
        dialog.grab_set()

        # Instructions
        ctk.CTkLabel(dialog, text="Paste emails below, one per line:",
                    font=ctk.CTkFont(size=14)).pack(pady=(20, 10), padx=20)

        # Email type selection
        type_frame = ctk.CTkFrame(dialog)
        type_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(type_frame, text="Select email type:", font=ctk.CTkFont(size=12)).pack(side="left", padx=10)

        email_type_var = ctk.StringVar(value="PM 30/35")
        email_type_combo = ctk.CTkComboBox(type_frame,
                                         values=["PM 30/35", "UE 25/25", "UE 25/15", "UE 25/1"],
                                         variable=email_type_var,
                                         state="readonly", width=150)
        email_type_combo.pack(side="left", padx=10)

        # Example format
        example = "<EMAIL>\<EMAIL>"
        ctk.CTkLabel(dialog, text=f"Example format:\n{example}",
                    font=ctk.CTkFont(size=12)).pack(pady=(10, 10), padx=20)

        # Text area for input
        text_area = ctk.CTkTextbox(dialog, width=550, height=200)
        text_area.pack(padx=20, pady=10, fill="both", expand=True)

        # Function to process the input
        def process_input():
            bulk_text = text_area.get("1.0", "end")
            emails = parse_bulk_emails(bulk_text)

            # Map display names to internal types
            type_mapping = {
                "PM 30/35": "pm_emails",
                "UE 25/25": "ue_emails_25_25",
                "UE 25/15": "ue_emails_25_15",
                "UE 25/1": "ue_emails_25_1"
            }

            selected_type = email_type_var.get()
            email_type = type_mapping.get(selected_type, "pm_emails")

            # Add emails to data manager
            added_count = 0
            duplicate_count = 0
            already_used_count = 0

            for email in emails:
                if not validate_email(email):
                    continue

                # Skip emails that have already been used
                if email in self.data_manager.all_used_emails or email in self.data_manager.used_emails:
                    already_used_count += 1
                    continue

                # Try to add the email with the selected type
                if self.data_manager.add_email(email, email_type):
                    added_count += 1
                else:
                    duplicate_count += 1

            # Update status and close dialog
            status_message = f"Added {added_count} {selected_type} emails from bulk input."
            if duplicate_count > 0:
                status_message += f" Skipped {duplicate_count} duplicate emails."
            if already_used_count > 0:
                status_message += f" Skipped {already_used_count} already used emails."

            self.status_var.set(status_message)
            self.update_lists()

            # Auto-save after bulk adding emails
            if added_count > 0:
                self.save_data()

            dialog.destroy()

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=20)

        ctk.CTkButton(button_frame, text="Add Emails", command=process_input).pack(side="left", padx=10, expand=True, fill="x")
        ctk.CTkButton(button_frame, text="Cancel", command=dialog.destroy).pack(side="right", padx=10, expand=True, fill="x")

    def toggle_auto_select(self):
        """Toggle auto-selection of card and email."""
        if self.auto_select_var.get():
            self.auto_select_card_email()

    def on_email_type_changed(self, selected_type):
        """Handle email type selection change."""
        # Map display names to internal types
        type_mapping = {
            "PM 30/35": "pm_emails",
            "UE 25/25": "ue_emails_25_25",
            "UE 25/15": "ue_emails_25_15",
            "UE 25/1": "ue_emails_25_1"
        }

        email_type = type_mapping.get(selected_type, "pm_emails")

        # Update email combo with emails of the selected type
        available_emails = self.data_manager.get_available_emails(email_type)

        # Add archived emails to the selection pool
        available_emails.extend(list(self.data_manager.archived_emails))

        # Filter out emails that are currently in use
        available_emails = [email for email in available_emails
                           if email not in self.data_manager.used_emails]

        self.email_combo.configure(values=available_emails)

        # Auto-select if enabled and emails are available
        if self.auto_select_var.get() and available_emails:
            # Select the oldest email (last in the list)
            self.email_combo.set(available_emails[-1])
        else:
            self.email_combo.set("")

    def auto_select_card_email(self):
        """Automatically select a card and an email, prioritizing the oldest ones (last in the list).
        Note: Only cards and emails that have NEVER been used before are selected."""
        # Get available cards and emails (those that have NEVER been used and aren't archived)
        available_cards = self.data_manager.get_available_cards()

        # Get emails of the selected type
        type_mapping = {
            "PM 30/35": "pm_emails",
            "UE 25/25": "ue_emails_25_25",
            "UE 25/15": "ue_emails_25_15",
            "UE 25/1": "ue_emails_25_1"
        }

        selected_email_type = self.email_type_combo.get()
        email_type = type_mapping.get(selected_email_type, "pm_emails")
        available_emails = self.data_manager.get_available_emails(email_type)

        # DO NOT add archived cards to the auto-selection pool
        # They should only be selectable manually

        # Debug information - before filtering
        print(f"Before filtering - Available cards: {len(available_cards)}")
        print(f"Before filtering - Available emails: {len(available_emails)}")
        print(f"Before filtering - First few cards: {available_cards[:3] if available_cards else 'None'}")
        print(f"Before filtering - First few emails: {available_emails[:3] if available_emails else 'None'}")

        # Double-check to ensure we NEVER select cards or emails that have been used before
        # This should be redundant with get_available_cards/emails, but we keep it for safety
        available_cards = [card for card in available_cards
                          if card not in self.data_manager.all_used_cards and
                             card not in self.data_manager.used_cards]

        available_emails = [email for email in available_emails
                           if email not in self.data_manager.all_used_emails and
                              email not in self.data_manager.used_emails]

        # Debug information - after filtering
        print(f"After filtering - Available cards for auto-selection: {len(available_cards)} cards")
        print(f"After filtering - Available emails for auto-selection: {len(available_emails)} emails")
        print(f"After filtering - First few cards: {available_cards[:3] if available_cards else 'None'}")
        print(f"After filtering - First few emails: {available_emails[:3] if available_emails else 'None'}")
        print(f"Used cards: {self.data_manager.used_cards}")
        print(f"Used emails: {self.data_manager.used_emails}")
        print(f"All used cards count: {len(self.data_manager.all_used_cards)}")
        print(f"All used emails count: {len(self.data_manager.all_used_emails)}")

        if available_cards and available_emails:
            # Select the oldest card and email (last ones in the list)
            card = available_cards[-1]  # Last card in the list
            email = available_emails[-1]  # Last email in the list

            print(f"Selected card: {card}")
            print(f"Selected email: {email}")

            # Final verification that these items have never been used
            if (card in self.data_manager.all_used_cards or
                email in self.data_manager.all_used_emails or
                card in self.data_manager.used_cards or
                email in self.data_manager.used_emails):
                print(f"WARNING: Attempted to select card or email that has been used: {card}, {email}")
                messagebox.showerror("Selection Error", "Error selecting card/email. The selected items have been used before.")
                # Clear the selections
                self.card_combo.set("")
                self.email_combo.set("")
                return

            # Update the comboboxes
            self.card_combo.set(card)
            self.email_combo.set(email)

            # Update status
            self.status_var.set(f"Selected oldest unused card and email for combination.")
        else:
            # Clear the comboboxes if no available cards or emails
            self.card_combo.set("")
            self.email_combo.set("")

            if not available_cards and not available_emails:
                messagebox.showinfo("No Available Items", "No unused cards or emails available to select.")
            elif not available_cards:
                messagebox.showinfo("No Available Cards", "No unused cards available to select.")
            elif not available_emails:
                messagebox.showinfo("No Available Emails", "No unused emails available to select.")

    def combine(self):
        """Combine a card and an email."""
        # If auto-select is enabled, select a card and email
        if self.auto_select_var.get():
            self.auto_select_card_email()

        card = self.card_combo.get()
        email = self.email_combo.get()

        if not card or not email:
            messagebox.showinfo("Selection Required", "Please select both a card and an email.")
            return

        # Check that the card and email are not currently in use
        # Note: We allow archived cards/emails to be selected
        if card in self.data_manager.used_cards:
            messagebox.showinfo("Card Currently In Use", "This card is currently being used in an active combination.")
            # Reset the card selection
            self.card_combo.set("")
            return

        if email in self.data_manager.used_emails:
            messagebox.showinfo("Email Currently In Use", "This email is currently being used in an active combination.")
            # Reset the email selection
            self.email_combo.set("")
            return

        success, result = self.data_manager.combine(card, email)

        if success:
            # Record action for undo/redo
            self.history_manager.record_action(
                ActionType.CREATE_COMBINATION,
                f"Create combination: {card[:15]}... + {email[:15]}...",
                {'combination': result},
                {}
            )

            # Store the latest combination
            self.latest_combination = result

            # Update the latest combination display
            self.update_latest_combination()

            if hasattr(self, 'status_var'):
                self.status_var.set(f"Combined: {result['formatted']}")
            self.update_lists(['cards', 'emails', 'combinations', 'comboboxes'])

            # Auto-save data after successful combination
            self.save_data()

            # Auto-select next card and email if enabled
            if self.auto_select_var.get():
                self.auto_select_card_email()
        else:
            messagebox.showinfo("Combination Failed", result)

    def delete_combination(self, index=None):
        """Delete a combination.
        If index is provided, delete that specific combination.
        """
        if index is not None:
            # Get combination data before deletion for undo
            combinations = self.data_manager.get_combinations()
            if 0 <= index < len(combinations):
                combination = combinations[index]

                # Record action for undo/redo
                self.history_manager.record_action(
                    ActionType.DELETE_COMBINATION,
                    f"Delete combination: {combination.get('formatted', 'Unknown')[:30]}...",
                    {'index': index},
                    {'combination': combination, 'index': index}
                )

            if self.data_manager.delete_combination(index):
                if hasattr(self, 'status_var'):
                    self.status_var.set("Combination deleted successfully.")
                self.update_lists(['cards', 'emails', 'combinations', 'comboboxes'])

                # Auto-save data after deleting a combination
                self.save_data()

                # Auto-select next card and email if enabled
                if self.auto_select_var.get():
                    self.auto_select_card_email()
            else:
                messagebox.showerror("Error", "Failed to delete combination.")
        else:
            messagebox.showinfo("Selection Required", "Please use the delete button next to a combination.")

    def remove_card(self, card):
        """Remove a card if it's not used in a combination."""
        if self.data_manager.remove_card(card):
            self.status_var.set(f"Card {card} removed successfully.")
            self.update_lists()
            # Auto-save after removing a card
            self.save_data()
        else:
            messagebox.showinfo("Cannot Remove", "This card is being used in a combination.")

    def remove_email(self, email):
        """Remove an email if it's not used in a combination."""
        if self.data_manager.remove_email(email):
            self.status_var.set(f"Email {email} removed successfully.")
            self.update_lists()
            # Auto-save after removing an email
            self.save_data()
        else:
            messagebox.showinfo("Cannot Remove", "This email is being used in a combination.")

    def remove_used_cards(self):
        """Remove all used cards."""
        count = self.data_manager.remove_used_cards()
        if count > 0:
            self.status_var.set(f"Removed {count} used cards.")
            self.update_lists()
            # Auto-save after removing used cards
            self.save_data()
        else:
            messagebox.showinfo("No Cards Removed", "No used cards to remove.")

    def copy_all_used_cards(self):
        """Copy all used cards to the clipboard in the format: number,month,year,cvv,zip."""
        used_cards = list(self.data_manager.all_used_cards)
        if used_cards:
            # Convert 2-digit year to 4-digit year
            formatted_cards = []
            for card in used_cards:
                parts = card.split(',')
                if len(parts) >= 3:  # Make sure we have enough parts
                    # Format: number,month,year,cvv,zip
                    card_number = parts[0]
                    month = parts[1]
                    year = parts[2]
                    cvv = parts[3] if len(parts) > 3 else ""
                    zip_code = parts[4] if len(parts) > 4 else ""

                    # Convert 2-digit year to 4-digit year
                    if len(year) == 2:
                        year = "20" + year

                    # Reconstruct the card with 4-digit year
                    formatted_card = f"{card_number},{month},{year},{cvv},{zip_code}"
                    formatted_cards.append(formatted_card)
                else:
                    # If the card format is unexpected, just use the original
                    formatted_cards.append(card)

            cards_text = "\n".join(formatted_cards)
            try:
                pyperclip.copy(cards_text)
                self.status_var.set(f"Copied {len(used_cards)} used cards to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Used Cards", "There are no used cards to copy.")

    def copy_all_available_cards(self):
        """Copy all available cards to the clipboard in the format: number,month,year,cvv,zip."""
        available_cards = self.data_manager.get_available_cards()
        if available_cards:
            # Convert 2-digit year to 4-digit year (e.g., 30 -> 2030)
            formatted_cards = []
            for card in available_cards:
                parts = card.split(',')
                if len(parts) >= 3:  # Make sure we have enough parts
                    # Format: number,month,year,cvv,zip
                    card_number = parts[0]
                    month = parts[1]
                    year = parts[2]
                    cvv = parts[3] if len(parts) > 3 else ""
                    zip_code = parts[4] if len(parts) > 4 else ""

                    # Convert 2-digit year to 4-digit year
                    if len(year) == 2:
                        year = "20" + year

                    # Reconstruct the card with 4-digit year
                    formatted_card = f"{card_number},{month},{year},{cvv},{zip_code}"
                    formatted_cards.append(formatted_card)
                else:
                    # If the card format is unexpected, just use the original
                    formatted_cards.append(card)

            cards_text = "\n".join(formatted_cards)
            try:
                pyperclip.copy(cards_text)
                self.status_var.set(f"Copied {len(available_cards)} available cards to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Available Cards", "There are no available cards to copy.")

    def copy_all_available_cards_2x(self):
        """Copy all available cards to the clipboard, duplicating each card."""
        available_cards = self.data_manager.get_available_cards()
        if available_cards:
            # Convert 2-digit year to 4-digit year and duplicate each card
            duplicated_cards = []
            for card in available_cards:
                parts = card.split(',')
                if len(parts) >= 3:  # Make sure we have enough parts
                    # Format: number,month,year,cvv,zip
                    card_number = parts[0]
                    month = parts[1]
                    year = parts[2]
                    cvv = parts[3] if len(parts) > 3 else ""
                    zip_code = parts[4] if len(parts) > 4 else ""

                    # Convert 2-digit year to 4-digit year
                    if len(year) == 2:
                        year = "20" + year

                    # Reconstruct the card with 4-digit year
                    formatted_card = f"{card_number},{month},{year},{cvv},{zip_code}"

                    # Add the card twice (duplicated)
                    duplicated_cards.append(formatted_card)
                    duplicated_cards.append(formatted_card)
                else:
                    # If the card format is unexpected, just use the original (duplicated)
                    duplicated_cards.append(card)
                    duplicated_cards.append(card)

            cards_text = "\n".join(duplicated_cards)
            try:
                pyperclip.copy(cards_text)
                self.status_var.set(f"Copied {len(available_cards)} available cards (2x) to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Available Cards", "There are no available cards to copy.")

    def remove_card(self, card):
        """Remove a specific card from the all_used_cards list and move it to archived cards."""
        print(f"Attempting to remove card: {card}")
        print(f"Before removal - all_used_cards: {self.data_manager.all_used_cards}")
        print(f"Before removal - archived_cards: {self.data_manager.archived_cards}")
        print(f"Before removal - cards: {self.data_manager.cards}")

        # Check if the card is in all_used_cards
        if card in self.data_manager.all_used_cards:
            # Remove the card from all_used_cards
            self.data_manager.all_used_cards.remove(card)
            # Add the card to archived cards
            self.data_manager.archived_cards.add(card)
            self.status_var.set(f"Archived card: {card}")

            print(f"After removal - all_used_cards: {self.data_manager.all_used_cards}")
            print(f"After removal - archived_cards: {self.data_manager.archived_cards}")
            print(f"After removal - cards: {self.data_manager.cards}")

            self.update_lists()
            # Auto-save after archiving a card
            self.save_data()
        # Check if the card is in the available cards
        elif card in self.data_manager.cards:
            # Add the card to archived cards
            self.data_manager.archived_cards.add(card)
            self.status_var.set(f"Archived card: {card}")

            print(f"After archiving - archived_cards: {self.data_manager.archived_cards}")
            print(f"After archiving - cards: {self.data_manager.cards}")

            self.update_lists()
            # Auto-save after archiving a card
            self.save_data()
        else:
            print(f"Card not found: {card}")
            messagebox.showinfo("Card Not Found", f"Card {card} not found.")

    def remove_used_emails(self):
        """Remove all used emails."""
        count = self.data_manager.remove_used_emails()
        if count > 0:
            self.status_var.set(f"Removed {count} used emails.")
            self.update_lists()
            # Auto-save after removing used emails
            self.save_data()
        else:
            messagebox.showinfo("No Emails Removed", "No used emails to remove.")

    def copy_all_available_emails(self):
        """Copy all available emails to the clipboard."""
        available_emails = self.data_manager.get_available_emails()
        if available_emails:
            # Format the emails as a string, one per line
            emails_text = "\n".join(available_emails)
            try:
                pyperclip.copy(emails_text)
                self.status_var.set(f"Copied {len(available_emails)} available emails to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Available Emails", "There are no available emails to copy.")

    def copy_all_available_emails_2x(self):
        """Copy all available emails to the clipboard, duplicating each email."""
        available_emails = self.data_manager.get_available_emails()
        if available_emails:
            # Format the emails as a string, duplicating each email
            duplicated_emails = []
            for email in available_emails:
                duplicated_emails.append(email)
                duplicated_emails.append(email)

            emails_text = "\n".join(duplicated_emails)
            try:
                pyperclip.copy(emails_text)
                self.status_var.set(f"Copied {len(available_emails)} available emails (2x) to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Available Emails", "There are no available emails to copy.")

    def delete_all_available_cards(self):
        """Delete all available cards (cards that have never been used)."""
        available_cards = self.data_manager.get_available_cards()
        if not available_cards:
            messagebox.showinfo("No Available Cards", "There are no available cards to delete.")
            return

        # Confirm deletion
        confirm = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete all {len(available_cards)} available cards?\n\nThis action cannot be undone."
        )

        if confirm:
            # Remove all available cards from the data manager
            for card in available_cards.copy():  # Use copy to avoid modifying during iteration
                self.data_manager.cards.remove(card)

            # Update the UI
            self.update_lists()
            self.status_var.set(f"Deleted {len(available_cards)} available cards.")

            # Auto-save after deleting
            self.save_data()

    def delete_all_available_emails(self):
        """Delete all available emails (emails that have never been used)."""
        available_emails = self.data_manager.get_available_emails()
        if not available_emails:
            messagebox.showinfo("No Available Emails", "There are no available emails to delete.")
            return

        # Confirm deletion
        confirm = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete all {len(available_emails)} available emails?\n\nThis action cannot be undone."
        )

        if confirm:
            # Remove all available emails from the data manager
            for email in available_emails.copy():  # Use copy to avoid modifying during iteration
                self.data_manager.emails.remove(email)

            # Update the UI
            self.update_lists()
            self.status_var.set(f"Deleted {len(available_emails)} available emails.")

            # Auto-save after deleting
            self.save_data()

    def copy_all_available_emails_by_type(self, email_type):
        """Copy all available emails of a specific type to the clipboard."""
        available_emails = self.data_manager.get_available_emails(email_type)
        if available_emails:
            # Format the emails as a string, one per line
            emails_text = "\n".join(available_emails)
            try:
                pyperclip.copy(emails_text)
                self.status_var.set(f"Copied {len(available_emails)} available {email_type} emails to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Available Emails", f"There are no available {email_type} emails to copy.")

    def copy_all_available_emails_2x_by_type(self, email_type):
        """Copy all available emails of a specific type to the clipboard, duplicating each email."""
        available_emails = self.data_manager.get_available_emails(email_type)
        if available_emails:
            # Format the emails as a string, duplicating each email
            duplicated_emails = []
            for email in available_emails:
                duplicated_emails.append(email)
                duplicated_emails.append(email)

            emails_text = "\n".join(duplicated_emails)
            try:
                pyperclip.copy(emails_text)
                self.status_var.set(f"Copied {len(available_emails)} available {email_type} emails (2x) to clipboard.")
            except Exception:
                messagebox.showerror("Copy Error", "Failed to copy to clipboard.")
        else:
            messagebox.showinfo("No Available Emails", f"There are no available {email_type} emails to copy.")

    def delete_all_available_emails_by_type(self, email_type):
        """Delete all available emails of a specific type."""
        available_emails = self.data_manager.get_available_emails(email_type)
        if not available_emails:
            messagebox.showinfo("No Available Emails", f"There are no available {email_type} emails to delete.")
            return

        # Confirm deletion
        confirm = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete all {len(available_emails)} available {email_type} emails?\n\nThis action cannot be undone."
        )

        if confirm:
            # Get the appropriate email list
            email_list = self.data_manager.get_email_list_by_type(email_type)

            # Remove all available emails from the data manager
            for email in available_emails.copy():  # Use copy to avoid modifying during iteration
                email_list.remove(email)

            # Update the UI
            self.update_lists()
            self.status_var.set(f"Deleted {len(available_emails)} available {email_type} emails.")

            # Auto-save after deleting
            self.save_data()

    def remove_email(self, email):
        """Remove a specific email and move it to archived emails."""
        print(f"Attempting to remove email: {email}")
        print(f"Before removal - all_used_emails: {self.data_manager.all_used_emails}")
        print(f"Before removal - archived_emails: {self.data_manager.archived_emails}")
        print(f"Before removal - emails: {self.data_manager.emails}")

        # Check if the email is in all_used_emails
        if email in self.data_manager.all_used_emails:
            # Remove the email from all_used_emails
            self.data_manager.all_used_emails.remove(email)
            # Add the email to archived emails
            self.data_manager.archived_emails.add(email)
            self.status_var.set(f"Archived email: {email}")

            print(f"After removal - all_used_emails: {self.data_manager.all_used_emails}")
            print(f"After removal - archived_emails: {self.data_manager.archived_emails}")
            print(f"After removal - emails: {self.data_manager.emails}")

            self.update_lists()
            # Auto-save after archiving an email
            self.save_data()
        # Check if the email is in the available emails
        elif email in self.data_manager.emails:
            # Add the email to archived emails
            self.data_manager.archived_emails.add(email)
            self.status_var.set(f"Archived email: {email}")

            print(f"After archiving - archived_emails: {self.data_manager.archived_emails}")
            print(f"After archiving - emails: {self.data_manager.emails}")

            self.update_lists()
            # Auto-save after archiving an email
            self.save_data()
        else:
            print(f"Email not found: {email}")
            messagebox.showinfo("Email Not Found", f"Email {email} not found.")

    def copy_combination(self, formatted_text, format_type="Standard"):
        """Copy a combination to the clipboard."""
        try:
            pyperclip.copy(formatted_text)
            self.status_var.set(f"Copied {format_type}: {formatted_text}")
        except Exception:
            messagebox.showerror("Copy Error", "Failed to copy to clipboard.")

    def update_lists(self, sections: Optional[List[str]] = None):
        """Update UI lists using the optimized UI manager.

        Args:
            sections: Optional list of sections to update ('cards', 'emails', 'combinations', 'comboboxes').
                     If None, updates all sections.
        """
        if sections is None:
            # Update all sections
            self.ui_manager.update_cards_incremental()
            self.ui_manager.update_emails_incremental()
            self.ui_manager.update_combinations_incremental()
            self.ui_manager.update_comboboxes()
        else:
            # Update specific sections
            self.ui_manager.update_specific_sections(sections)

        # Debug information (only if debug mode is enabled)
        if self.config_manager.is_debug_mode():
            print(f"Currently used cards: {self.data_manager.used_cards}")
            print(f"Currently used emails: {self.data_manager.used_emails}")
            print(f"All used cards: {self.data_manager.all_used_cards}")
            print(f"All used emails: {self.data_manager.all_used_emails}")
            print(f"Archived cards: {self.data_manager.archived_cards}")
            print(f"Archived emails: {self.data_manager.archived_emails}")

        # Update card lists
        all_cards = self.data_manager.get_all_cards()
        available_cards_count = 0
        used_cards_count = 0

        for i, card in enumerate(all_cards):
            # Skip archived cards - they should only appear in the archived section
            if card in self.data_manager.archived_cards:
                continue

            if card in self.data_manager.all_used_cards:
                # Add to used cards list
                card_frame = ctk.CTkFrame(self.used_card_list_frame)
                card_frame.pack(fill="x", pady=2)

                # Mark cards that are currently in use with an indicator
                if card in self.data_manager.used_cards:
                    label = ctk.CTkLabel(card_frame, text=f"{card} (In Use)", anchor="w")
                    # Can't remove cards that are currently in use
                else:
                    label = ctk.CTkLabel(card_frame, text=card, anchor="w")
                    # Add remove button for used cards that are not currently in use
                    remove_btn = ctk.CTkButton(card_frame, text="Remove", width=80,
                                             command=lambda c=card: self.remove_card(c))
                    remove_btn.pack(side="right", padx=5)

                label.pack(side="left", fill="x", expand=True, padx=5)
                used_cards_count += 1
            else:
                # Add to available cards list
                card_frame = ctk.CTkFrame(self.card_list_frame)
                card_frame.pack(fill="x", pady=2)

                label = ctk.CTkLabel(card_frame, text=card, anchor="w")
                label.pack(side="left", fill="x", expand=True, padx=5)
                available_cards_count += 1

                # Add remove button for unused cards
                remove_btn = ctk.CTkButton(card_frame, text="Remove", width=80,
                                         command=lambda c=card: self.remove_card(c))
                remove_btn.pack(side="right", padx=5)

        # Update card count labels
        self.available_cards_label.configure(text=f"Available Cards ({available_cards_count})")
        self.used_cards_label.configure(text=f"Used Cards ({used_cards_count})")

        # Display archived cards
        archived_cards_count = 0
        for card in self.data_manager.archived_cards:
            card_frame = ctk.CTkFrame(self.archived_card_list_frame)
            card_frame.pack(fill="x", pady=2)

            label = ctk.CTkLabel(card_frame, text=card, anchor="w",
                               font=ctk.CTkFont(weight="bold"))  # Bold text for archived cards
            label.pack(side="left", fill="x", expand=True, padx=5)

            # Add a button to use this card in a combination
            use_btn = ctk.CTkButton(card_frame, text="Use", width=60,
                                  command=lambda c=card: self.select_card_for_combination(c))
            use_btn.pack(side="right", padx=5)

            archived_cards_count += 1

        # Update archived cards count label
        self.archived_cards_label.configure(text=f"Archived Cards ({archived_cards_count})")

        # Update email lists for all types
        email_types = [
            ("pm_emails", "PM emails 30 off 35"),
            ("ue_emails_25_25", "UE emails 25 off 25"),
            ("ue_emails_25_15", "UE emails 25 off 15"),
            ("ue_emails_25_1", "UE emails 25 off 1")
        ]

        used_emails_count = 0

        for email_type, section_title in email_types:
            email_list = self.data_manager.get_email_list_by_type(email_type)
            available_emails = self.data_manager.get_available_emails(email_type)

            # Update the count label for this email type
            label_attr = f"available_{email_type}_label"
            if hasattr(self, label_attr):
                getattr(self, label_attr).configure(text=f"{section_title} ({len(available_emails)})")

            # Update the list frame for this email type
            list_frame_attr = f"{email_type}_list_frame"
            if hasattr(self, list_frame_attr):
                list_frame = getattr(self, list_frame_attr)

                for email in available_emails:
                    email_frame = ctk.CTkFrame(list_frame)
                    email_frame.pack(fill="x", pady=2)

                    label = ctk.CTkLabel(email_frame, text=email, anchor="w")
                    label.pack(side="left", fill="x", expand=True, padx=5)

                    # Add remove button for unused emails
                    remove_btn = ctk.CTkButton(email_frame, text="Remove", width=80,
                                             command=lambda e=email: self.remove_email(e))
                    remove_btn.pack(side="right", padx=5)

        # Count used emails from all types
        for email_list in [self.data_manager.emails, self.data_manager.pm_emails,
                          self.data_manager.ue_emails_25_25, self.data_manager.ue_emails_25_15,
                          self.data_manager.ue_emails_25_1]:
            for email in email_list:
                if email in self.data_manager.all_used_emails and email not in self.data_manager.archived_emails:
                    # Add to used emails list
                    email_frame = ctk.CTkFrame(self.used_email_list_frame)
                    email_frame.pack(fill="x", pady=2)

                    # Mark emails that are currently in use with an indicator
                    if email in self.data_manager.used_emails:
                        label = ctk.CTkLabel(email_frame, text=f"{email} (In Use)", anchor="w")
                    else:
                        label = ctk.CTkLabel(email_frame, text=email, anchor="w")

                    label.pack(side="left", fill="x", expand=True, padx=5)
                    used_emails_count += 1

        # Update used emails count label
        self.used_emails_label.configure(text=f"Used Emails ({used_emails_count})")

        # Display archived emails
        archived_emails_count = 0
        for email in self.data_manager.archived_emails:
            email_frame = ctk.CTkFrame(self.archived_email_list_frame)
            email_frame.pack(fill="x", pady=2)

            label = ctk.CTkLabel(email_frame, text=email, anchor="w",
                               font=ctk.CTkFont(weight="bold"))  # Bold text for archived emails
            label.pack(side="left", fill="x", expand=True, padx=5)

            # Add a button to use this email in a combination
            use_btn = ctk.CTkButton(email_frame, text="Use", width=60,
                                  command=lambda e=email: self.select_email_for_combination(e))
            use_btn.pack(side="right", padx=5)

            archived_emails_count += 1

        # Update archived emails count label
        if hasattr(self, 'archived_emails_label'):
            self.archived_emails_label.configure(text=f"Archived Emails ({archived_emails_count})")

        # Update combinations list
        combinations = self.data_manager.get_combinations()

        # Update combinations count label
        self.combinations_count_label.configure(text=f"All Combinations ({len(combinations)})")

        # Add combinations to the list
        for i, combo in enumerate(combinations):
            # Main combination frame - larger and more prominent
            combo_frame = ctk.CTkFrame(self.combinations_list_frame)
            combo_frame.pack(fill="x", pady=8, padx=2)  # Increased padding

            # Highlight the latest combination
            if self.latest_combination and combo == self.latest_combination:
                combo_frame.configure(fg_color=("#e0f0ff", "#2a3b4d"))
                highlight_color = True
            else:
                highlight_color = False

            # Top frame for the combination text
            top_frame = ctk.CTkFrame(combo_frame, fg_color=combo_frame.cget("fg_color"))
            top_frame.pack(fill="x", padx=8, pady=(8, 0))  # Increased padding

            # Combination text with email type - larger font
            email_type = combo.get('email_type', 'Unknown')
            display_text = f"{combo['formatted']} ({email_type})"

            if highlight_color:
                label = ctk.CTkLabel(top_frame, text=display_text, anchor="w",
                                   font=ctk.CTkFont(size=14, weight="bold"))  # Increased font size
            else:
                label = ctk.CTkLabel(top_frame, text=display_text, anchor="w",
                                   font=ctk.CTkFont(size=14))  # Increased font size

            label.pack(fill="x", expand=True, padx=8, pady=4)  # Increased padding

            # Bottom frame for buttons
            btn_frame = ctk.CTkFrame(combo_frame, fg_color=combo_frame.cget("fg_color"))
            btn_frame.pack(fill="x", padx=8, pady=(4, 8))  # Increased padding

            # Parse the combination to get card and email parts
            card_parts = combo['card'].split(',')
            email = combo['email']

            # Format for Wool button: card_number,month/year,cvv,zip,email
            wool_format = f"{card_parts[0]},{card_parts[1]}/{card_parts[2]},{card_parts[3]},{card_parts[4]},{email}"

            # Format for Fusion button: card_number,month,year,cvv,zip,email
            fusion_format = f"{card_parts[0]},{card_parts[1]},{card_parts[2]},{card_parts[3]},{card_parts[4]},{email}"

            # Format for OTP button: just the email
            otp_format = email

            # Wool button - larger
            wool_btn = ctk.CTkButton(btn_frame, text="Wool", width=80, height=30,  # Increased size
                                   command=lambda txt=wool_format: self.copy_combination(txt, "Wool format"),
                                   font=ctk.CTkFont(size=12))  # Increased font size
            wool_btn.pack(side="left", padx=6, pady=4, expand=True, fill="x")  # Increased padding

            # Fusion button - larger
            fusion_btn = ctk.CTkButton(btn_frame, text="Fusion", width=80, height=30,  # Increased size
                                     command=lambda txt=fusion_format: self.copy_combination(txt, "Fusion format"),
                                     font=ctk.CTkFont(size=12))  # Increased font size
            fusion_btn.pack(side="left", padx=6, pady=4, expand=True, fill="x")  # Increased padding

            # OTP button - larger
            otp_btn = ctk.CTkButton(btn_frame, text="OTP", width=80, height=30,  # Increased size
                                  command=lambda txt=otp_format: self.copy_combination(txt, "Email only"),
                                  font=ctk.CTkFont(size=12))  # Increased font size
            otp_btn.pack(side="left", padx=6, pady=4, expand=True, fill="x")  # Increased padding

            # Delete button - larger
            delete_btn = ctk.CTkButton(btn_frame, text="Delete", width=80, height=30,  # Increased size
                                     command=lambda idx=i: self.delete_combination(idx),
                                     font=ctk.CTkFont(size=12))  # Increased font size
            delete_btn.pack(side="left", padx=6, pady=4, expand=True, fill="x")  # Increased padding

        # Update comboboxes
        # Get cards and emails that have never been used (for auto-selection)
        unused_cards = self.data_manager.get_available_cards()

        # For manual selection, include all cards and emails that are not currently in use
        # This includes cards/emails that have been used before but are not currently in use
        manual_selection_cards = []
        for card in self.data_manager.cards:
            if card not in self.data_manager.used_cards:
                manual_selection_cards.append(card)

        # Get all emails from all types that are not currently in use
        manual_selection_emails = []
        for email_list in [self.data_manager.emails, self.data_manager.pm_emails,
                          self.data_manager.ue_emails_25_25, self.data_manager.ue_emails_25_15,
                          self.data_manager.ue_emails_25_1]:
            for email in email_list:
                if email not in self.data_manager.used_emails:
                    manual_selection_emails.append(email)

        # Add archived cards to the selection pool for manual selection
        # (but they won't be auto-selected)
        manual_selection_cards.extend(list(self.data_manager.archived_cards))
        manual_selection_emails.extend(list(self.data_manager.archived_emails))

        # Filter out any cards or emails that are currently in use (double-check)
        manual_selection_cards = [card for card in manual_selection_cards
                                 if card not in self.data_manager.used_cards]

        manual_selection_emails = [email for email in manual_selection_emails
                                  if email not in self.data_manager.used_emails]

        # Update the dropdown menus with all available cards and emails (including archived and previously used)
        self.card_combo.configure(values=manual_selection_cards)
        self.email_combo.configure(values=manual_selection_emails)

        # Initialize email combo based on selected email type
        if hasattr(self, 'email_type_combo'):
            self.on_email_type_changed(self.email_type_combo.get())

        # Auto-select card and email if enabled
        # Note: auto-selection will only use cards and emails that have NEVER been used before
        if self.auto_select_var.get() and unused_cards:
            # Get emails of the selected type
            type_mapping = {
                "PM 30/35": "pm_emails",
                "UE 25/25": "ue_emails_25_25",
                "UE 25/15": "ue_emails_25_15",
                "UE 25/1": "ue_emails_25_1"
            }

            selected_email_type = self.email_type_combo.get() if hasattr(self, 'email_type_combo') else "PM 30/35"
            email_type = type_mapping.get(selected_email_type, "pm_emails")
            available_emails_of_type = self.data_manager.get_available_emails(email_type)

            if available_emails_of_type:
                self.auto_select_card_email()

    def setup_auto_save_timer(self):
        """Set up a timer to automatically save data periodically with configurable interval."""
        if self.auto_save_timer:
            self.root.after_cancel(self.auto_save_timer)

        def auto_save():
            if self.config_manager.is_auto_save_enabled():
                # Only save if there have been changes since the last save
                current_time = time.time()
                interval = self.config_manager.get_auto_save_interval()

                if current_time - self.last_save_time >= interval:
                    if self.config_manager.is_debug_mode():
                        print("Auto-saving data...")
                    self.save_data(auto_save=True)

                # Schedule the next auto-save
                self.auto_save_timer = self.root.after(interval * 1000, auto_save)

        # Start the auto-save timer
        interval = self.config_manager.get_auto_save_interval()
        self.auto_save_timer = self.root.after(interval * 1000, auto_save)

    def update_save_status(self):
        """Update the save status indicator."""
        if hasattr(self, 'save_status_var'):
            # Format the last save time
            last_save_time_str = time.strftime("%H:%M:%S", time.localtime(self.last_save_time))
            self.save_status_var.set(f"Last saved: {last_save_time_str}")

    def save_data(self, auto_save=False):
        """Save the current data with enhanced error handling and backup."""
        # Make sure all_used sets include all used items before saving
        self.data_manager.all_used_cards.update(self.data_manager.used_cards)
        self.data_manager.all_used_emails.update(self.data_manager.used_emails)

        try:
            # Create backup before saving (using enhanced backup manager)
            if not auto_save:
                self.error_handler.backup_manager.create_backup("manual")

            # Use enhanced save with validation
            success, error_msg = self.error_handler.safe_save_data(self.data_manager)

            if success:
                # Update last save time
                self.last_save_time = time.time()

                # Update the save status indicator
                self.update_save_status()

                if not auto_save:  # Only show message if not auto-saving
                    if hasattr(self, 'status_var'):
                        self.status_var.set("Data saved successfully.")

                # Print debug information (only if debug mode is enabled)
                if self.config_manager.is_debug_mode():
                    print(f"\nAfter saving:")
                    print(f"Used cards: {self.data_manager.used_cards}")
                    print(f"Used emails: {self.data_manager.used_emails}")
                    print(f"All used cards: {self.data_manager.all_used_cards}")
                    print(f"All used emails: {self.data_manager.all_used_emails}")
                    print(f"Archived cards: {self.data_manager.archived_cards}")
                    print(f"Archived emails: {self.data_manager.archived_emails}")

                # Save window geometry to config
                self.save_window_geometry()

                return True
            else:
                if not auto_save:
                    messagebox.showerror("Save Error", f"Failed to save data: {error_msg}")
                if self.config_manager.is_debug_mode():
                    print(f"Save error: {error_msg}")
                return False

        except Exception as e:
            if not auto_save:
                messagebox.showerror("Save Error", f"Unexpected error while saving: {str(e)}")
            if self.config_manager.is_debug_mode():
                print(f"Unexpected save error: {str(e)}")
            return False

    def save_window_geometry(self):
        """Save current window geometry to config."""
        try:
            geometry = self.root.geometry()
            # Parse geometry string (e.g., "900x650+100+50")
            size_pos = geometry.split('+')
            size = size_pos[0].split('x')

            width = int(size[0])
            height = int(size[1])
            x = int(size_pos[1]) if len(size_pos) > 1 else None
            y = int(size_pos[2]) if len(size_pos) > 2 else None

            self.config_manager.set_window_geometry(width, height, x, y)
        except Exception as e:
            if self.config_manager.is_debug_mode():
                print(f"Failed to save window geometry: {e}")

def main():
    root = ctk.CTk()
    app = CardManagerApp(root)

    # Add window closing event handler to save data when the application is closed
    def on_closing():
        # Save data before closing (re-enabled with enhanced error handling)
        app.save_data()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
