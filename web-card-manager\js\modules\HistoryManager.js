/**
 * History Manager - Handles undo/redo functionality
 */

import { deepClone } from '../utils/helpers.js';

export class HistoryManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.uiManager = null;
        this.history = [];
        this.currentIndex = -1;
        this.maxHistorySize = 50;
        this.isRestoring = false;
    }

    /**
     * Initialize history manager
     */
    async init() {
        console.log('⏱️ Initializing History Manager...');
        
        // Save initial state
        this.saveState('Initial state');
        
        // Listen to data changes
        this.setupDataListeners();
        
        console.log('✅ History Manager initialized');
    }

    /**
     * Set UI manager reference
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
    }

    /**
     * Set up data manager listeners
     */
    setupDataListeners() {
        // Listen to data changes and save states
        this.dataManager.on('cardAdded', () => this.saveState('Card added'));
        this.dataManager.on('cardsAdded', () => this.saveState('Cards added'));
        this.dataManager.on('cardRemoved', () => this.saveState('Card removed'));
        this.dataManager.on('cardArchived', () => this.saveState('Card archived'));
        this.dataManager.on('cardRestored', () => this.saveState('Card restored'));
        
        this.dataManager.on('emailAdded', () => this.saveState('Email added'));
        this.dataManager.on('emailsAdded', () => this.saveState('Emails added'));
        this.dataManager.on('emailRemoved', () => this.saveState('Email removed'));
        
        this.dataManager.on('combinationCreated', () => this.saveState('Combination created'));
        this.dataManager.on('combinationDeleted', () => this.saveState('Combination deleted'));
        
        this.dataManager.on('dataImported', () => this.saveState('Data imported'));
        this.dataManager.on('dataCleared', () => this.saveState('Data cleared'));
    }

    /**
     * Save current state to history
     */
    saveState(description = 'State change') {
        if (this.isRestoring) return;
        
        try {
            // Get current data state
            const state = {
                cards: Array.from(this.dataManager.cards),
                pmEmails: Array.from(this.dataManager.pmEmails),
                ueEmails2525: Array.from(this.dataManager.ueEmails2525),
                ueEmails2515: Array.from(this.dataManager.ueEmails2515),
                ueEmails251: Array.from(this.dataManager.ueEmails251),
                usedCards: Array.from(this.dataManager.usedCards),
                usedEmails: Array.from(this.dataManager.usedEmails),
                allUsedCards: Array.from(this.dataManager.allUsedCards),
                allUsedEmails: Array.from(this.dataManager.allUsedEmails),
                archivedCards: Array.from(this.dataManager.archivedCards),
                archivedEmails: Array.from(this.dataManager.archivedEmails),
                combinations: deepClone(this.dataManager.combinations),
                timestamp: Date.now(),
                description
            };
            
            // Remove any states after current index (when undoing then making new changes)
            if (this.currentIndex < this.history.length - 1) {
                this.history = this.history.slice(0, this.currentIndex + 1);
            }
            
            // Add new state
            this.history.push(state);
            this.currentIndex = this.history.length - 1;
            
            // Limit history size
            if (this.history.length > this.maxHistorySize) {
                this.history.shift();
                this.currentIndex--;
            }
            
            // Update UI buttons
            this.updateUIButtons();
            
        } catch (error) {
            console.error('Failed to save state:', error);
        }
    }

    /**
     * Undo last action
     */
    undo() {
        if (!this.canUndo()) return false;
        
        this.currentIndex--;
        const state = this.history[this.currentIndex];
        
        this.restoreState(state);
        this.updateUIButtons();
        
        return true;
    }

    /**
     * Redo last undone action
     */
    redo() {
        if (!this.canRedo()) return false;
        
        this.currentIndex++;
        const state = this.history[this.currentIndex];
        
        this.restoreState(state);
        this.updateUIButtons();
        
        return true;
    }

    /**
     * Check if undo is possible
     */
    canUndo() {
        return this.currentIndex > 0;
    }

    /**
     * Check if redo is possible
     */
    canRedo() {
        return this.currentIndex < this.history.length - 1;
    }

    /**
     * Restore state
     */
    restoreState(state) {
        this.isRestoring = true;
        
        try {
            // Restore data structures
            this.dataManager.cards = new Set(state.cards || []);
            this.dataManager.pmEmails = new Set(state.pmEmails || []);
            this.dataManager.ueEmails2525 = new Set(state.ueEmails2525 || []);
            this.dataManager.ueEmails2515 = new Set(state.ueEmails2515 || []);
            this.dataManager.ueEmails251 = new Set(state.ueEmails251 || []);
            this.dataManager.usedCards = new Set(state.usedCards || []);
            this.dataManager.usedEmails = new Set(state.usedEmails || []);
            this.dataManager.allUsedCards = new Set(state.allUsedCards || []);
            this.dataManager.allUsedEmails = new Set(state.allUsedEmails || []);
            this.dataManager.archivedCards = new Set(state.archivedCards || []);
            this.dataManager.archivedEmails = new Set(state.archivedEmails || []);
            this.dataManager.combinations = deepClone(state.combinations || []);
            
            // Save to storage
            this.dataManager.saveData();
            
            // Update UI
            if (this.uiManager) {
                this.uiManager.refreshAllLists();
                this.uiManager.updateAllCounts();
            }
            
            console.log('State restored:', state.description);
            
        } catch (error) {
            console.error('Failed to restore state:', error);
        } finally {
            this.isRestoring = false;
        }
    }

    /**
     * Update UI buttons
     */
    updateUIButtons() {
        if (this.uiManager) {
            this.uiManager.updateHistoryButtons();
        }
    }

    /**
     * Get current state description
     */
    getCurrentStateDescription() {
        if (this.currentIndex >= 0 && this.currentIndex < this.history.length) {
            return this.history[this.currentIndex].description;
        }
        return 'No state';
    }

    /**
     * Get history summary
     */
    getHistorySummary() {
        return this.history.map((state, index) => ({
            index,
            description: state.description,
            timestamp: state.timestamp,
            isCurrent: index === this.currentIndex
        }));
    }

    /**
     * Jump to specific state
     */
    jumpToState(index) {
        if (index < 0 || index >= this.history.length) return false;
        
        this.currentIndex = index;
        const state = this.history[index];
        
        this.restoreState(state);
        this.updateUIButtons();
        
        return true;
    }

    /**
     * Clear history
     */
    clearHistory() {
        this.history = [];
        this.currentIndex = -1;
        this.updateUIButtons();
    }

    /**
     * Set max history size
     */
    setMaxHistorySize(size) {
        this.maxHistorySize = Math.max(1, size);
        
        // Trim history if needed
        if (this.history.length > this.maxHistorySize) {
            const excess = this.history.length - this.maxHistorySize;
            this.history.splice(0, excess);
            this.currentIndex = Math.max(0, this.currentIndex - excess);
        }
    }

    /**
     * Get history size
     */
    getHistorySize() {
        return this.history.length;
    }

    /**
     * Export history
     */
    exportHistory() {
        return {
            history: this.history,
            currentIndex: this.currentIndex,
            maxHistorySize: this.maxHistorySize,
            exportedAt: Date.now()
        };
    }

    /**
     * Import history
     */
    importHistory(historyData) {
        try {
            if (!historyData || !Array.isArray(historyData.history)) {
                throw new Error('Invalid history data');
            }
            
            this.history = historyData.history;
            this.currentIndex = historyData.currentIndex || 0;
            this.maxHistorySize = historyData.maxHistorySize || 50;
            
            // Validate current index
            if (this.currentIndex >= this.history.length) {
                this.currentIndex = this.history.length - 1;
            }
            
            this.updateUIButtons();
            
            return true;
            
        } catch (error) {
            console.error('Failed to import history:', error);
            return false;
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this.clearHistory();
        this.uiManager = null;
    }
}
