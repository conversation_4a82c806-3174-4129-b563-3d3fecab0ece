<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initialization Test - Card Manager</title>
    
    <!-- Load all the CSS files like the main app -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    
    <style>
        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: Arial, sans-serif;
        }
        .test-panel {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 12px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .test-status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .warning { background: #5a5a2d; }
        .info { background: #2d2d5a; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <!-- Copy the exact HTML structure from index.html -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading Card Manager...</p>
        </div>
    </div>

    <div id="main-app" class="main-app hidden">
        <div class="app-container">
            <!-- Simplified structure with just the bulk buttons -->
            <div class="content-area">
                <div class="tab-content">
                    <div class="tab-panel active" id="cards-tab">
                        <div class="section">
                            <div class="section-header">
                                <h2>Add Cards</h2>
                                <div class="section-actions">
                                    <button id="bulk-add-cards-btn" class="secondary-btn">
                                        <i class="fas fa-plus-square"></i>
                                        Bulk Add
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-panel" id="emails-tab">
                        <div class="section">
                            <div class="section-header">
                                <h2>Add Emails</h2>
                                <div class="section-actions">
                                    <button id="bulk-add-emails-btn" class="secondary-btn">
                                        <i class="fas fa-plus-square"></i>
                                        Bulk Add
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal overlay -->
    <div id="modal-overlay" class="modal-overlay hidden"></div>

    <!-- Test overlay -->
    <div class="test-overlay" id="test-overlay">
        <div class="test-panel">
            <h1>🧪 Card Manager Initialization Test</h1>
            
            <div style="margin: 20px 0;">
                <button onclick="startTest()">Start Test</button>
                <button onclick="testBulkButtons()">Test Bulk Buttons</button>
                <button onclick="clearResults()">Clear Results</button>
                <button onclick="hideTestPanel()">Hide Test Panel</button>
            </div>
            
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        let testResults = document.getElementById('test-results');
        let testStep = 0;
        
        function addResult(type, message) {
            const div = document.createElement('div');
            div.className = `test-status ${type}`;
            div.textContent = `Step ${++testStep}: ${message}`;
            testResults.appendChild(div);
            testResults.scrollTop = testResults.scrollHeight;
            console.log(`[${type.toUpperCase()}] Step ${testStep}: ${message}`);
        }
        
        function clearResults() {
            testResults.innerHTML = '';
            testStep = 0;
        }
        
        function hideTestPanel() {
            document.getElementById('test-overlay').style.display = 'none';
        }
        
        async function startTest() {
            clearResults();
            addResult('info', 'Starting Card Manager initialization test...');
            
            try {
                // Test 1: Check DOM elements
                addResult('info', 'Checking DOM elements...');
                const loadingScreen = document.getElementById('loading-screen');
                const mainApp = document.getElementById('main-app');
                const bulkCardsBtn = document.getElementById('bulk-add-cards-btn');
                const bulkEmailsBtn = document.getElementById('bulk-add-emails-btn');
                const modalOverlay = document.getElementById('modal-overlay');
                
                if (loadingScreen) addResult('success', 'loading-screen found');
                else addResult('error', 'loading-screen NOT found');
                
                if (mainApp) addResult('success', 'main-app found');
                else addResult('error', 'main-app NOT found');
                
                if (bulkCardsBtn) addResult('success', 'bulk-add-cards-btn found');
                else addResult('error', 'bulk-add-cards-btn NOT found');
                
                if (bulkEmailsBtn) addResult('success', 'bulk-add-emails-btn found');
                else addResult('error', 'bulk-add-emails-btn NOT found');
                
                if (modalOverlay) addResult('success', 'modal-overlay found');
                else addResult('error', 'modal-overlay NOT found');
                
                // Test 2: Import main module
                addResult('info', 'Importing main.js module...');
                const mainModule = await import('./js/main.js');
                addResult('success', 'main.js imported successfully');
                
                if (mainModule.CardManagerApp) {
                    addResult('success', 'CardManagerApp class found in module');
                } else {
                    addResult('error', 'CardManagerApp class NOT found in module');
                }
                
                // Test 3: Create app instance
                addResult('info', 'Creating CardManagerApp instance...');
                const app = new mainModule.CardManagerApp();
                addResult('success', 'CardManagerApp instance created');
                
                // Test 4: Initialize app
                addResult('info', 'Initializing CardManagerApp...');
                await app.init();
                addResult('success', 'CardManagerApp initialized successfully');
                
                // Test 5: Check global variable
                if (window.cardManagerApp) {
                    addResult('success', 'window.cardManagerApp is available');
                    addResult('info', `App initialized: ${window.cardManagerApp.isInitialized}`);
                } else {
                    addResult('warning', 'window.cardManagerApp not set (this is expected in manual test)');
                }
                
                // Test 6: Check managers
                if (app.managers) {
                    addResult('success', 'App managers object exists');
                    
                    const managers = ['data', 'ui', 'modal', 'toast', 'config', 'search', 'history', 'autoSave'];
                    managers.forEach(name => {
                        if (app.managers[name]) {
                            addResult('success', `${name} manager found`);
                        } else {
                            addResult('error', `${name} manager NOT found`);
                        }
                    });
                } else {
                    addResult('error', 'App managers object NOT found');
                }
                
                // Test 7: Test bulk button functionality
                addResult('info', 'Testing bulk button functionality...');
                if (app.managers.ui && typeof app.managers.ui.showBulkAddModal === 'function') {
                    addResult('success', 'showBulkAddModal method found');
                    
                    // Try to call the method
                    try {
                        app.managers.ui.showBulkAddModal('cards');
                        addResult('success', 'showBulkAddModal called successfully');
                    } catch (error) {
                        addResult('error', `showBulkAddModal failed: ${error.message}`);
                    }
                } else {
                    addResult('error', 'showBulkAddModal method NOT found');
                }
                
                addResult('success', 'All tests completed!');
                
            } catch (error) {
                addResult('error', `Test failed: ${error.message}`);
                console.error('Test error:', error);
            }
        }
        
        function testBulkButtons() {
            addResult('info', 'Testing bulk button clicks...');
            
            const bulkCardsBtn = document.getElementById('bulk-add-cards-btn');
            const bulkEmailsBtn = document.getElementById('bulk-add-emails-btn');
            
            if (bulkCardsBtn) {
                addResult('info', 'Clicking bulk cards button...');
                bulkCardsBtn.click();
                addResult('success', 'Bulk cards button clicked');
            }
            
            if (bulkEmailsBtn) {
                addResult('info', 'Clicking bulk emails button...');
                bulkEmailsBtn.click();
                addResult('success', 'Bulk emails button clicked');
            }
        }
        
        // Auto-start test when page loads
        window.addEventListener('load', () => {
            setTimeout(startTest, 1000);
        });
    </script>
</body>
</html>
