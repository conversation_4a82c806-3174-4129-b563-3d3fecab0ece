"""
Recovery script to restore all used cards and emails.
This script will update the data file to include all previously used cards and emails.
"""
import json
import os

# Define the data file path
DATA_FILE = "card_manager_data.json"

# Define the list of all used cards and emails
ALL_USED_CARDS = [
    "****************,04,30,821,94112",
    "****************,04,30,300,94112",
    "****************,04,30,156,94112",
    "****************,04,30,441,94112",
    "****************,04,30,735,94112",
    "****************,04,30,835,94112",
    "****************,04,30,511,94112",
    "****************,04,30,005,94112",
    "****************,04,30,137,94112",
    "****************,04,30,973,94112",
    "****************,04,30,095,94112",
    "****************,04,30,567,94112",
    "****************,04,30,941,94112",
    "****************,04,30,820,94112",
    "****************,04,30,015,94112",
    "****************,04,30,991,94112",
    "****************,04,30,001,94112",
    "****************,04,30,780,94112",
    "****************,04,30,049,94112",
    "****************,04,30,216,94112",
    "****************,04,30,129,94112"
]

ALL_USED_EMAILS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
]

def recover_used_items():
    """Recover all used cards and emails."""
    # Check if the data file exists
    if not os.path.exists(DATA_FILE):
        print(f"Error: Data file {DATA_FILE} not found.")
        return False
    
    try:
        # Load the existing data
        with open(DATA_FILE, 'r') as f:
            data = json.load(f)
        
        # Add the all_used_cards and all_used_emails fields
        data["all_used_cards"] = ALL_USED_CARDS
        data["all_used_emails"] = ALL_USED_EMAILS
        
        # Make sure used_cards and used_emails are included in all_used_cards and all_used_emails
        for card in data.get("used_cards", []):
            if card not in data["all_used_cards"]:
                data["all_used_cards"].append(card)
        
        for email in data.get("used_emails", []):
            if email not in data["all_used_emails"]:
                data["all_used_emails"].append(email)
        
        # Save the updated data
        with open(DATA_FILE, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Successfully recovered {len(data['all_used_cards'])} used cards and {len(data['all_used_emails'])} used emails.")
        return True
    except Exception as e:
        print(f"Error recovering used items: {e}")
        return False

if __name__ == "__main__":
    recover_used_items()
