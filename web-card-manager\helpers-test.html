<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Helpers Test</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff0000;
            color: #ff4444;
        }
    </style>
</head>
<body>
    <h1>🧪 Helpers Test</h1>
    <div id="test-results"></div>
    
    <!-- Test elements -->
    <div id="test-element-1">Test Element 1</div>
    <div id="test-element-2">Test Element 2</div>

    <script type="module">
        const resultsDiv = document.getElementById('test-results');
        
        function addResult(message, success = true) {
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
            console.log(`[${success ? 'SUCCESS' : 'ERROR'}] ${message}`);
        }

        async function runTests() {
            try {
                addResult('Starting helpers test...');
                
                // Test 1: Import helpers
                addResult('Importing helpers...');
                const helpers = await import('./js/utils/helpers.js');
                addResult('✅ Helpers imported successfully');
                addResult(`Available functions: ${Object.keys(helpers).join(', ')}`);
                
                // Test 2: Test getElementById
                addResult('Testing getElementById...');
                const { getElementById } = helpers;
                
                const element1 = getElementById('test-element-1');
                if (element1) {
                    addResult('✅ getElementById found existing element');
                } else {
                    addResult('❌ getElementById failed to find existing element', false);
                }
                
                const nonExistent = getElementById('non-existent-element');
                if (!nonExistent) {
                    addResult('✅ getElementById correctly returned null for non-existent element');
                } else {
                    addResult('❌ getElementById should return null for non-existent element', false);
                }
                
                // Test 3: Test createElement
                addResult('Testing createElement...');
                const { createElement } = helpers;
                
                const newElement = createElement('div', { 
                    className: 'test-class',
                    textContent: 'Test content'
                });
                
                if (newElement && newElement.tagName === 'DIV') {
                    addResult('✅ createElement created element successfully');
                } else {
                    addResult('❌ createElement failed', false);
                }
                
                // Test 4: Test other utilities
                addResult('Testing other utilities...');
                const { copyToClipboard, animateElement } = helpers;
                
                if (typeof copyToClipboard === 'function') {
                    addResult('✅ copyToClipboard function available');
                } else {
                    addResult('❌ copyToClipboard function missing', false);
                }
                
                if (typeof animateElement === 'function') {
                    addResult('✅ animateElement function available');
                } else {
                    addResult('❌ animateElement function missing', false);
                }
                
                addResult('🎉 All helpers tests completed!');
                
            } catch (error) {
                addResult(`❌ Helpers test failed: ${error.message}`, false);
                console.error('Helpers test error:', error);
            }
        }

        // Run tests when DOM is ready
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
