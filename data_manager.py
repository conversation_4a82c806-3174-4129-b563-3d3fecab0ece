"""
Data management module for the Card Manager application.
Handles the logic for cards, emails, and their combinations.
"""

class DataManager:
    def __init__(self):
        # Initialize empty lists for cards, emails, and combinations
        self.cards = []
        # Different email types
        self.pm_emails = []  # PM emails 30 off 35
        self.ue_emails_25_25 = []  # UE emails 25 off 25
        self.ue_emails_25_15 = []  # UE emails 25 off 15
        self.ue_emails_25_1 = []  # UE emails 25 off 1
        # Legacy emails list for backward compatibility
        self.emails = []
        self.combinations = []
        # Track which cards and emails are currently in use
        self.used_cards = set()
        self.used_emails = set()
        # Track which cards and emails have ever been used
        self.all_used_cards = set()
        self.all_used_emails = set()
        # Track archived cards and emails (removed from used but not put back into available)
        self.archived_cards = set()
        self.archived_emails = set()

    def initialize_used_sets(self):
        """Initialize the all_used sets from the current used sets.
        This is used when loading data from a file that doesn't have all_used sets.
        """
        # Add all currently used items to the all_used sets
        self.all_used_cards.update(self.used_cards)
        self.all_used_emails.update(self.used_emails)

    def add_card(self, card_number):
        """Add a card if it doesn't already exist."""
        if card_number and card_number not in self.cards:
            self.cards.append(card_number)
            return True
        return False

    def add_email(self, email, email_type="legacy"):
        """Add an email if it doesn't already exist."""
        if not email:
            return False

        # Get the appropriate email list based on type
        email_list = self.get_email_list_by_type(email_type)

        if email not in email_list:
            email_list.append(email)
            return True
        return False

    def get_email_list_by_type(self, email_type):
        """Get the email list for a specific type."""
        if email_type == "pm_emails":
            return self.pm_emails
        elif email_type == "ue_emails_25_25":
            return self.ue_emails_25_25
        elif email_type == "ue_emails_25_15":
            return self.ue_emails_25_15
        elif email_type == "ue_emails_25_1":
            return self.ue_emails_25_1
        else:  # legacy or unknown
            return self.emails

    def combine(self, card, email, email_type=None):
        """Combine a card and email if neither is currently in use."""
        # Check if card exists
        if card not in self.cards:
            return False, "Card not found."

        # Check if email exists in any email list and determine its type
        email_found = False
        detected_email_type = None

        email_type_mapping = {
            "emails": "Legacy",
            "pm_emails": "PM 30/35",
            "ue_emails_25_25": "UE 25/25",
            "ue_emails_25_15": "UE 25/15",
            "ue_emails_25_1": "UE 25/1"
        }

        for list_name, email_list in [("emails", self.emails), ("pm_emails", self.pm_emails),
                                     ("ue_emails_25_25", self.ue_emails_25_25),
                                     ("ue_emails_25_15", self.ue_emails_25_15),
                                     ("ue_emails_25_1", self.ue_emails_25_1)]:
            if email in email_list:
                email_found = True
                detected_email_type = email_type_mapping[list_name]
                break

        if not email_found:
            return False, "Email not found."

        if card in self.used_cards:
            return False, "This card is currently in use."

        if email in self.used_emails:
            return False, "This email is currently in use."

        # Create a combination with email type information
        combination = {
            "card": card,
            "email": email,
            "email_type": detected_email_type,
            "formatted": f"{card},{email}"  # Using comma format
        }

        self.combinations.append(combination)
        # Mark as currently in use
        self.used_cards.add(card)
        self.used_emails.add(email)
        # Mark as ever used
        self.all_used_cards.add(card)
        self.all_used_emails.add(email)
        # Remove from archived if they were there
        if card in self.archived_cards:
            self.archived_cards.remove(card)
        if email in self.archived_emails:
            self.archived_emails.remove(email)
        return True, combination

    def delete_combination(self, index):
        """Delete a combination and free up the card and email.
        Note: The card and email remain marked as 'ever used'.
        """
        if 0 <= index < len(self.combinations):
            combination = self.combinations.pop(index)
            # Remove from currently in use
            self.used_cards.remove(combination["card"])
            self.used_emails.remove(combination["email"])
            # But keep in all_used sets
            return True
        return False

    def get_available_cards(self):
        """Return cards that have never been used and aren't archived."""
        # Return cards that have never been used and are not archived
        available_cards = []
        for card in self.cards:
            # Exclude cards that have ever been used or are archived
            if card not in self.all_used_cards and card not in self.archived_cards:
                available_cards.append(card)
        return available_cards

    def get_all_cards(self):
        """Return all cards."""
        return self.cards

    def get_available_emails(self, email_type="legacy"):
        """Return emails that have never been used and aren't archived."""
        # Get the appropriate email list based on type
        email_list = self.get_email_list_by_type(email_type)

        # Return emails that have never been used and are not archived
        available_emails = []
        for email in email_list:
            # Exclude emails that have ever been used or are archived
            if email not in self.all_used_emails and email not in self.archived_emails:
                available_emails.append(email)
        return available_emails

    def get_all_available_emails(self):
        """Return all available emails from all types."""
        all_available = []
        for email_type in ["legacy", "pm_emails", "ue_emails_25_25", "ue_emails_25_15", "ue_emails_25_1"]:
            all_available.extend(self.get_available_emails(email_type))
        return all_available

    def get_all_emails(self):
        """Return all emails."""
        return self.emails

    def remove_card(self, card):
        """Remove a card if it's not currently used in a combination."""
        if card in self.cards and card not in self.used_cards:
            self.cards.remove(card)
            return True
        return False

    def remove_email(self, email):
        """Remove an email if it's not currently used in a combination."""
        if email in self.used_emails:
            return False

        # Check all email lists and remove from the one that contains it
        for email_list in [self.emails, self.pm_emails, self.ue_emails_25_25,
                          self.ue_emails_25_15, self.ue_emails_25_1]:
            if email in email_list:
                email_list.remove(email)
                return True
        return False

    def remove_used_cards(self):
        """Remove all cards that have ever been used."""
        removed_count = 0
        # Create a copy of the list to avoid modification during iteration
        for card in list(self.cards):
            if card in self.all_used_cards:
                self.cards.remove(card)
                removed_count += 1
        return removed_count

    def remove_used_emails(self):
        """Remove all emails that have ever been used."""
        removed_count = 0
        # Check all email lists and remove used emails
        for email_list in [self.emails, self.pm_emails, self.ue_emails_25_25,
                          self.ue_emails_25_15, self.ue_emails_25_1]:
            # Create a copy of the list to avoid modification during iteration
            for email in list(email_list):
                if email in self.all_used_emails:
                    email_list.remove(email)
                    removed_count += 1
        return removed_count

    def get_combinations(self):
        """Return all combinations."""
        return self.combinations
