<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4f7cff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b5bdb;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #444;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Import Functionality Test</h1>
        
        <div class="test-section">
            <h2>📋 Test Console</h2>
            <div id="log" class="log">Starting import functionality test...\n</div>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h2>🎯 Import Tests</h2>
            <button onclick="testImportButton()">Test Import Button Click</button>
            <button onclick="testImportModal()">Test Import Modal Creation</button>
            <button onclick="testSampleImport()">Test Sample Data Import</button>
        </div>

        <div class="test-section">
            <h2>📱 Card Manager Application</h2>
            <iframe id="app-frame" src="index.html"></iframe>
        </div>
    </div>

    <script>
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function testImportButton() {
            log('🔍 Testing import button click...');
            
            const frame = document.getElementById('app-frame');
            const frameDoc = frame.contentDocument || frame.contentWindow.document;
            const frameWindow = frame.contentWindow;
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found in frame');
                return;
            }
            
            const importBtn = frameDoc.getElementById('import-btn');
            if (!importBtn) {
                log('❌ Import button not found');
                return;
            }
            
            log('✅ Import button found, clicking...');
            importBtn.click();
            
            setTimeout(() => {
                const modal = frameDoc.querySelector('.import-modal');
                if (modal) {
                    log('✅ Import modal appeared successfully!');
                } else {
                    log('❌ Import modal did not appear');
                }
            }, 500);
        }

        function testImportModal() {
            log('🔍 Testing import modal creation directly...');
            
            const frame = document.getElementById('app-frame');
            const frameWindow = frame.contentWindow;
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found');
                return;
            }
            
            const modalManager = frameWindow.cardManagerApp.managers?.modal;
            if (!modalManager) {
                log('❌ Modal manager not found');
                return;
            }
            
            log('✅ Modal manager found, calling showImportModal...');
            try {
                modalManager.showImportModal((data) => {
                    log('✅ Import modal callback triggered with data:', JSON.stringify(data, null, 2));
                });
                log('✅ showImportModal called successfully');
            } catch (error) {
                log('❌ Error calling showImportModal:', error.message);
            }
        }

        function testSampleImport() {
            log('🔍 Testing sample data import...');
            
            const sampleData = {
                cards: ['1234567890123456,12,25,123,12345'],
                pmEmails: ['<EMAIL>'],
                ueEmails2525: ['<EMAIL>'],
                ueEmails2515: [],
                ueEmails251: [],
                usedCards: [],
                usedEmails: [],
                allUsedCards: [],
                allUsedEmails: [],
                archivedCards: [],
                archivedEmails: [],
                combinations: []
            };
            
            const frame = document.getElementById('app-frame');
            const frameWindow = frame.contentWindow;
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found');
                return;
            }
            
            const dataManager = frameWindow.cardManagerApp.managers?.data;
            if (!dataManager) {
                log('❌ Data manager not found');
                return;
            }
            
            log('✅ Data manager found, importing sample data...');
            try {
                dataManager.importData(sampleData);
                log('✅ Sample data imported successfully!');
            } catch (error) {
                log('❌ Error importing sample data:', error.message);
            }
        }

        // Wait for frame to load
        document.getElementById('app-frame').onload = function() {
            log('✅ Card Manager application loaded in frame');
            
            // Wait a bit more for full initialization
            setTimeout(() => {
                const frame = document.getElementById('app-frame');
                const frameWindow = frame.contentWindow;
                
                if (frameWindow && frameWindow.cardManagerApp) {
                    log('✅ Card Manager app initialized and ready for testing');
                } else {
                    log('❌ Card Manager app not properly initialized');
                }
            }, 2000);
        };
    </script>
</body>
</html>
