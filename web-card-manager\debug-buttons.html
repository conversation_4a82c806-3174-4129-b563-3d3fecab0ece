<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Debug - Card Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
        }
        .test-button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #28a745; }
        .error { background: #dc3545; }
        .warning { background: #ffc107; color: black; }
        .info { background: #17a2b8; }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Card Manager Button Debug Tool</h1>
    
    <div class="debug-section">
        <h2>JavaScript Loading Test</h2>
        <div id="js-status" class="status info">Testing JavaScript loading...</div>
        <button class="test-button" onclick="testJSLoading()">Test JS Loading</button>
    </div>
    
    <div class="debug-section">
        <h2>DOM Element Test</h2>
        <div id="dom-status" class="status info">Testing DOM elements...</div>
        <button class="test-button" onclick="testDOMElements()">Test DOM Elements</button>
    </div>
    
    <div class="debug-section">
        <h2>Event Listener Test</h2>
        <div id="event-status" class="status info">Testing event listeners...</div>
        <button class="test-button" onclick="testEventListeners()">Test Event Listeners</button>
    </div>
    
    <div class="debug-section">
        <h2>Manager Initialization Test</h2>
        <div id="manager-status" class="status info">Testing manager initialization...</div>
        <button class="test-button" onclick="testManagers()">Test Managers</button>
    </div>
    
    <div class="debug-section">
        <h2>Console Output</h2>
        <div id="console-output"></div>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
    </div>
    
    <script type="module">
        // Console capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addToConsole('log', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            addToConsole('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            addToConsole('warn', ...args);
        };
        
        // Test functions
        window.testJSLoading = async function() {
            const statusEl = document.getElementById('js-status');
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status info';
            
            try {
                // Test module imports
                const { DataManager } = await import('./js/modules/DataManager.js');
                const { UIManager } = await import('./js/modules/UIManager.js');
                const { ModalManager } = await import('./js/modules/ModalManager.js');
                
                console.log('✅ All modules loaded successfully');
                statusEl.textContent = '✅ JavaScript modules loaded successfully';
                statusEl.className = 'status success';
            } catch (error) {
                console.error('❌ Failed to load modules:', error);
                statusEl.textContent = `❌ Failed to load modules: ${error.message}`;
                statusEl.className = 'status error';
            }
        };
        
        window.testDOMElements = function() {
            const statusEl = document.getElementById('dom-status');
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status info';

            // First test if we can access the main app iframe
            const mainFrame = parent.document.getElementById('main-app') ||
                             parent.document.querySelector('iframe') ||
                             window.parent.document.querySelector('iframe');

            if (mainFrame && mainFrame.contentDocument) {
                const doc = mainFrame.contentDocument;
                const elementsToTest = [
                    'bulk-add-cards-btn',
                    'bulk-add-emails-btn',
                    'settings-btn',
                    'import-btn',
                    'export-btn',
                    'undo-btn',
                    'redo-btn'
                ];

                const results = [];
                elementsToTest.forEach(id => {
                    const element = doc.getElementById(id);
                    if (element) {
                        results.push(`✅ ${id}: Found`);
                        // Test if element has event listeners
                        const hasListeners = element.onclick ||
                                           element.addEventListener ||
                                           element.hasAttribute('onclick');
                        results.push(`   Event listeners: ${hasListeners ? '✅' : '❌'}`);
                    } else {
                        results.push(`❌ ${id}: Not found`);
                    }
                });

                console.log('DOM Element Test Results:', results);
                statusEl.innerHTML = results.join('<br>');
                statusEl.className = 'status info';
            } else {
                // Test in current document
                const elementsToTest = [
                    'bulk-add-cards-btn',
                    'bulk-add-emails-btn',
                    'settings-btn',
                    'import-btn',
                    'export-btn',
                    'undo-btn',
                    'redo-btn'
                ];

                const results = [];
                elementsToTest.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        results.push(`✅ ${id}: Found`);
                    } else {
                        results.push(`❌ ${id}: Not found`);
                    }
                });

                console.log('DOM Element Test Results:', results);
                statusEl.innerHTML = results.join('<br>');
                statusEl.className = 'status info';
            }
        };
        
        window.testEventListeners = function() {
            const statusEl = document.getElementById('event-status');
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status info';
            
            // This is a basic test - in a real scenario we'd need to check if listeners are attached
            const testButton = document.createElement('button');
            testButton.textContent = 'Test Button';
            let clicked = false;
            
            testButton.addEventListener('click', () => {
                clicked = true;
            });
            
            testButton.click();
            
            if (clicked) {
                console.log('✅ Event listeners working');
                statusEl.textContent = '✅ Event listener mechanism working';
                statusEl.className = 'status success';
            } else {
                console.log('❌ Event listeners not working');
                statusEl.textContent = '❌ Event listener mechanism failed';
                statusEl.className = 'status error';
            }
        };
        
        window.testManagers = async function() {
            const statusEl = document.getElementById('manager-status');
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status info';
            
            try {
                if (window.cardManagerApp) {
                    console.log('✅ Card Manager App found');
                    console.log('App initialized:', window.cardManagerApp.isInitialized);
                    console.log('Available managers:', Object.keys(window.cardManagerApp.managers));
                    
                    statusEl.innerHTML = `
                        ✅ Card Manager App: Found<br>
                        Initialized: ${window.cardManagerApp.isInitialized}<br>
                        Managers: ${Object.keys(window.cardManagerApp.managers).join(', ')}
                    `;
                    statusEl.className = 'status success';
                } else {
                    console.log('❌ Card Manager App not found');
                    statusEl.textContent = '❌ Card Manager App not found';
                    statusEl.className = 'status error';
                }
            } catch (error) {
                console.error('❌ Manager test failed:', error);
                statusEl.textContent = `❌ Manager test failed: ${error.message}`;
                statusEl.className = 'status error';
            }
        };
        
        window.clearConsole = function() {
            consoleOutput.textContent = '';
        };
        
        // Auto-run tests on load
        setTimeout(() => {
            testJSLoading();
        }, 1000);
        
        setTimeout(() => {
            testManagers();
        }, 3000);
    </script>
</body>
</html>
