"""
Database module for the Card Manager application.
Handles saving and loading data to/from a JSON file.
"""
import json
import os

DEFAULT_DB_FILE = "card_manager_data.json"

def save_data(data_manager, filename=DEFAULT_DB_FILE):
    """Save the data manager state to a JSON file."""
    # Make sure all_used sets include all currently used items
    data_manager.all_used_cards.update(data_manager.used_cards)
    data_manager.all_used_emails.update(data_manager.used_emails)

    data = {
        "cards": data_manager.cards,
        "emails": data_manager.emails,
        "pm_emails": data_manager.pm_emails,
        "ue_emails_25_25": data_manager.ue_emails_25_25,
        "ue_emails_25_15": data_manager.ue_emails_25_15,
        "ue_emails_25_1": data_manager.ue_emails_25_1,
        "combinations": data_manager.combinations,
        "used_cards": list(data_manager.used_cards),
        "used_emails": list(data_manager.used_emails),
        "all_used_cards": list(data_manager.all_used_cards),
        "all_used_emails": list(data_manager.all_used_emails),
        "archived_cards": list(data_manager.archived_cards),
        "archived_emails": list(data_manager.archived_emails)
    }

    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"Data saved successfully to {filename}")
        print(f"Saved {len(data_manager.cards)} cards, {len(data_manager.emails)} emails, {len(data_manager.combinations)} combinations")
        print(f"Saved {len(data_manager.used_cards)} used cards, {len(data_manager.used_emails)} used emails")
        print(f"Saved {len(data_manager.all_used_cards)} all-used cards, {len(data_manager.all_used_emails)} all-used emails")
        print(f"Saved {len(data_manager.archived_cards)} archived cards, {len(data_manager.archived_emails)} archived emails")
        return True
    except Exception as e:
        print(f"Error saving data: {e}")
        return False

def load_data(data_manager, filename=DEFAULT_DB_FILE):
    """Load data from a JSON file into the data manager."""
    if not os.path.exists(filename):
        print(f"No data file found at {filename}. Starting with empty data.")
        return False

    try:
        with open(filename, 'r') as f:
            data = json.load(f)

        data_manager.cards = data.get("cards", [])
        data_manager.emails = data.get("emails", [])
        data_manager.pm_emails = data.get("pm_emails", [])
        data_manager.ue_emails_25_25 = data.get("ue_emails_25_25", [])
        data_manager.ue_emails_25_15 = data.get("ue_emails_25_15", [])
        data_manager.ue_emails_25_1 = data.get("ue_emails_25_1", [])
        data_manager.combinations = data.get("combinations", [])
        data_manager.used_cards = set(data.get("used_cards", []))
        data_manager.used_emails = set(data.get("used_emails", []))

        # Handle the case where the file doesn't have all_used sets (backward compatibility)
        if "all_used_cards" in data and "all_used_emails" in data:
            data_manager.all_used_cards = set(data.get("all_used_cards", []))
            data_manager.all_used_emails = set(data.get("all_used_emails", []))
        else:
            # Initialize all_used sets from used sets
            data_manager.all_used_cards = set(data_manager.used_cards)  # Copy used_cards
            data_manager.all_used_emails = set(data_manager.used_emails)  # Copy used_emails

        # Handle archived cards and emails (backward compatibility)
        if "archived_cards" in data and "archived_emails" in data:
            data_manager.archived_cards = set(data.get("archived_cards", []))
            data_manager.archived_emails = set(data.get("archived_emails", []))
        else:
            # Initialize empty archived sets
            data_manager.archived_cards = set()
            data_manager.archived_emails = set()

        # Make sure all_used sets include all currently used items
        data_manager.all_used_cards.update(data_manager.used_cards)
        data_manager.all_used_emails.update(data_manager.used_emails)

        print(f"Data loaded successfully from {filename}")
        print(f"Loaded {len(data_manager.cards)} cards, {len(data_manager.emails)} emails, {len(data_manager.combinations)} combinations")
        print(f"Loaded {len(data_manager.used_cards)} used cards, {len(data_manager.used_emails)} used emails")
        print(f"Loaded {len(data_manager.all_used_cards)} all-used cards, {len(data_manager.all_used_emails)} all-used emails")
        print(f"Loaded {len(data_manager.archived_cards)} archived cards, {len(data_manager.archived_emails)} archived emails")
        return True
    except Exception as e:
        print(f"Error loading data: {e}")
        return False
