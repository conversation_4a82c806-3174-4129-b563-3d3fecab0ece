<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Console - Card Manager</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        .console {
            background: #000;
            border: 2px solid #00ff00;
            border-radius: 8px;
            padding: 20px;
            height: 80vh;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #333;
        }
        .log-entry.error {
            color: #ff4444;
            border-left-color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
        }
        .log-entry.success {
            color: #44ff44;
            border-left-color: #44ff44;
        }
        .log-entry.warning {
            color: #ffaa44;
            border-left-color: #ffaa44;
        }
        .log-entry.info {
            color: #4444ff;
            border-left-color: #4444ff;
        }
        .timestamp {
            color: #888;
            font-size: 0.8em;
        }
        .clear-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #333;
            border-radius: 8px;
        }
        .test-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #005a99;
        }
    </style>
</head>
<body>
    <h1>🐛 Card Manager Debug Console</h1>
    
    <div class="test-controls">
        <button class="test-btn" onclick="testModuleImports()">Test Module Imports</button>
        <button class="test-btn" onclick="testManagerCreation()">Test Manager Creation</button>
        <button class="test-btn" onclick="testFullInitialization()">Test Full Initialization</button>
        <button class="test-btn" onclick="testNetworkRequests()">Test Network Requests</button>
        <button class="clear-btn" onclick="clearConsole()">Clear Console</button>
    </div>
    
    <div class="console" id="console"></div>

    <script type="module">
        const consoleDiv = document.getElementById('console');
        let logCount = 0;

        function log(message, type = 'info', details = null) {
            logCount++;
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            let content = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            
            if (details) {
                content += `<br><pre style="margin: 5px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 4px; font-size: 0.9em;">${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            entry.innerHTML = content;
            consoleDiv.appendChild(entry);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            // Also log to browser console
            console[type === 'error' ? 'error' : 'log'](`[${type.toUpperCase()}] ${message}`, details || '');
        }

        window.clearConsole = function() {
            consoleDiv.innerHTML = '';
            logCount = 0;
        };

        // Capture all console methods
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        console.log = (...args) => {
            log(args.join(' '), 'info');
            originalConsole.log(...args);
        };

        console.error = (...args) => {
            log(args.join(' '), 'error');
            originalConsole.error(...args);
        };

        console.warn = (...args) => {
            log(args.join(' '), 'warning');
            originalConsole.warn(...args);
        };

        // Capture unhandled errors
        window.addEventListener('error', (event) => {
            log(`❌ Unhandled Error: ${event.message}`, 'error', {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ Unhandled Promise Rejection: ${event.reason}`, 'error', {
                reason: event.reason,
                stack: event.reason?.stack
            });
        });

        // Test functions
        window.testModuleImports = async function() {
            log('🧪 Testing module imports...', 'info');
            
            const modules = [
                { name: 'DataManager', path: './js/modules/DataManager.js' },
                { name: 'UIManager', path: './js/modules/UIManager.js' },
                { name: 'ToastManager', path: './js/modules/ToastManager.js' },
                { name: 'ModalManager', path: './js/modules/ModalManager.js' },
                { name: 'SearchManager', path: './js/modules/SearchManager.js' },
                { name: 'HistoryManager', path: './js/modules/HistoryManager.js' },
                { name: 'ConfigManager', path: './js/modules/ConfigManager.js' },
                { name: 'AutoSaveManager', path: './js/modules/AutoSaveManager.js' },
                { name: 'Validation', path: './js/utils/validation.js' },
                { name: 'Helpers', path: './js/utils/helpers.js' }
            ];

            for (const module of modules) {
                try {
                    log(`Importing ${module.name}...`, 'info');
                    const imported = await import(module.path);
                    log(`✅ ${module.name} imported successfully`, 'success', Object.keys(imported));
                } catch (error) {
                    log(`❌ Failed to import ${module.name}`, 'error', {
                        path: module.path,
                        error: error.message,
                        stack: error.stack
                    });
                }
            }
        };

        window.testManagerCreation = async function() {
            log('🧪 Testing manager creation...', 'info');
            
            try {
                // Import all managers
                const { DataManager } = await import('./js/modules/DataManager.js');
                const { UIManager } = await import('./js/modules/UIManager.js');
                const { ToastManager } = await import('./js/modules/ToastManager.js');
                const { ConfigManager } = await import('./js/modules/ConfigManager.js');
                
                log('Creating ConfigManager...', 'info');
                const configManager = new ConfigManager();
                log('✅ ConfigManager created', 'success');
                
                log('Creating ToastManager...', 'info');
                const toastManager = new ToastManager();
                log('✅ ToastManager created', 'success');
                
                log('Creating DataManager...', 'info');
                const dataManager = new DataManager();
                log('✅ DataManager created', 'success');
                
                log('Creating UIManager...', 'info');
                const uiManager = new UIManager(dataManager, toastManager);
                log('✅ UIManager created', 'success');
                
                log('🎉 All managers created successfully!', 'success');
                
            } catch (error) {
                log('❌ Manager creation failed', 'error', {
                    error: error.message,
                    stack: error.stack
                });
            }
        };

        window.testFullInitialization = async function() {
            log('🧪 Testing full initialization...', 'info');
            
            try {
                log('Importing CardManagerApp...', 'info');
                const { CardManagerApp } = await import('./js/main.js');
                log('✅ CardManagerApp imported', 'success');
                
                log('Creating app instance...', 'info');
                const app = new CardManagerApp();
                log('✅ App instance created', 'success');
                
                log('Starting initialization...', 'info');
                await app.init();
                log('🎉 Full initialization completed successfully!', 'success');
                
            } catch (error) {
                log('❌ Full initialization failed', 'error', {
                    error: error.message,
                    stack: error.stack
                });
            }
        };

        window.testNetworkRequests = async function() {
            log('🧪 Testing network requests...', 'info');
            
            const files = [
                'js/main.js',
                'js/modules/DataManager.js',
                'js/modules/UIManager.js',
                'js/modules/ToastManager.js',
                'js/modules/ModalManager.js',
                'js/modules/SearchManager.js',
                'js/modules/HistoryManager.js',
                'js/modules/ConfigManager.js',
                'js/modules/AutoSaveManager.js',
                'js/utils/validation.js',
                'js/utils/helpers.js',
                'css/styles.css'
            ];

            for (const file of files) {
                try {
                    log(`Checking ${file}...`, 'info');
                    const response = await fetch(file);
                    if (response.ok) {
                        log(`✅ ${file} - Status: ${response.status}`, 'success');
                    } else {
                        log(`❌ ${file} - Status: ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${file} - Network error`, 'error', error.message);
                }
            }
        };

        // Auto-start basic tests
        log('🚀 Debug console initialized', 'success');
        log('Click the test buttons above to run diagnostics', 'info');
        
        // Run network test automatically
        setTimeout(() => {
            testNetworkRequests();
        }, 1000);
    </script>
</body>
</html>
