#!/usr/bin/env python3
"""
Enhanced Backup System for Card Manager
Creates multiple timestamped backups to prevent data loss.
"""

import json
import os
import shutil
from datetime import datetime

def create_enhanced_backup():
    """Create multiple timestamped backups of the data file."""
    
    data_file = "card_manager_data.json"
    backup_dir = "backups"
    
    # Create backup directory if it doesn't exist
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"Created backup directory: {backup_dir}")
    
    if not os.path.exists(data_file):
        print(f"No data file found: {data_file}")
        return False
    
    try:
        # Create timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create multiple backup copies
        backup_files = [
            f"{backup_dir}/card_manager_data_{timestamp}.json",
            f"{backup_dir}/card_manager_data_latest.json",
            f"card_manager_data_backup_{timestamp}.json"
        ]
        
        for backup_file in backup_files:
            shutil.copy2(data_file, backup_file)
            print(f"✅ Created backup: {backup_file}")
        
        # Keep only last 10 timestamped backups
        cleanup_old_backups(backup_dir)
        
        return True
        
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def cleanup_old_backups(backup_dir, keep_count=10):
    """Keep only the most recent backup files."""
    try:
        # Get all timestamped backup files
        backup_files = []
        for file in os.listdir(backup_dir):
            if file.startswith("card_manager_data_") and file.endswith(".json") and "latest" not in file:
                backup_files.append(os.path.join(backup_dir, file))
        
        # Sort by modification time (newest first)
        backup_files.sort(key=os.path.getmtime, reverse=True)
        
        # Remove old backups
        for old_backup in backup_files[keep_count:]:
            os.remove(old_backup)
            print(f"🗑️ Removed old backup: {old_backup}")
            
    except Exception as e:
        print(f"Error cleaning up backups: {e}")

def restore_from_backup():
    """List available backups and allow restoration."""
    backup_dir = "backups"
    
    if not os.path.exists(backup_dir):
        print("No backup directory found.")
        return False
    
    # List available backups
    backup_files = []
    for file in os.listdir(backup_dir):
        if file.startswith("card_manager_data_") and file.endswith(".json"):
            backup_path = os.path.join(backup_dir, file)
            backup_files.append((backup_path, os.path.getmtime(backup_path)))
    
    if not backup_files:
        print("No backup files found.")
        return False
    
    # Sort by modification time (newest first)
    backup_files.sort(key=lambda x: x[1], reverse=True)
    
    print("\nAvailable backups:")
    for i, (backup_path, mtime) in enumerate(backup_files, 1):
        timestamp = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
        print(f"{i}. {os.path.basename(backup_path)} ({timestamp})")
    
    return backup_files

if __name__ == "__main__":
    print("Enhanced Backup System")
    print("=" * 50)
    
    print("\n1. Creating backup...")
    create_enhanced_backup()
    
    print("\n2. Available backups:")
    backups = restore_from_backup()
    
    if backups:
        print(f"\n✅ Found {len(backups)} backup files")
    else:
        print("\n❌ No backups available")
