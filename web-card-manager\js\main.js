/**
 * Main Application Entry Point
 * Modern Card Manager Web Application
 */

import { DataManager } from './modules/DataManager.js';
import { UIManager } from './modules/UIManager.js';
import { ToastManager } from './modules/ToastManager.js';
import { ModalManager } from './modules/ModalManager.js';
import { SearchManager } from './modules/SearchManager.js';
import { HistoryManager } from './modules/HistoryManager.js';
import { ConfigManager } from './modules/ConfigManager.js';
import { AutoSaveManager } from './modules/AutoSaveManager.js';

/**
 * Main Application Class
 * Coordinates all managers and handles application lifecycle
 */
class CardManagerApp {
    constructor() {
        this.isInitialized = false;
        this.managers = {};
        
        // Performance tracking
        this.performanceStart = performance.now();
        
        // Bind methods
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing Card Manager...');

            // Show loading screen
            console.log('📺 Step 1: Showing loading screen...');
            this.showLoadingScreen();
            console.log('✅ Step 1 completed: Loading screen shown');

            // Initialize managers in dependency order
            console.log('📋 Step 2: Initializing managers...');
            await this.initializeManagers();
            console.log('✅ Step 2 completed: Managers initialized');

            // Set up event listeners
            console.log('👂 Step 3: Setting up event listeners...');
            this.setupEventListeners();
            console.log('✅ Step 3 completed: Event listeners set up');

            // Apply initial configuration
            console.log('⚙️ Step 4: Applying initial configuration...');
            await this.applyInitialConfig();
            console.log('✅ Step 4 completed: Configuration applied');

            // Load saved data
            console.log('💾 Step 5: Loading initial data...');
            await this.loadInitialData();
            console.log('✅ Step 5 completed: Data loaded');

            // Hide loading screen and show app
            console.log('🎨 Step 6: Showing application...');
            await this.showApplication();
            console.log('✅ Step 6 completed: Application shown');

            // Mark as initialized
            this.isInitialized = true;

            // Log performance
            const initTime = performance.now() - this.performanceStart;
            console.log(`🎉 Card Manager initialized successfully in ${initTime.toFixed(2)}ms`);

            // Show welcome message
            this.managers.toast.success('Welcome to Card Manager', 'Application loaded successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Card Manager:', error);
            console.error('Error details:', error);
            console.error('Error stack:', error.stack);
            this.handleError(error);
        }
    }

    /**
     * Initialize all managers
     */
    async initializeManagers() {
        // Core managers (no dependencies)
        this.managers.config = new ConfigManager();
        this.managers.toast = new ToastManager();

        // Data layer
        this.managers.data = new DataManager();

        // Business logic managers
        this.managers.search = new SearchManager(this.managers.data);
        this.managers.history = new HistoryManager(this.managers.data);
        this.managers.autoSave = new AutoSaveManager(this.managers.data, this.managers.config);

        // UI managers
        this.managers.modal = new ModalManager();
        this.managers.ui = new UIManager(this.managers.data, this.managers.toast);

        // Initialize all managers in dependency order
        await this.managers.config.init();
        await this.managers.toast.init();
        await this.managers.data.init();
        await this.managers.search.init();
        await this.managers.history.init();
        await this.managers.modal.init();
        await this.managers.ui.init();
        await this.managers.autoSave.init();

        // Connect managers
        this.connectManagers();

        // Ensure bulk operation listeners are properly setup after all connections
        console.log('🔧 Ensuring bulk operation listeners after manager connections...');
        if (this.managers.ui && typeof this.managers.ui.ensureBulkListenersSetup === 'function') {
            this.managers.ui.ensureBulkListenersSetup();
        }
    }

    /**
     * Connect managers with cross-dependencies
     */
    connectManagers() {
        // Connect UI to other managers
        this.managers.ui.setSearchManager(this.managers.search);
        this.managers.ui.setHistoryManager(this.managers.history);
        this.managers.ui.setModalManager(this.managers.modal);
        this.managers.ui.setConfigManager(this.managers.config);
        this.managers.ui.setToastManager(this.managers.toast);

        // Connect search to UI for highlighting
        this.managers.search.setUIManager(this.managers.ui);

        // Connect history to UI for button updates
        this.managers.history.setUIManager(this.managers.ui);

        // Connect auto-save to toast for notifications
        this.managers.autoSave.setToastManager(this.managers.toast);
    }

    /**
     * Set up global event listeners
     */
    setupEventListeners() {
        // Page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Before unload (save data)
        window.addEventListener('beforeunload', this.handleBeforeUnload);
        
        // Global error handling
        window.addEventListener('error', this.handleError);
        window.addEventListener('unhandledrejection', this.handleError);
        
        // Resize handling
        window.addEventListener('resize', this.debounce(() => {
            this.managers.ui?.handleResize();
        }, 250));
    }

    /**
     * Apply initial configuration
     */
    async applyInitialConfig() {
        // Apply theme from config
        this.managers.config.applyTheme();

        // Apply other initial settings
        this.managers.config.applyAnimationSettings();
        this.managers.config.applyCompactMode();
    }

    /**
     * Apply theme to the application
     */
    applyTheme(theme) {
        const root = document.documentElement;
        
        if (theme === 'auto') {
            // Use system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            theme = prefersDark ? 'dark' : 'light';
        }
        
        root.setAttribute('data-theme', theme);
        
        // Listen for system theme changes if auto theme is selected
        if (this.managers.config?.get('theme') === 'auto') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                root.setAttribute('data-theme', e.matches ? 'dark' : 'light');
            });
        }
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            await this.managers.data.loadData();
            this.managers.ui.updateAllCounts();
            this.managers.ui.refreshAllLists();
        } catch (error) {
            console.warn('Could not load saved data:', error);
            this.managers.toast.warning('Data Loading', 'Could not load previously saved data. Starting fresh.');
        }
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainApp = document.getElementById('main-app');
        
        if (loadingScreen) loadingScreen.classList.remove('hidden');
        if (mainApp) mainApp.classList.add('hidden');
    }

    /**
     * Show main application
     */
    async showApplication() {
        console.log('🎨 Showing application...');

        const loadingScreen = document.getElementById('loading-screen');
        const mainApp = document.getElementById('main-app');

        console.log('Loading screen element:', loadingScreen);
        console.log('Main app element:', mainApp);

        if (!loadingScreen) {
            console.error('❌ Loading screen element not found!');
            throw new Error('Loading screen element not found');
        }

        if (!mainApp) {
            console.error('❌ Main app element not found!');
            throw new Error('Main app element not found');
        }

        // Hide loading screen immediately
        console.log('Hiding loading screen...');
        loadingScreen.style.display = 'none';

        // Show main app immediately
        console.log('Showing main app...');
        mainApp.classList.remove('hidden');
        mainApp.style.display = 'flex';

        console.log('✅ Application shown successfully');

        // Small delay to ensure DOM updates
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    /**
     * Handle page visibility changes
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden - save current state
            this.managers.data?.saveData();
        } else {
            // Page is visible - restart auto-save if needed
            if (this.managers.autoSave && !this.managers.autoSave.isEnabled()) {
                this.managers.autoSave.enable();
            }
        }
    }

    /**
     * Handle before unload
     */
    handleBeforeUnload(event) {
        // Save data before leaving
        if (this.managers.data) {
            this.managers.data.saveData();
        }
        
        // Save configuration
        if (this.managers.config) {
            this.managers.config.saveConfig();
        }
    }

    /**
     * Handle errors
     */
    handleError(error) {
        console.error('Application Error:', error);
        
        if (this.managers.toast) {
            this.managers.toast.error(
                'Application Error',
                error.message || 'An unexpected error occurred'
            );
        }
        
        // Could implement error reporting here
    }

    /**
     * Get manager instance
     */
    getManager(name) {
        return this.managers[name];
    }

    /**
     * Utility: Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Cleanup resources
     */
    destroy() {
        // Remove event listeners
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
        window.removeEventListener('error', this.handleError);
        window.removeEventListener('unhandledrejection', this.handleError);
        
        // Cleanup managers
        Object.values(this.managers).forEach(manager => {
            if (typeof manager.destroy === 'function') {
                manager.destroy();
            }
        });
        
        this.isInitialized = false;
    }
}

// Initialize application when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('🌟 DOM Content Loaded - Starting Card Manager...');

        // Create global app instance
        window.cardManagerApp = new CardManagerApp();

        // Initialize the application
        await window.cardManagerApp.init();

        console.log('🎉 Card Manager fully loaded and ready!');

    } catch (error) {
        console.error('💥 Failed to initialize Card Manager:', error);
        console.error('Error stack:', error.stack);

        // Show error message to user
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div class="loading-spinner">
                    <div class="error-icon">❌</div>
                    <p style="color: #ff4444;">Failed to load Card Manager</p>
                    <p style="color: #888; font-size: 0.9em;">Please refresh the page or check the console for details</p>
                </div>
            `;
        }
    }
});

// Global error handlers
window.addEventListener('unhandledrejection', (event) => {
    console.error('🚨 Unhandled Promise Rejection:', event.reason);
    console.error('Promise:', event.promise);

    // Prevent the default browser behavior (logging to console)
    event.preventDefault();

    // Show user-friendly error if app is not initialized
    if (!window.cardManagerApp || !window.cardManagerApp.isInitialized) {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen && !loadingScreen.classList.contains('hidden')) {
            loadingScreen.innerHTML = `
                <div class="loading-spinner">
                    <div class="error-icon">⚠️</div>
                    <p style="color: #ff8c00;">Loading Error</p>
                    <p style="color: #888; font-size: 0.9em;">Please refresh the page</p>
                </div>
            `;
        }
    }
});

window.addEventListener('error', (event) => {
    console.error('🚨 Global Error:', event.error);
    console.error('Message:', event.message);
    console.error('Source:', event.filename, 'Line:', event.lineno);
});

// Export for module usage
export { CardManagerApp };
