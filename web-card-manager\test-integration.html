<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Integration Test - Card Formatting Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4f7cff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b5bdb;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #444;
            border-radius: 8px;
        }
        .side-by-side {
            display: flex;
            gap: 20px;
        }
        .side-by-side > div {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Integration Test - Card Formatting Fix</h1>
        
        <div class="test-section">
            <h2>📋 Test Console</h2>
            <div id="log" class="log">Starting integration test...\n</div>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h2>🎯 Integration Tests</h2>
            <button onclick="testCardAddition()">Test Card Addition</button>
            <button onclick="testBulkAddCards()">Test Bulk Add Cards</button>
            <button onclick="testDataPersistence()">Test Data Persistence</button>
            <button onclick="testCombinationCreation()">Test Combination Creation</button>
        </div>

        <div class="test-section side-by-side">
            <div>
                <h2>🧪 Test Results</h2>
                <iframe id="test-frame" src="test-card-formatting.html"></iframe>
            </div>
            <div>
                <h2>📱 Card Manager App</h2>
                <iframe id="app-frame" src="index.html"></iframe>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function getAppFrame() {
            const frame = document.getElementById('app-frame');
            return {
                frame,
                window: frame.contentWindow,
                document: frame.contentDocument || frame.contentWindow.document
            };
        }

        window.testCardAddition = function() {
            log('🔍 Testing individual card addition...');
            
            const { window: frameWindow } = getAppFrame();
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found');
                return;
            }
            
            const dataManager = frameWindow.cardManagerApp.managers?.data;
            if (!dataManager) {
                log('❌ Data manager not found');
                return;
            }
            
            const testCard = '****************,07,30,997,94112';
            log(`Adding test card: ${testCard}`);
            
            try {
                // Clear existing data first
                dataManager.clearAllData();
                
                // Add the test card
                const result = dataManager.addCard(testCard);
                
                log(`✅ Card added successfully: ${result}`);
                
                // Verify the year was preserved
                const parts = result.split(',');
                const year = parts[2];
                
                if (year === '30') {
                    log('✅ SUCCESS: Year preserved as 2-digit (30)');
                } else {
                    log(`❌ FAIL: Year was modified to: ${year}`);
                }
                
                // Check if it's in the data
                const cards = dataManager.getAvailableCards();
                log(`Available cards count: ${cards.length}`);
                if (cards.includes(result)) {
                    log('✅ Card found in available cards');
                } else {
                    log('❌ Card not found in available cards');
                }
                
            } catch (error) {
                log(`❌ Error adding card: ${error.message}`);
            }
        };

        window.testBulkAddCards = function() {
            log('📦 Testing bulk card addition...');
            
            const { window: frameWindow } = getAppFrame();
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found');
                return;
            }
            
            const dataManager = frameWindow.cardManagerApp.managers?.data;
            if (!dataManager) {
                log('❌ Data manager not found');
                return;
            }
            
            const testCards = [
                '****************,07,30,997,94112',
                '****************,12,25,456,90210',
                '1234567890123456,01,99,789,12345'
            ];
            
            log(`Adding ${testCards.length} test cards...`);
            
            try {
                // Clear existing data first
                dataManager.clearAllData();
                
                // Add cards one by one (simulating bulk add)
                const results = [];
                testCards.forEach((card, index) => {
                    try {
                        const result = dataManager.addCard(card);
                        results.push(result);
                        log(`${index + 1}. Added: ${result}`);
                    } catch (error) {
                        log(`${index + 1}. Error: ${error.message}`);
                    }
                });
                
                log(`\n📊 Results: ${results.length}/${testCards.length} cards added successfully`);
                
                // Verify year preservation
                let yearsPreserved = 0;
                testCards.forEach((original, index) => {
                    if (index < results.length) {
                        const originalYear = original.split(',')[2];
                        const resultYear = results[index].split(',')[2];
                        
                        if (originalYear === resultYear) {
                            yearsPreserved++;
                            log(`✅ Card ${index + 1}: Year preserved (${originalYear})`);
                        } else {
                            log(`❌ Card ${index + 1}: Year modified (${originalYear} → ${resultYear})`);
                        }
                    }
                });
                
                log(`\n🎯 Year preservation: ${yearsPreserved}/${testCards.length} cards preserved original year format`);
                
            } catch (error) {
                log(`❌ Error in bulk add test: ${error.message}`);
            }
        };

        window.testDataPersistence = function() {
            log('💾 Testing data persistence...');
            
            const { window: frameWindow } = getAppFrame();
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found');
                return;
            }
            
            const dataManager = frameWindow.cardManagerApp.managers?.data;
            if (!dataManager) {
                log('❌ Data manager not found');
                return;
            }
            
            const testCard = '****************,07,30,997,94112';
            
            try {
                // Clear and add test card
                dataManager.clearAllData();
                const addedCard = dataManager.addCard(testCard);
                log(`Added card: ${addedCard}`);
                
                // Force save
                dataManager.saveData();
                log('Data saved to localStorage');
                
                // Simulate reload by creating new data manager and loading
                const testDataManager = new frameWindow.DataManager();
                testDataManager.loadData();
                
                const loadedCards = testDataManager.getAvailableCards();
                log(`Loaded cards count: ${loadedCards.length}`);
                
                if (loadedCards.length > 0) {
                    const loadedCard = loadedCards[0];
                    log(`Loaded card: ${loadedCard}`);
                    
                    const loadedYear = loadedCard.split(',')[2];
                    if (loadedYear === '30') {
                        log('✅ SUCCESS: Year preserved after save/load (30)');
                    } else {
                        log(`❌ FAIL: Year was modified after save/load: ${loadedYear}`);
                    }
                } else {
                    log('❌ No cards found after loading');
                }
                
            } catch (error) {
                log(`❌ Error in persistence test: ${error.message}`);
            }
        };

        window.testCombinationCreation = function() {
            log('🔗 Testing combination creation...');
            
            const { window: frameWindow } = getAppFrame();
            
            if (!frameWindow || !frameWindow.cardManagerApp) {
                log('❌ Card Manager app not found');
                return;
            }
            
            const dataManager = frameWindow.cardManagerApp.managers?.data;
            if (!dataManager) {
                log('❌ Data manager not found');
                return;
            }
            
            const testCard = '****************,07,30,997,94112';
            const testEmail = '<EMAIL>';
            
            try {
                // Clear and add test data
                dataManager.clearAllData();
                dataManager.addCard(testCard);
                dataManager.addEmail(testEmail, 'pm');
                
                log(`Added card: ${testCard}`);
                log(`Added email: ${testEmail}`);
                
                // Create combination
                const combination = dataManager.createCombination(testCard, testEmail, 'pm');
                log(`Created combination: ${JSON.stringify(combination, null, 2)}`);
                
                // Check if year is preserved in combination
                const combCard = combination.card;
                const combYear = combCard.split(',')[2];
                
                if (combYear === '30') {
                    log('✅ SUCCESS: Year preserved in combination (30)');
                } else {
                    log(`❌ FAIL: Year was modified in combination: ${combYear}`);
                }
                
                // Check formatted outputs
                log(`Wool format: ${combination.woolFormat}`);
                log(`Fusion format: ${combination.fusionFormat}`);
                
                // Verify wool format preserves year
                const woolParts = combination.woolFormat.split(',');
                const woolYear = woolParts[1].split('/')[1]; // month/year format
                
                if (woolYear === '30') {
                    log('✅ SUCCESS: Year preserved in Wool format (30)');
                } else {
                    log(`❌ FAIL: Year was modified in Wool format: ${woolYear}`);
                }
                
            } catch (error) {
                log(`❌ Error in combination test: ${error.message}`);
            }
        };

        // Wait for frames to load
        document.getElementById('app-frame').onload = function() {
            log('✅ Card Manager application loaded');
            
            setTimeout(() => {
                const { window: frameWindow } = getAppFrame();
                if (frameWindow && frameWindow.cardManagerApp) {
                    log('✅ Card Manager app initialized and ready for testing');
                    log('🚀 You can now run the integration tests above');
                } else {
                    log('❌ Card Manager app not properly initialized');
                }
            }, 2000);
        };
    </script>
</body>
</html>
