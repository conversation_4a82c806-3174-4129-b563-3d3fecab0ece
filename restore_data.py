#!/usr/bin/env python3
"""
Data Restoration Script
Restores emails from convert_backup.txt to the card manager data file.
"""

import json
import os

def restore_data_from_emails():
    """Restore data by adding emails from convert_backup.txt to the data file."""
    
    # Read emails from convert_backup.txt
    emails = []
    try:
        with open("convert_backup.txt", 'r', encoding='utf-8') as f:
            for line in f:
                email = line.strip()
                if email and '@' in email:
                    emails.append(email)
        
        print(f"Found {len(emails)} emails to restore")
        
    except FileNotFoundError:
        print("convert_backup.txt not found!")
        return False
    
    # Create data structure
    data = {
        "cards": [],
        "emails": [],
        "pm_emails": emails,  # Put all emails in PM 30/35 section
        "ue_emails_25_25": [],
        "ue_emails_25_15": [],
        "ue_emails_25_1": [],
        "combinations": [],
        "used_cards": [],
        "used_emails": [],
        "all_used_cards": [],
        "all_used_emails": [],
        "archived_cards": [],
        "archived_emails": []
    }
    
    # Save to data file
    try:
        # Backup existing file if it exists
        if os.path.exists("card_manager_data.json"):
            os.rename("card_manager_data.json", "card_manager_data.json.old")
            print("Backed up existing data file to card_manager_data.json.old")

        # Write new data
        print(f"Writing data with {len(data['pm_emails'])} PM emails...")
        with open("card_manager_data.json", 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)

        # Verify the file was written
        with open("card_manager_data.json", 'r', encoding='utf-8') as f:
            verify_data = json.load(f)
            print(f"Verification: File contains {len(verify_data['pm_emails'])} PM emails")

        print(f"✅ Successfully restored {len(emails)} emails to card_manager_data.json")
        print("All emails have been added to the 'PM 30/35' section")
        
        # Display the emails that were restored
        print("\nRestored emails:")
        for i, email in enumerate(emails, 1):
            print(f"{i:2d}. {email}")
        
        return True
        
    except Exception as e:
        print(f"Error saving data: {e}")
        return False

if __name__ == "__main__":
    print("Data Restoration Script")
    print("=" * 50)
    
    success = restore_data_from_emails()
    
    if success:
        print("\n✅ Data restoration complete!")
        print("You can now run the card manager and your emails should be loaded.")
    else:
        print("\n❌ Data restoration failed.")
