#!/usr/bin/env python3
"""
Simple Email Extraction Script
Extracts all emails from convert_backup.txt and outputs them to convert.txt
"""

import re

def extract_emails():
    """Extract all emails from convert_backup.txt and save them to convert.txt."""
    
    input_file = "convert_backup.txt"
    output_file = "convert.txt"
    
    emails = []
    
    try:
        # Read the input file
        print(f"Reading {input_file}...")
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"File content length: {len(content)} characters")
        
        # Use regex to find all email patterns
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        found_emails = re.findall(email_pattern, content)
        
        print(f"Found {len(found_emails)} emails via regex")
        
        # Remove duplicates while preserving order
        unique_emails = []
        seen = set()
        for email in found_emails:
            if email not in seen:
                unique_emails.append(email)
                seen.add(email)
        
        print(f"Found {len(unique_emails)} unique emails")
        
        # Write the extracted emails to the output file
        print(f"Writing emails to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            for email in unique_emails:
                f.write(email + '\n')
        
        print(f"✅ Successfully wrote {len(unique_emails)} emails to {output_file}")
        print("\nExtracted emails:")
        for i, email in enumerate(unique_emails, 1):
            print(f"{i:2d}. {email}")
        
        return unique_emails
        
    except FileNotFoundError:
        print(f"Error: {input_file} not found!")
        return []
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return []

if __name__ == "__main__":
    print("Simple Email Extraction Script")
    print("=" * 50)
    
    extracted_emails = extract_emails()
    
    if extracted_emails:
        print(f"\n✅ Successfully extracted {len(extracted_emails)} unique emails!")
    else:
        print("\n❌ No emails were extracted.")
