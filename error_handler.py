"""
Enhanced error handling and data integrity management.

Provides comprehensive error handling, data validation, and recovery mechanisms
for the Card Manager application.
"""
import json
import os
import shutil
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('card_manager.log'),
        logging.StreamHandler()
    ]
)

class DataIntegrityError(Exception):
    """Custom exception for data integrity issues."""
    pass

class BackupManager:
    """Manages backup creation and restoration."""
    
    def __init__(self, data_file: str = "card_manager_data.json"):
        self.data_file = data_file
        self.backup_dir = "backups"
        self.max_backups = 10
        
        # Create backup directory if it doesn't exist
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, suffix: str = "") -> bool:
        """Create a timestamped backup of the data file."""
        try:
            if not os.path.exists(self.data_file):
                return True  # No file to backup
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"card_manager_data_{timestamp}{suffix}.json"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            shutil.copy2(self.data_file, backup_path)
            logging.info(f"Backup created: {backup_path}")
            
            # Clean up old backups
            self._cleanup_old_backups()
            return True
            
        except Exception as e:
            logging.error(f"Failed to create backup: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """Remove old backup files, keeping only the most recent ones."""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith("card_manager_data_") and file.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove old backups
            for file_path, _ in backup_files[self.max_backups:]:
                os.remove(file_path)
                logging.info(f"Removed old backup: {file_path}")
                
        except Exception as e:
            logging.error(f"Failed to cleanup old backups: {e}")
    
    def get_backup_list(self) -> List[Tuple[str, datetime]]:
        """Get list of available backups."""
        backups = []
        try:
            for file in os.listdir(self.backup_dir):
                if file.startswith("card_manager_data_") and file.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, file)
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    backups.append((file_path, mtime))
            
            # Sort by modification time (newest first)
            backups.sort(key=lambda x: x[1], reverse=True)
            
        except Exception as e:
            logging.error(f"Failed to get backup list: {e}")
        
        return backups
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore from a backup file."""
        try:
            if not os.path.exists(backup_path):
                logging.error(f"Backup file not found: {backup_path}")
                return False
            
            # Create a backup of current file before restoring
            self.create_backup("_before_restore")
            
            # Restore the backup
            shutil.copy2(backup_path, self.data_file)
            logging.info(f"Restored from backup: {backup_path}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to restore backup: {e}")
            return False

class DataValidator:
    """Validates data integrity and structure."""
    
    @staticmethod
    def validate_data_structure(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate the overall data structure."""
        errors = []
        
        # Required fields
        required_fields = [
            'cards', 'pm_emails', 'ue_emails_25_25', 'ue_emails_25_15', 'ue_emails_25_1',
            'combinations', 'used_cards', 'used_emails', 'all_used_cards', 'all_used_emails'
        ]
        
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
        
        # Validate field types
        list_fields = ['cards', 'pm_emails', 'ue_emails_25_25', 'ue_emails_25_15', 'ue_emails_25_1', 'combinations']
        for field in list_fields:
            if field in data and not isinstance(data[field], list):
                errors.append(f"Field {field} should be a list, got {type(data[field])}")
        
        set_fields = ['used_cards', 'used_emails', 'all_used_cards', 'all_used_emails']
        for field in set_fields:
            if field in data and not isinstance(data[field], (list, set)):
                errors.append(f"Field {field} should be a list or set, got {type(data[field])}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_combinations(combinations: List[Dict]) -> Tuple[bool, List[str]]:
        """Validate combinations data."""
        errors = []
        
        for i, combo in enumerate(combinations):
            if not isinstance(combo, dict):
                errors.append(f"Combination {i} should be a dictionary")
                continue
            
            required_combo_fields = ['card', 'email', 'formatted']
            for field in required_combo_fields:
                if field not in combo:
                    errors.append(f"Combination {i} missing field: {field}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def fix_data_inconsistencies(data: Dict[str, Any]) -> Dict[str, Any]:
        """Fix common data inconsistencies."""
        fixed_data = data.copy()
        
        # Convert sets to lists for JSON serialization
        set_fields = ['used_cards', 'used_emails', 'all_used_cards', 'all_used_emails', 'archived_cards', 'archived_emails']
        for field in set_fields:
            if field in fixed_data and isinstance(fixed_data[field], set):
                fixed_data[field] = list(fixed_data[field])
        
        # Ensure all_used sets include currently used items
        if 'used_cards' in fixed_data and 'all_used_cards' in fixed_data:
            all_used_cards = set(fixed_data.get('all_used_cards', []))
            used_cards = set(fixed_data.get('used_cards', []))
            all_used_cards.update(used_cards)
            fixed_data['all_used_cards'] = list(all_used_cards)
        
        if 'used_emails' in fixed_data and 'all_used_emails' in fixed_data:
            all_used_emails = set(fixed_data.get('all_used_emails', []))
            used_emails = set(fixed_data.get('used_emails', []))
            all_used_emails.update(used_emails)
            fixed_data['all_used_emails'] = list(all_used_emails)
        
        # Initialize missing optional fields
        optional_fields = {
            'archived_cards': [],
            'archived_emails': [],
            'emails': []  # Legacy field
        }
        
        for field, default_value in optional_fields.items():
            if field not in fixed_data:
                fixed_data[field] = default_value
        
        return fixed_data

class ErrorHandler:
    """Main error handler for the application."""
    
    def __init__(self, data_file: str = "card_manager_data.json"):
        self.backup_manager = BackupManager(data_file)
        self.validator = DataValidator()
        self.data_file = data_file
    
    def safe_load_data(self) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Safely load data with error handling and recovery."""
        try:
            # Try to load the main data file
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Validate data structure
                is_valid, errors = self.validator.validate_data_structure(data)
                if not is_valid:
                    logging.warning(f"Data validation errors: {errors}")
                    # Try to fix common issues
                    data = self.validator.fix_data_inconsistencies(data)
                    
                    # Re-validate
                    is_valid, errors = self.validator.validate_data_structure(data)
                    if not is_valid:
                        raise DataIntegrityError(f"Data integrity errors: {errors}")
                
                return True, data, None
            else:
                # No data file exists, return empty structure
                return True, self._get_empty_data_structure(), None
                
        except json.JSONDecodeError as e:
            logging.error(f"JSON decode error: {e}")
            return self._try_backup_recovery("JSON decode error")
        
        except DataIntegrityError as e:
            logging.error(f"Data integrity error: {e}")
            return self._try_backup_recovery(str(e))
        
        except Exception as e:
            logging.error(f"Unexpected error loading data: {e}")
            return self._try_backup_recovery(f"Unexpected error: {e}")
    
    def _try_backup_recovery(self, error_msg: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Try to recover from backup files."""
        backups = self.backup_manager.get_backup_list()
        
        for backup_path, backup_time in backups:
            try:
                logging.info(f"Trying to recover from backup: {backup_path}")
                with open(backup_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Validate backup data
                is_valid, errors = self.validator.validate_data_structure(data)
                if is_valid:
                    logging.info(f"Successfully recovered from backup: {backup_path}")
                    return True, data, f"Recovered from backup due to: {error_msg}"
                else:
                    logging.warning(f"Backup {backup_path} also has validation errors: {errors}")
                    
            except Exception as e:
                logging.error(f"Failed to load backup {backup_path}: {e}")
                continue
        
        # If all backups failed, return empty structure
        logging.warning("All recovery attempts failed, starting with empty data")
        return True, self._get_empty_data_structure(), f"Started with empty data due to: {error_msg}"
    
    def safe_save_data(self, data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Safely save data with backup and validation."""
        try:
            # Create backup before saving
            self.backup_manager.create_backup("_auto")
            
            # Validate data before saving
            is_valid, errors = self.validator.validate_data_structure(data)
            if not is_valid:
                # Try to fix issues
                data = self.validator.fix_data_inconsistencies(data)
                is_valid, errors = self.validator.validate_data_structure(data)
                if not is_valid:
                    return False, f"Data validation failed: {errors}"
            
            # Save data
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logging.info("Data saved successfully")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to save data: {e}"
            logging.error(error_msg)
            return False, error_msg
    
    def _get_empty_data_structure(self) -> Dict[str, Any]:
        """Get empty data structure with all required fields."""
        return {
            'cards': [],
            'emails': [],
            'pm_emails': [],
            'ue_emails_25_25': [],
            'ue_emails_25_15': [],
            'ue_emails_25_1': [],
            'combinations': [],
            'used_cards': [],
            'used_emails': [],
            'all_used_cards': [],
            'all_used_emails': [],
            'archived_cards': [],
            'archived_emails': []
        }
    
    def handle_exception(self, exception: Exception, context: str = "") -> str:
        """Handle and log exceptions with context."""
        error_msg = f"Error in {context}: {str(exception)}"
        logging.error(f"{error_msg}\n{traceback.format_exc()}")
        return error_msg
