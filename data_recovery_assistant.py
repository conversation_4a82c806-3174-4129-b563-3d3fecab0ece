#!/usr/bin/env python3
"""
Data Recovery Assistant
Helps rebuild card manager data step by step.
"""

import json
import os
from datetime import datetime

def create_fresh_data_structure():
    """Create a fresh data structure with proper initialization."""
    
    data = {
        "cards": [],
        "emails": [],
        "pm_emails": [],
        "ue_emails_25_25": [],
        "ue_emails_25_15": [],
        "ue_emails_25_1": [],
        "combinations": [],
        "used_cards": [],
        "used_emails": [],
        "all_used_cards": [],
        "all_used_emails": [],
        "archived_cards": [],
        "archived_emails": [],
        "metadata": {
            "created": datetime.now().isoformat(),
            "version": "2.0",
            "last_backup": None
        }
    }
    
    return data

def save_data_safely(data, filename="card_manager_data.json"):
    """Save data with backup creation."""
    
    # Create backup if file exists
    if os.path.exists(filename):
        backup_name = f"{filename}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.rename(filename, backup_name)
        print(f"Created backup: {backup_name}")
    
    # Save new data
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"✅ Data saved successfully to {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving data: {e}")
        return False

def add_sample_data():
    """Add some sample data to test the system."""
    
    data = create_fresh_data_structure()
    
    # Add sample cards
    sample_cards = [
        "1234567890123456,12,25,123,12345",
        "2345678901234567,01,26,456,23456",
        "3456789012345678,02,27,789,34567"
    ]
    
    # Add sample emails from convert_backup.txt if available
    sample_emails = []
    if os.path.exists("convert_backup.txt"):
        try:
            with open("convert_backup.txt", 'r', encoding='utf-8') as f:
                for line in f:
                    email = line.strip()
                    if email and '@' in email:
                        sample_emails.append(email)
            print(f"Loaded {len(sample_emails)} emails from convert_backup.txt")
        except Exception as e:
            print(f"Error reading convert_backup.txt: {e}")
    
    if not sample_emails:
        # Fallback sample emails
        sample_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
    
    # Add data to structure
    data["cards"] = sample_cards
    data["pm_emails"] = sample_emails[:10] if len(sample_emails) > 10 else sample_emails
    data["ue_emails_25_25"] = sample_emails[10:15] if len(sample_emails) > 15 else []
    
    return data

def interactive_recovery():
    """Interactive recovery process."""
    
    print("🔧 Data Recovery Assistant")
    print("=" * 50)
    
    print("\nWhat would you like to do?")
    print("1. Create fresh data structure")
    print("2. Add sample data for testing")
    print("3. Import emails from convert_backup.txt")
    print("4. Exit")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        data = create_fresh_data_structure()
        if save_data_safely(data):
            print("✅ Fresh data structure created successfully!")
        
    elif choice == "2":
        data = add_sample_data()
        if save_data_safely(data):
            print("✅ Sample data added successfully!")
            print(f"Added {len(data['cards'])} cards and {len(data['pm_emails'])} emails")
        
    elif choice == "3":
        if os.path.exists("convert_backup.txt"):
            data = create_fresh_data_structure()
            
            # Import emails
            try:
                with open("convert_backup.txt", 'r', encoding='utf-8') as f:
                    emails = []
                    for line in f:
                        email = line.strip()
                        if email and '@' in email:
                            emails.append(email)
                
                data["pm_emails"] = emails
                
                if save_data_safely(data):
                    print(f"✅ Imported {len(emails)} emails successfully!")
                    
            except Exception as e:
                print(f"❌ Error importing emails: {e}")
        else:
            print("❌ convert_backup.txt not found!")
    
    elif choice == "4":
        print("Goodbye!")
        return
    
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    interactive_recovery()
