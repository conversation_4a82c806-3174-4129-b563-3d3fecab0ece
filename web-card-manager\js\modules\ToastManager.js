/**
 * Toast Manager - Handles toast notifications
 */

import { createElement, animateElement } from '../utils/helpers.js';

export class ToastManager {
    constructor() {
        this.container = null;
        this.toasts = new Map();
        this.defaultDuration = 4000;
        this.maxToasts = 5;
    }

    /**
     * Initialize toast manager
     */
    async init() {
        console.log('🍞 Initializing Toast Manager...');
        
        // Create toast container if it doesn't exist
        this.container = document.getElementById('toast-container');
        if (!this.container) {
            this.container = createElement('div', {
                id: 'toast-container',
                className: 'toast-container'
            });
            document.body.appendChild(this.container);
        }
        
        console.log('✅ Toast Manager initialized');
    }

    /**
     * Show success toast
     */
    success(title, message, duration = this.defaultDuration) {
        return this.show('success', title, message, duration);
    }

    /**
     * Show error toast
     */
    error(title, message, duration = this.defaultDuration * 1.5) {
        return this.show('error', title, message, duration);
    }

    /**
     * Show warning toast
     */
    warning(title, message, duration = this.defaultDuration) {
        return this.show('warning', title, message, duration);
    }

    /**
     * Show info toast
     */
    info(title, message, duration = this.defaultDuration) {
        return this.show('info', title, message, duration);
    }

    /**
     * Show toast notification
     */
    show(type, title, message, duration = this.defaultDuration) {
        // Remove oldest toast if at max capacity
        if (this.toasts.size >= this.maxToasts) {
            const oldestId = this.toasts.keys().next().value;
            this.remove(oldestId);
        }

        const id = this.generateId();
        const toast = this.createToast(id, type, title, message, duration);
        
        this.container.appendChild(toast);
        this.toasts.set(id, {
            element: toast,
            timer: null,
            type,
            title,
            message,
            duration
        });

        // Animate in
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // Auto-remove after duration
        if (duration > 0) {
            const timer = setTimeout(() => {
                this.remove(id);
            }, duration);
            
            this.toasts.get(id).timer = timer;
        }

        return id;
    }

    /**
     * Create toast element
     */
    createToast(id, type, title, message, duration) {
        const toast = createElement('div', {
            className: `toast toast-${type}`,
            'data-toast-id': id
        });

        // Icon
        const icon = createElement('div', { className: 'toast-icon' });
        icon.innerHTML = this.getIcon(type);

        // Content
        const content = createElement('div', { className: 'toast-content' });
        
        const titleEl = createElement('div', { className: 'toast-title' }, title);
        content.appendChild(titleEl);
        
        if (message) {
            const messageEl = createElement('div', { className: 'toast-message' }, message);
            content.appendChild(messageEl);
        }

        // Close button
        const closeBtn = createElement('button', {
            className: 'toast-close',
            'aria-label': 'Close notification'
        });
        closeBtn.innerHTML = '×';
        closeBtn.addEventListener('click', () => {
            this.remove(id);
        });

        // Progress bar (if duration > 0)
        let progressBar = null;
        if (duration > 0) {
            progressBar = createElement('div', { className: 'toast-progress' });
            const progressFill = createElement('div', { className: 'toast-progress-fill' });
            progressBar.appendChild(progressFill);
            
            // Animate progress bar
            requestAnimationFrame(() => {
                progressFill.style.animationDuration = `${duration}ms`;
                progressFill.classList.add('animate');
            });
        }

        // Assemble toast
        toast.appendChild(icon);
        toast.appendChild(content);
        toast.appendChild(closeBtn);
        if (progressBar) {
            toast.appendChild(progressBar);
        }

        // Pause timer on hover
        if (duration > 0) {
            toast.addEventListener('mouseenter', () => {
                this.pauseTimer(id);
            });
            
            toast.addEventListener('mouseleave', () => {
                this.resumeTimer(id, duration);
            });
        }

        return toast;
    }

    /**
     * Get icon for toast type
     */
    getIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    /**
     * Remove toast
     */
    async remove(id) {
        const toastData = this.toasts.get(id);
        if (!toastData) return;

        const { element, timer } = toastData;

        // Clear timer
        if (timer) {
            clearTimeout(timer);
        }

        // Animate out
        element.classList.add('removing');
        
        try {
            await animateElement(element, 'slide-out-right');
        } catch (error) {
            // Animation failed, continue with removal
        }

        // Remove from DOM and map
        if (element.parentNode) {
            element.parentNode.removeChild(element);
        }
        this.toasts.delete(id);
    }

    /**
     * Pause toast timer
     */
    pauseTimer(id) {
        const toastData = this.toasts.get(id);
        if (!toastData || !toastData.timer) return;

        clearTimeout(toastData.timer);
        toastData.timer = null;

        // Pause progress bar animation
        const progressFill = toastData.element.querySelector('.toast-progress-fill');
        if (progressFill) {
            progressFill.style.animationPlayState = 'paused';
        }
    }

    /**
     * Resume toast timer
     */
    resumeTimer(id, remainingDuration) {
        const toastData = this.toasts.get(id);
        if (!toastData) return;

        // Resume progress bar animation
        const progressFill = toastData.element.querySelector('.toast-progress-fill');
        if (progressFill) {
            progressFill.style.animationPlayState = 'running';
        }

        // Set new timer
        const timer = setTimeout(() => {
            this.remove(id);
        }, remainingDuration);
        
        toastData.timer = timer;
    }

    /**
     * Clear all toasts
     */
    clearAll() {
        const toastIds = Array.from(this.toasts.keys());
        toastIds.forEach(id => this.remove(id));
    }

    /**
     * Clear toasts by type
     */
    clearByType(type) {
        const toastIds = Array.from(this.toasts.entries())
            .filter(([id, data]) => data.type === type)
            .map(([id]) => id);
        
        toastIds.forEach(id => this.remove(id));
    }

    /**
     * Update toast message
     */
    update(id, title, message) {
        const toastData = this.toasts.get(id);
        if (!toastData) return false;

        const titleEl = toastData.element.querySelector('.toast-title');
        const messageEl = toastData.element.querySelector('.toast-message');

        if (titleEl) {
            titleEl.textContent = title;
            toastData.title = title;
        }

        if (messageEl && message) {
            messageEl.textContent = message;
            toastData.message = message;
        } else if (message && !messageEl) {
            // Add message element if it doesn't exist
            const content = toastData.element.querySelector('.toast-content');
            const newMessageEl = createElement('div', { className: 'toast-message' }, message);
            content.appendChild(newMessageEl);
            toastData.message = message;
        }

        return true;
    }

    /**
     * Get toast count
     */
    getCount() {
        return this.toasts.size;
    }

    /**
     * Get toast count by type
     */
    getCountByType(type) {
        return Array.from(this.toasts.values())
            .filter(data => data.type === type).length;
    }

    /**
     * Check if toast exists
     */
    exists(id) {
        return this.toasts.has(id);
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Set default duration
     */
    setDefaultDuration(duration) {
        this.defaultDuration = duration;
    }

    /**
     * Set max toasts
     */
    setMaxToasts(max) {
        this.maxToasts = max;
        
        // Remove excess toasts if needed
        while (this.toasts.size > max) {
            const oldestId = this.toasts.keys().next().value;
            this.remove(oldestId);
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this.clearAll();
        
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.container = null;
        this.toasts.clear();
    }
}
