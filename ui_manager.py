"""
UI Manager for optimized widget handling and caching.

This module provides performance-optimized UI management with incremental updates
and widget caching to eliminate lag during combine/delete operations.
"""
from typing import Dict, List, Set, Optional, Any, Callable
import customtkinter as ctk
from dataclasses import dataclass, field
import weakref

@dataclass
class WidgetCache:
    """Cache for UI widgets to avoid recreation."""
    cards_widgets: Dict[str, ctk.CTkFrame] = field(default_factory=dict)
    emails_widgets: Dict[str, ctk.CTkFrame] = field(default_factory=dict)
    combinations_widgets: Dict[int, ctk.CTkFrame] = field(default_factory=dict)
    
    def clear_section(self, section: str):
        """Clear a specific section of the cache."""
        if section == "cards":
            self.cards_widgets.clear()
        elif section == "emails":
            self.emails_widgets.clear()
        elif section == "combinations":
            self.combinations_widgets.clear()

class UIManager:
    """Manages UI updates with performance optimization."""
    
    def __init__(self, app):
        self.app = weakref.ref(app)  # Weak reference to avoid circular references
        self.widget_cache = WidgetCache()
        self.last_data_state = {
            'cards': set(),
            'emails': set(),
            'combinations': [],
            'used_cards': set(),
            'used_emails': set(),
            'archived_cards': set(),
            'archived_emails': set()
        }
        
    def get_app(self):
        """Get the app instance safely."""
        app = self.app()
        if app is None:
            raise RuntimeError("Application instance has been garbage collected")
        return app

    def has_data_changed(self) -> bool:
        """Check if data has changed since last update."""
        try:
            app = self.get_app()
            current_state = {
                'cards': set(app.data_manager.cards),
                'emails': set(app.data_manager.emails),
                'combinations': app.data_manager.combinations.copy(),
                'used_cards': set(app.data_manager.used_cards),
                'used_emails': set(app.data_manager.used_emails),
                'archived_cards': set(app.data_manager.archived_cards),
                'archived_emails': set(app.data_manager.archived_emails)
            }

            return current_state != self.last_data_state
        except:
            return True  # Assume changed if we can't check

    def mark_data_changed(self):
        """Mark data as changed to force next update."""
        # Clear the last state to force update
        self.last_data_state = {
            'cards': set(),
            'emails': set(),
            'combinations': [],
            'used_cards': set(),
            'used_emails': set(),
            'archived_cards': set(),
            'archived_emails': set()
        }
    
    def update_cards_incremental(self, force_refresh: bool = False):
        """Update cards UI incrementally."""
        app = self.get_app()
        data_manager = app.data_manager
        
        # Get current state
        current_cards = set(data_manager.cards)
        current_used = set(data_manager.all_used_cards)
        current_archived = set(data_manager.archived_cards)
        
        # Check if we need to update
        if not force_refresh and (
            current_cards == self.last_data_state['cards'] and
            current_used == self.last_data_state['used_cards'] and
            current_archived == self.last_data_state['archived_cards']
        ):
            return  # No changes, skip update
        
        # Update available cards
        self._update_available_cards(current_cards, current_used, current_archived)
        
        # Update used cards
        self._update_used_cards(current_cards, current_used)
        
        # Update archived cards
        self._update_archived_cards(current_archived)
        
        # Update state tracking
        self.last_data_state['cards'] = current_cards.copy()
        self.last_data_state['used_cards'] = current_used.copy()
        self.last_data_state['archived_cards'] = current_archived.copy()
    
    def _update_available_cards(self, all_cards: Set[str], used_cards: Set[str], archived_cards: Set[str]):
        """Update available cards section."""
        app = self.get_app()
        
        # Calculate available cards
        available_cards = all_cards - used_cards - archived_cards
        
        # Remove widgets for cards that are no longer available
        for card in list(self.widget_cache.cards_widgets.keys()):
            if card not in available_cards:
                widget = self.widget_cache.cards_widgets.pop(card)
                widget.destroy()
        
        # Add widgets for new available cards
        for card in available_cards:
            if card not in self.widget_cache.cards_widgets:
                self._create_card_widget(card, app.card_list_frame, "available")
        
        # Update count label
        app.available_cards_label.configure(text=f"Available Cards ({len(available_cards)})")
    
    def _update_used_cards(self, all_cards: Set[str], used_cards: Set[str]):
        """Update used cards section."""
        app = self.get_app()
        
        # Get currently used cards that are not archived
        current_used = used_cards - app.data_manager.archived_cards
        
        # Clear and rebuild used cards (simpler for this section due to status changes)
        for widget in app.used_card_list_frame.winfo_children():
            widget.destroy()
        
        used_count = 0
        for card in all_cards:
            if card in used_cards and card not in app.data_manager.archived_cards:
                self._create_used_card_widget(card, app.used_card_list_frame)
                used_count += 1
        
        # Update count label
        app.used_cards_label.configure(text=f"Used Cards ({used_count})")
    
    def _update_archived_cards(self, archived_cards: Set[str]):
        """Update archived cards section."""
        app = self.get_app()
        
        # Clear and rebuild archived cards section
        for widget in app.archived_card_list_frame.winfo_children():
            widget.destroy()
        
        for card in archived_cards:
            self._create_archived_card_widget(card, app.archived_card_list_frame)
        
        # Update count label
        app.archived_cards_label.configure(text=f"Archived Cards ({len(archived_cards)})")
    
    def _create_card_widget(self, card: str, parent: ctk.CTkFrame, card_type: str):
        """Create a card widget and add it to cache."""
        app = self.get_app()
        
        card_frame = ctk.CTkFrame(parent)
        card_frame.pack(fill="x", pady=2)
        
        label = ctk.CTkLabel(card_frame, text=card, anchor="w")
        label.pack(side="left", fill="x", expand=True, padx=5)
        
        if card_type == "available":
            # Add remove button for available cards
            remove_btn = ctk.CTkButton(card_frame, text="Remove", width=80,
                                     command=lambda c=card: app.remove_card(c))
            remove_btn.pack(side="right", padx=5)
        
        # Cache the widget
        self.widget_cache.cards_widgets[card] = card_frame
    
    def _create_used_card_widget(self, card: str, parent: ctk.CTkFrame):
        """Create a used card widget."""
        app = self.get_app()
        
        card_frame = ctk.CTkFrame(parent)
        card_frame.pack(fill="x", pady=2)
        
        # Mark cards that are currently in use with an indicator
        if card in app.data_manager.used_cards:
            label = ctk.CTkLabel(card_frame, text=f"{card} (In Use)", anchor="w")
        else:
            label = ctk.CTkLabel(card_frame, text=card, anchor="w")
            # Add remove button for used cards that are not currently in use
            remove_btn = ctk.CTkButton(card_frame, text="Remove", width=80,
                                     command=lambda c=card: app.remove_card(c))
            remove_btn.pack(side="right", padx=5)
        
        label.pack(side="left", fill="x", expand=True, padx=5)
    
    def _create_archived_card_widget(self, card: str, parent: ctk.CTkFrame):
        """Create an archived card widget."""
        app = self.get_app()
        
        card_frame = ctk.CTkFrame(parent)
        card_frame.pack(fill="x", pady=2)
        
        label = ctk.CTkLabel(card_frame, text=card, anchor="w",
                           font=ctk.CTkFont(weight="bold"))
        label.pack(side="left", fill="x", expand=True, padx=5)
        
        # Add a button to use this card in a combination
        use_btn = ctk.CTkButton(card_frame, text="Use", width=60,
                              command=lambda c=card: app.select_card_for_combination(c))
        use_btn.pack(side="right", padx=5)
    
    def update_emails_incremental(self, force_refresh: bool = False):
        """Update emails UI incrementally."""
        app = self.get_app()
        data_manager = app.data_manager
        
        # For now, use a simpler approach for emails due to multiple types
        # This can be further optimized later
        self._rebuild_email_sections()
    
    def _rebuild_email_sections(self):
        """Rebuild email sections (to be optimized further)."""
        app = self.get_app()
        
        # Clear email list frames for all types
        email_frame_attrs = ['pm_emails_list_frame', 'ue_emails_25_25_list_frame',
                           'ue_emails_25_15_list_frame', 'ue_emails_25_1_list_frame']
        
        for frame_attr in email_frame_attrs:
            if hasattr(app, frame_attr):
                for widget in getattr(app, frame_attr).winfo_children():
                    widget.destroy()
        
        # Clear used and archived email frames
        for widget in app.used_email_list_frame.winfo_children():
            widget.destroy()
        
        for widget in app.archived_email_list_frame.winfo_children():
            widget.destroy()
        
        # Rebuild email sections
        self._rebuild_available_emails()
        self._rebuild_used_emails()
        self._rebuild_archived_emails()
    
    def _rebuild_available_emails(self):
        """Rebuild available emails sections."""
        app = self.get_app()
        
        email_types = [
            ("pm_emails", "PM emails 30 off 35"),
            ("ue_emails_25_25", "UE emails 25 off 25"),
            ("ue_emails_25_15", "UE emails 25 off 15"),
            ("ue_emails_25_1", "UE emails 25 off 1")
        ]
        
        for email_type, section_title in email_types:
            available_emails = app.data_manager.get_available_emails(email_type)
            
            # Update the count label for this email type
            label_attr = f"available_{email_type}_label"
            if hasattr(app, label_attr):
                getattr(app, label_attr).configure(text=f"{section_title} ({len(available_emails)})")
            
            # Update the list frame for this email type
            list_frame_attr = f"{email_type}_list_frame"
            if hasattr(app, list_frame_attr):
                list_frame = getattr(app, list_frame_attr)
                
                for email in available_emails:
                    self._create_email_widget(email, list_frame, "available")
    
    def _create_email_widget(self, email: str, parent: ctk.CTkFrame, email_type: str):
        """Create an email widget."""
        app = self.get_app()
        
        email_frame = ctk.CTkFrame(parent)
        email_frame.pack(fill="x", pady=2)
        
        label = ctk.CTkLabel(email_frame, text=email, anchor="w")
        label.pack(side="left", fill="x", expand=True, padx=5)
        
        if email_type == "available":
            # Add remove button for available emails
            remove_btn = ctk.CTkButton(email_frame, text="Remove", width=80,
                                     command=lambda e=email: app.remove_email(e))
            remove_btn.pack(side="right", padx=5)
    
    def _rebuild_used_emails(self):
        """Rebuild used emails section."""
        app = self.get_app()
        
        used_emails_count = 0
        for email_list in [app.data_manager.emails, app.data_manager.pm_emails,
                          app.data_manager.ue_emails_25_25, app.data_manager.ue_emails_25_15,
                          app.data_manager.ue_emails_25_1]:
            for email in email_list:
                if email in app.data_manager.all_used_emails and email not in app.data_manager.archived_emails:
                    self._create_used_email_widget(email, app.used_email_list_frame)
                    used_emails_count += 1
        
        # Update count label
        app.used_emails_label.configure(text=f"Used Emails ({used_emails_count})")
    
    def _create_used_email_widget(self, email: str, parent: ctk.CTkFrame):
        """Create a used email widget."""
        app = self.get_app()
        
        email_frame = ctk.CTkFrame(parent)
        email_frame.pack(fill="x", pady=2)
        
        # Mark emails that are currently in use with an indicator
        if email in app.data_manager.used_emails:
            label = ctk.CTkLabel(email_frame, text=f"{email} (In Use)", anchor="w")
        else:
            label = ctk.CTkLabel(email_frame, text=email, anchor="w")
            # Add remove button for used emails that are not currently in use
            remove_btn = ctk.CTkButton(email_frame, text="Remove", width=80,
                                     command=lambda e=email: app.remove_email(e))
            remove_btn.pack(side="right", padx=5)
        
        label.pack(side="left", fill="x", expand=True, padx=5)
    
    def _rebuild_archived_emails(self):
        """Rebuild archived emails section."""
        app = self.get_app()
        
        for email in app.data_manager.archived_emails:
            self._create_archived_email_widget(email, app.archived_email_list_frame)
        
        # Update count label
        app.archived_emails_label.configure(text=f"Archived Emails ({len(app.data_manager.archived_emails)})")
    
    def _create_archived_email_widget(self, email: str, parent: ctk.CTkFrame):
        """Create an archived email widget."""
        app = self.get_app()
        
        email_frame = ctk.CTkFrame(parent)
        email_frame.pack(fill="x", pady=2)
        
        label = ctk.CTkLabel(email_frame, text=email, anchor="w",
                           font=ctk.CTkFont(weight="bold"))
        label.pack(side="left", fill="x", expand=True, padx=5)
        
        # Add a button to use this email in a combination
        use_btn = ctk.CTkButton(email_frame, text="Use", width=60,
                              command=lambda e=email: app.select_email_for_combination(e))
        use_btn.pack(side="right", padx=5)

    def update_combinations_incremental(self, force_refresh: bool = False):
        """Update combinations UI incrementally."""
        app = self.get_app()
        data_manager = app.data_manager

        # Get current combinations
        current_combinations = data_manager.get_combinations()

        # Check if we need to update
        if not force_refresh and current_combinations == self.last_data_state['combinations']:
            return  # No changes, skip update

        # For combinations, we'll do a full rebuild for now since they change frequently
        # and the structure is complex. This can be optimized further if needed.
        self._rebuild_combinations_section(current_combinations)

        # Update state tracking
        self.last_data_state['combinations'] = current_combinations.copy()

    def _rebuild_combinations_section(self, combinations: List[Dict]):
        """Rebuild combinations section."""
        app = self.get_app()

        # Clear existing widgets
        for widget in app.combinations_list_frame.winfo_children():
            widget.destroy()

        # Update count label
        app.combinations_count_label.configure(text=f"All Combinations ({len(combinations)})")

        # Create combination widgets
        for i, combo in enumerate(combinations):
            self._create_combination_widget(combo, i, app.combinations_list_frame)

    def _create_combination_widget(self, combo: Dict, index: int, parent: ctk.CTkFrame):
        """Create a combination widget."""
        app = self.get_app()

        # Main frame for the combination
        combo_frame = ctk.CTkFrame(parent, fg_color=("#f0f0f0", "#2b2b2b"))
        combo_frame.pack(fill="x", pady=4, padx=8)

        # Top frame for combination text
        text_frame = ctk.CTkFrame(combo_frame, fg_color=combo_frame.cget("fg_color"))
        text_frame.pack(fill="x", padx=8, pady=(8, 4))

        # Display the combination with email type
        email_type = combo.get('email_type', 'Unknown')
        display_text = f"{combo['formatted']} ({email_type})"

        combo_label = ctk.CTkLabel(text_frame, text=display_text,
                                 font=ctk.CTkFont(size=14, weight="bold"),
                                 anchor="w")
        combo_label.pack(fill="x", padx=5, pady=2)

        # Bottom frame for buttons
        btn_frame = ctk.CTkFrame(combo_frame, fg_color=combo_frame.cget("fg_color"))
        btn_frame.pack(fill="x", padx=8, pady=(4, 8))

        # Parse the combination to get card and email parts
        card_parts = combo['card'].split(',')
        email = combo['email']

        # Format for different button types
        wool_format = f"{card_parts[0]},{card_parts[1]}/{card_parts[2]},{card_parts[3]},{card_parts[4]},{email}"
        fusion_format = f"{card_parts[0]},{card_parts[1]},{card_parts[2]},{card_parts[3]},{card_parts[4]},{email}"
        otp_format = email

        # Create buttons
        buttons = [
            ("Wool", wool_format, "Wool format"),
            ("Fusion", fusion_format, "Fusion format"),
            ("OTP", otp_format, "Email only"),
            ("Delete", None, None)
        ]

        for btn_text, format_text, format_name in buttons:
            if btn_text == "Delete":
                btn = ctk.CTkButton(btn_frame, text=btn_text, width=80, height=30,
                                  command=lambda idx=index: app.delete_combination(idx),
                                  font=ctk.CTkFont(size=12),
                                  fg_color="#D32F2F", hover_color="#B71C1C")
            else:
                btn = ctk.CTkButton(btn_frame, text=btn_text, width=80, height=30,
                                  command=lambda txt=format_text, name=format_name: app.copy_combination(txt, name),
                                  font=ctk.CTkFont(size=12))

            btn.pack(side="left", padx=6, pady=4, expand=True, fill="x")

    def update_comboboxes(self):
        """Update dropdown comboboxes with current data."""
        app = self.get_app()
        data_manager = app.data_manager

        # Get cards for manual selection (not currently in use)
        manual_selection_cards = []
        for card in data_manager.cards:
            if card not in data_manager.used_cards:
                manual_selection_cards.append(card)

        # Add archived cards to the selection pool
        manual_selection_cards.extend(list(data_manager.archived_cards))

        # Filter out any cards that are currently in use (double-check)
        manual_selection_cards = [card for card in manual_selection_cards
                                 if card not in data_manager.used_cards]

        # Update card combo
        app.card_combo.configure(values=manual_selection_cards)

        # Update email combo based on selected type
        if hasattr(app, 'email_type_combo'):
            app.on_email_type_changed(app.email_type_combo.get())

    def force_full_refresh(self):
        """Force a complete UI refresh."""
        self.widget_cache = WidgetCache()  # Clear cache
        self.last_data_state = {
            'cards': set(),
            'emails': set(),
            'combinations': [],
            'used_cards': set(),
            'used_emails': set(),
            'archived_cards': set(),
            'archived_emails': set()
        }

        # Update all sections
        self.update_cards_incremental(force_refresh=True)
        self.update_emails_incremental(force_refresh=True)
        self.update_combinations_incremental(force_refresh=True)
        self.update_comboboxes()

    def update_specific_sections(self, sections: List[str]):
        """Update only specific sections of the UI.

        Args:
            sections: List of sections to update ('cards', 'emails', 'combinations', 'comboboxes')
        """
        if 'cards' in sections:
            self.update_cards_incremental()

        if 'emails' in sections:
            self.update_emails_incremental()

        if 'combinations' in sections:
            self.update_combinations_incremental()

        if 'comboboxes' in sections:
            self.update_comboboxes()
