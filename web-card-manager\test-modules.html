<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .warning { background: #5a5a2d; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Module Loading Test</h1>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Console Output</h2>
            <div id="console-output"></div>
        </div>
    </div>

    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        const testResults = document.getElementById('test-results');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function addTestResult(status, message) {
            const div = document.createElement('div');
            div.className = `status ${status}`;
            div.textContent = message;
            testResults.appendChild(div);
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('WARN', ...args);
        };
        
        // Test module loading
        async function testModuleLoading() {
            console.log('🧪 Starting module loading tests...');
            
            try {
                // Test 1: Check if we can import main.js
                console.log('📦 Test 1: Importing main.js...');
                const mainModule = await import('./js/main.js');
                console.log('✅ main.js imported successfully:', mainModule);
                addTestResult('success', 'main.js imported successfully');
                
                // Test 2: Check CardManagerApp class
                if (mainModule.CardManagerApp) {
                    console.log('✅ CardManagerApp class found');
                    addTestResult('success', 'CardManagerApp class found');
                } else {
                    console.error('❌ CardManagerApp class not found in main.js');
                    addTestResult('error', 'CardManagerApp class not found');
                }
                
            } catch (error) {
                console.error('❌ Failed to import main.js:', error);
                addTestResult('error', `Failed to import main.js: ${error.message}`);
            }
            
            try {
                // Test 3: Check individual modules
                console.log('📦 Test 3: Testing individual modules...');
                
                const modules = [
                    'DataManager',
                    'UIManager', 
                    'ToastManager',
                    'ModalManager',
                    'SearchManager',
                    'HistoryManager',
                    'ConfigManager',
                    'AutoSaveManager'
                ];
                
                for (const moduleName of modules) {
                    try {
                        const module = await import(`./js/modules/${moduleName}.js`);
                        console.log(`✅ ${moduleName} imported successfully`);
                        addTestResult('success', `${moduleName} imported successfully`);
                    } catch (error) {
                        console.error(`❌ Failed to import ${moduleName}:`, error);
                        addTestResult('error', `Failed to import ${moduleName}: ${error.message}`);
                    }
                }
                
            } catch (error) {
                console.error('❌ Module testing failed:', error);
                addTestResult('error', `Module testing failed: ${error.message}`);
            }
            
            // Test 4: Check DOM elements
            console.log('🔍 Test 4: Checking DOM elements...');
            
            const elements = [
                'loading-screen',
                'main-app',
                'bulk-add-cards-btn',
                'bulk-add-emails-btn'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    console.log(`✅ Element found: ${id}`);
                    addTestResult('success', `Element found: ${id}`);
                } else {
                    console.warn(`⚠️ Element not found: ${id}`);
                    addTestResult('warning', `Element not found: ${id}`);
                }
            });
            
            // Test 5: Check global variables after delay
            setTimeout(() => {
                console.log('🔍 Test 5: Checking global variables...');
                
                if (window.cardManagerApp) {
                    console.log('✅ window.cardManagerApp found');
                    addTestResult('success', 'window.cardManagerApp found');
                    
                    if (window.cardManagerApp.isInitialized) {
                        console.log('✅ CardManagerApp is initialized');
                        addTestResult('success', 'CardManagerApp is initialized');
                    } else {
                        console.warn('⚠️ CardManagerApp not yet initialized');
                        addTestResult('warning', 'CardManagerApp not yet initialized');
                    }
                } else {
                    console.error('❌ window.cardManagerApp not found');
                    addTestResult('error', 'window.cardManagerApp not found');
                }
                
                if (window.CardManagerApp) {
                    console.log('✅ window.CardManagerApp found');
                    addTestResult('success', 'window.CardManagerApp found');
                } else {
                    console.warn('⚠️ window.CardManagerApp not found (this is expected)');
                    addTestResult('warning', 'window.CardManagerApp not found (expected)');
                }
            }, 3000);
        }
        
        // Start tests when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', testModuleLoading);
        } else {
            testModuleLoading();
        }
    </script>
    
    <!-- Load the main app HTML structure for DOM testing -->
    <div style="display: none;">
        <div id="loading-screen">Loading...</div>
        <div id="main-app">
            <button id="bulk-add-cards-btn">Bulk Add Cards</button>
            <button id="bulk-add-emails-btn">Bulk Add Emails</button>
        </div>
    </div>
</body>
</html>
