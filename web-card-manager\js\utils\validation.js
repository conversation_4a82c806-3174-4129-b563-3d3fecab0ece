/**
 * Validation utilities for cards and emails
 */

/**
 * Validate email format
 */
export function validateEmail(email) {
    if (!email || typeof email !== 'string') {
        return false;
    }

    email = email.trim();
    if (!email) {
        return false;
    }

    // Enhanced email validation
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    // Basic checks
    if (!email.includes('@') || !email.includes('.')) {
        return false;
    }

    // Check for multiple @ symbols
    if ((email.match(/@/g) || []).length !== 1) {
        return false;
    }

    // Check pattern
    if (!emailPattern.test(email)) {
        return false;
    }

    // Additional checks
    const [localPart, domain] = email.split('@');
    
    // Local part checks
    if (localPart.length === 0 || localPart.length > 64) {
        return false;
    }
    
    if (localPart.startsWith('.') || localPart.endsWith('.')) {
        return false;
    }
    
    if (localPart.includes('..')) {
        return false;
    }

    // Domain checks
    if (domain.length === 0 || domain.length > 253) {
        return false;
    }
    
    if (domain.startsWith('.') || domain.endsWith('.')) {
        return false;
    }
    
    if (domain.includes('..')) {
        return false;
    }

    return true;
}

/**
 * Validate card format
 * Expected format: card_number,month,year,cvv,zip
 */
export function validateCard(card) {
    if (!card || typeof card !== 'string') {
        return { isValid: false, error: 'Card data is empty or invalid' };
    }

    card = card.trim();
    if (!card) {
        return { isValid: false, error: 'Card data is empty' };
    }

    // Split by comma
    const parts = card.split(',');
    if (parts.length !== 5) {
        return { 
            isValid: false, 
            error: `Card must have exactly 5 comma-separated fields, got ${parts.length}` 
        };
    }

    const [cardNumber, month, year, cvv, zip] = parts.map(part => part.trim());

    // Validate card number
    if (!cardNumber) {
        return { isValid: false, error: 'Card number is required' };
    }
    
    if (!/^\d{13,19}$/.test(cardNumber)) {
        return { 
            isValid: false, 
            error: 'Card number must be 13-19 digits' 
        };
    }

    // Validate month
    if (!month) {
        return { isValid: false, error: 'Month is required' };
    }
    
    const monthNum = parseInt(month, 10);
    if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
        return { 
            isValid: false, 
            error: 'Month must be between 1 and 12' 
        };
    }

    // Validate year
    if (!year) {
        return { isValid: false, error: 'Year is required' };
    }
    
    const yearNum = parseInt(year, 10);
    const currentYear = new Date().getFullYear();
    
    if (isNaN(yearNum)) {
        return { isValid: false, error: 'Year must be a number' };
    }
    
    // Accept both 2-digit and 4-digit years
    let fullYear = yearNum;
    if (yearNum < 100) {
        // Convert 2-digit year to 4-digit
        fullYear = yearNum < 50 ? 2000 + yearNum : 1900 + yearNum;
    }
    
    if (fullYear < currentYear || fullYear > currentYear + 20) {
        return { 
            isValid: false, 
            error: `Year must be between ${currentYear} and ${currentYear + 20}` 
        };
    }

    // Validate CVV
    if (!cvv) {
        return { isValid: false, error: 'CVV is required' };
    }
    
    if (!/^\d{3,4}$/.test(cvv)) {
        return { 
            isValid: false, 
            error: 'CVV must be 3 or 4 digits' 
        };
    }

    // Validate ZIP
    if (!zip) {
        return { isValid: false, error: 'ZIP code is required' };
    }
    
    if (!/^\d{5}(\d{4})?$/.test(zip)) {
        return { 
            isValid: false, 
            error: 'ZIP code must be 5 or 9 digits' 
        };
    }

    return { isValid: true };
}

/**
 * Format card data consistently
 */
export function formatCard(card) {
    if (!card || typeof card !== 'string') {
        throw new Error('Invalid card data');
    }

    const parts = card.trim().split(',').map(part => part.trim());
    
    if (parts.length !== 5) {
        throw new Error('Card must have exactly 5 parts');
    }

    let [cardNumber, month, year, cvv, zip] = parts;

    // Normalize month (ensure 2 digits)
    month = month.padStart(2, '0');

    // Normalize year (ensure 4 digits)
    const yearNum = parseInt(year, 10);
    if (yearNum < 100) {
        year = (yearNum < 50 ? 2000 + yearNum : 1900 + yearNum).toString();
    }

    // Normalize ZIP (ensure 5 digits for basic ZIP)
    if (zip.length === 4) {
        zip = '0' + zip;
    }

    return `${cardNumber},${month},${year},${cvv},${zip}`;
}

/**
 * Parse bulk card entries
 */
export function parseBulkCards(text) {
    if (!text || typeof text !== 'string') {
        return [];
    }

    const lines = text.trim().split('\n');
    const cards = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        try {
            const validation = validateCard(line);
            if (validation.isValid) {
                cards.push(formatCard(line));
            } else {
                console.warn(`Invalid card on line ${i + 1}: ${validation.error}`);
            }
        } catch (error) {
            console.warn(`Error processing card on line ${i + 1}: ${error.message}`);
        }
    }

    return cards;
}

/**
 * Parse bulk email entries
 */
export function parseBulkEmails(text) {
    if (!text || typeof text !== 'string') {
        return [];
    }

    const lines = text.trim().split('\n');
    const emails = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        if (validateEmail(line)) {
            emails.push(line.toLowerCase());
        } else {
            console.warn(`Invalid email on line ${i + 1}: ${line}`);
        }
    }

    return emails;
}

/**
 * Validate import data structure
 */
export function validateImportData(data) {
    if (!data || typeof data !== 'object') {
        return { isValid: false, error: 'Data must be an object' };
    }

    // Check for required structure
    const requiredFields = [
        'cards', 'pmEmails', 'ueEmails2525', 'ueEmails2515', 'ueEmails251',
        'combinations'
    ];

    const missingFields = requiredFields.filter(field => !(field in data));
    if (missingFields.length > 0) {
        return { 
            isValid: false, 
            error: `Missing required fields: ${missingFields.join(', ')}` 
        };
    }

    // Validate data types
    const arrayFields = [
        'cards', 'pmEmails', 'ueEmails2525', 'ueEmails2515', 'ueEmails251',
        'usedCards', 'usedEmails', 'allUsedCards', 'allUsedEmails',
        'archivedCards', 'archivedEmails', 'combinations'
    ];

    for (const field of arrayFields) {
        if (data[field] && !Array.isArray(data[field])) {
            return { 
                isValid: false, 
                error: `Field '${field}' must be an array` 
            };
        }
    }

    // Validate cards
    if (data.cards) {
        for (let i = 0; i < data.cards.length; i++) {
            const validation = validateCard(data.cards[i]);
            if (!validation.isValid) {
                return { 
                    isValid: false, 
                    error: `Invalid card at index ${i}: ${validation.error}` 
                };
            }
        }
    }

    // Validate emails
    const emailFields = ['pmEmails', 'ueEmails2525', 'ueEmails2515', 'ueEmails251'];
    for (const field of emailFields) {
        if (data[field]) {
            for (let i = 0; i < data[field].length; i++) {
                if (!validateEmail(data[field][i])) {
                    return { 
                        isValid: false, 
                        error: `Invalid email in ${field} at index ${i}: ${data[field][i]}` 
                    };
                }
            }
        }
    }

    // Validate combinations
    if (data.combinations) {
        for (let i = 0; i < data.combinations.length; i++) {
            const combo = data.combinations[i];
            if (!combo.id || !combo.card || !combo.email || !combo.emailType) {
                return { 
                    isValid: false, 
                    error: `Invalid combination at index ${i}: missing required fields` 
                };
            }
        }
    }

    return { isValid: true };
}

/**
 * Sanitize text input
 */
export function sanitizeInput(input) {
    if (typeof input !== 'string') {
        return '';
    }
    
    return input
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/\s+/g, ' '); // Normalize whitespace
}

/**
 * Validate email type
 */
export function validateEmailType(type) {
    const validTypes = ['pm_emails', 'ue_emails_25_25', 'ue_emails_25_15', 'ue_emails_25_1'];
    return validTypes.includes(type);
}

/**
 * Get email type display name
 */
export function getEmailTypeDisplayName(type) {
    const displayNames = {
        'pm_emails': 'PM emails 30 off 35',
        'ue_emails_25_25': 'UE emails 25 off 25',
        'ue_emails_25_15': 'UE emails 25 off 15',
        'ue_emails_25_1': 'UE emails 25 off 1'
    };
    
    return displayNames[type] || type;
}

/**
 * Validate combination data
 */
export function validateCombination(combination) {
    if (!combination || typeof combination !== 'object') {
        return { isValid: false, error: 'Combination must be an object' };
    }

    if (!combination.card || !combination.email || !combination.emailType) {
        return { 
            isValid: false, 
            error: 'Combination must have card, email, and emailType' 
        };
    }

    const cardValidation = validateCard(combination.card);
    if (!cardValidation.isValid) {
        return { 
            isValid: false, 
            error: `Invalid card: ${cardValidation.error}` 
        };
    }

    if (!validateEmail(combination.email)) {
        return { 
            isValid: false, 
            error: 'Invalid email format' 
        };
    }

    if (!validateEmailType(combination.emailType)) {
        return { 
            isValid: false, 
            error: 'Invalid email type' 
        };
    }

    return { isValid: true };
}
