#!/usr/bin/env python3
"""
Email Extraction Script
Extracts all emails from convert.txt and outputs them in a clean format.
"""

import re
import ast

def extract_emails_from_convert_txt():
    """Extract all emails from convert.txt and save them back to the same file."""

    input_file = "convert.txt"
    output_file = "convert.txt"

    emails = []

    try:
        # Read the input file completely first
        print(f"Reading {input_file}...")
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()

        lines = content.strip().split('\n')
        print(f"Processing {len(lines)} lines from {input_file}...")

        # Extract emails from each line
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue

            try:
                # Try to parse the line as a Python literal (list)
                data = ast.literal_eval(line)

                # Check if it's a list and has at least one element
                if isinstance(data, list) and len(data) > 0:
                    # The first element should be the email
                    email = data[0]

                    # Validate that it looks like an email
                    if isinstance(email, str) and '@' in email:
                        emails.append(email)
                        print(f"Line {line_num}: Found email: {email}")
                    else:
                        print(f"Line {line_num}: First element is not a valid email: {email}")
                else:
                    print(f"Line {line_num}: Not a valid list format")

            except (ValueError, SyntaxError) as e:
                # If parsing fails, try regex as fallback
                print(f"Line {line_num}: Failed to parse as Python literal, trying regex...")

                # Use regex to find email patterns
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                found_emails = re.findall(email_pattern, line)

                for email in found_emails:
                    emails.append(email)
                    print(f"Line {line_num}: Found email via regex: {email}")

                if not found_emails:
                    print(f"Line {line_num}: No emails found - {str(e)}")

        # Remove duplicates while preserving order
        unique_emails = []
        seen = set()
        for email in emails:
            if email not in seen:
                unique_emails.append(email)
                seen.add(email)

        print(f"\nExtracted {len(emails)} total emails")
        print(f"Found {len(unique_emails)} unique emails")

        # Write the extracted emails to the output file (overwriting it completely)
        print(f"\nWriting emails to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            for email in unique_emails:
                f.write(email + '\n')

        print(f"✅ Successfully wrote {len(unique_emails)} emails to {output_file}")
        print("\nExtracted emails:")
        for i, email in enumerate(unique_emails, 1):
            print(f"{i:2d}. {email}")

        return unique_emails

    except FileNotFoundError:
        print(f"Error: {input_file} not found!")
        return []
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return []

if __name__ == "__main__":
    print("Email Extraction Script")
    print("=" * 50)

    extracted_emails = extract_emails_from_convert_txt()

    if extracted_emails:
        print(f"\n✅ Successfully extracted {len(extracted_emails)} unique emails!")
    else:
        print("\n❌ No emails were extracted.")
