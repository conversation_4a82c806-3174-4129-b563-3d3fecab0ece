<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Card Manager Test</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-overlay {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-width: 400px;
            z-index: 10000;
            border: 2px solid #4CAF50;
        }
        .debug-overlay.error {
            border-color: #f44336;
        }
        .debug-overlay h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        .debug-overlay.error h3 {
            color: #f44336;
        }
        .debug-log {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .debug-log p {
            margin: 2px 0;
            font-size: 11px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Loading Card Manager...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <!-- Minimal content for testing -->
            <header class="toolbar">
                <div class="toolbar-left">
                    <h1 class="app-title">
                        <i class="fas fa-credit-card"></i>
                        Card Manager
                    </h1>
                </div>
            </header>
            
            <main class="main-content">
                <div class="tabs">
                    <button class="tab-button active" data-tab="cards">
                        <i class="fas fa-credit-card"></i>
                        Cards
                        <span class="badge" id="cards-count">0</span>
                    </button>
                </div>
                
                <div class="tab-content active" id="cards-tab">
                    <div class="section">
                        <h3>Available Cards</h3>
                        <div class="list-container">
                            <div class="list" id="available-cards-list"></div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Debug Overlay -->
    <div id="debug-overlay" class="debug-overlay">
        <h3>🧪 Debug Status</h3>
        <div id="debug-status">Initializing...</div>
        <div class="debug-log" id="debug-log"></div>
    </div>

    <script type="module">
        const debugOverlay = document.getElementById('debug-overlay');
        const debugStatus = document.getElementById('debug-status');
        const debugLog = document.getElementById('debug-log');
        
        let hasError = false;
        
        function log(message, type = 'info') {
            const p = document.createElement('p');
            p.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            p.className = type;
            debugLog.appendChild(p);
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
            
            if (type === 'error') {
                hasError = true;
                debugOverlay.classList.add('error');
                debugStatus.textContent = '❌ Error Detected';
            }
        }

        function updateStatus(status) {
            debugStatus.textContent = status;
        }

        // Capture all console errors
        const originalError = console.error;
        console.error = function(...args) {
            log(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        // Capture unhandled errors
        window.addEventListener('error', (event) => {
            log(`Unhandled Error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`Unhandled Promise Rejection: ${event.reason}`, 'error');
        });

        async function runTest() {
            try {
                log('Starting comprehensive test...', 'info');
                updateStatus('🚀 Testing...');
                
                // Import and initialize the main app
                log('Importing CardManagerApp...', 'info');
                const { CardManagerApp } = await import('./js/main.js');
                log('✓ CardManagerApp imported successfully', 'success');
                
                log('Creating app instance...', 'info');
                const app = new CardManagerApp();
                log('✓ App instance created', 'success');
                
                log('Initializing application...', 'info');
                await app.init();
                log('✓ Application initialized successfully', 'success');
                
                // Wait a moment for any async operations
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (!hasError) {
                    updateStatus('✅ All Tests Passed!');
                    log('🎉 All tests completed successfully!', 'success');
                    debugOverlay.style.borderColor = '#4CAF50';
                } else {
                    updateStatus('❌ Tests Failed');
                    log('❌ Some tests failed - check errors above', 'error');
                }
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
                updateStatus('❌ Test Failed');
            }
        }

        // Run test when DOM is ready
        document.addEventListener('DOMContentLoaded', runTest);
    </script>
</body>
</html>
